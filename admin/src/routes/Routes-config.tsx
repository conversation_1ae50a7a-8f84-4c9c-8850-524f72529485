import { IRoutesConfig } from "../Interface/global.interface";
import { LoginPage } from "../pages";
// import { BankManagementIndex } from "../pages/bankManagement/bankManagementIndex";
import BookingDetails from "../pages/BookingManagement/BookingDetails";
import BookingManagement from "../pages/BookingManagement/BookingManagement";

import ReservationForm from "../pages/BookingManagement/components/ReservationForm";
import CustomerComplaintIndex from "../pages/CustomerComplaintBox/CustomerComplaintIndex";
import ExpensesIndex from "../pages/ExpensesManagement/expenses/ExpensesIndex";
import ExpenseCategoryIndex from "../pages/ExpensesManagement/ExpensesCategory/ExpenseCategoryIndex";
import TransactionAdjustment from "../pages/FinanceManagement/TransactionAdjustment/TransactionAdjustment";
import FoodOrderingIndex from "../pages/FoodOrdering/FoodOrderingIndex";
import RestaurantDashboardPage from "../pages/RestaurantManagement/RestaurantDashboard/RestaurantDashboardPage";
import RestaurantInventoryPage from "../pages/RestaurantManagement/RestaurantInventory/RestaurantInventoryPage";
import GuestDetails from "../pages/GuestManagement/components/GuestDetails";
import GuestIndex from "../pages/GuestManagement/GuestIndex";
import LostFound from "../pages/HouseKeeping/Lost&Found/LostFound";
import Maintenance from "../pages/HouseKeeping/Maintenance/Maintenance";
import RoomCleaning from "../pages/HouseKeeping/RoomCleaning/RoomCleaning";
import RoomInspection from "../pages/HouseKeeping/RoomInspection/RoomInspection";
import ServiceIndex from "../pages/HouseKeeping/ServiceTicketing/ServiceIndex";
import HousekeepingInventory from "../pages/HouseKeeping/Inventory/HousekeepingInventory";
import EmployeeManagementIndex from "../pages/HrManagement/AllEmployees/EmployeeManagementIndex";
import DepartmentIndex from "../pages/HrManagement/DepartmentIndex";
import DesignationIndex from "../pages/HrManagement/DesignationIndex";
import EmployeeConfigurationIndex from "../pages/HrManagement/EmployeeConfiguration/EmployeeConfigurationIndex";
import LeaveTrackingIndex from "../pages/HrManagement/LeaveTracking/LeaveTrackingIndex";
import Inventoryindex from "../pages/InventoryManagement/InventoryListing/InventoryIndex";
import InventoryPurchaseIndex from "../pages/InventoryManagement/Inventorypurchase/InventoryPurchaseIndex";
import ProductCategory from "../pages/InventoryManagement/ProductCategory/ProductCategory";
import ProductList from "../pages/InventoryManagement/productList/ProductList";
import { NotFoundPage } from "../pages/NotFoundPage";
import WakeupIndex from "../pages/WakeUpModule/WakeupIndex";
import { ActivityBooking } from "../pages/ActivityManagement/Activity-Booking/Activity-Booking";
import { ActivityList } from "../pages/ActivityManagement/Activity-List/ActivityList";
import CabBooking from "../pages/CabFacility/CabBooking/CabBooking";
import CabList from "../pages/CabFacility/CabList/CabList";
import LaundryCategory from "../pages/LaundryManagement/LaundryCategory/LaundryCategory";
import LaundryServices from "../pages/LaundryManagement/LaundryServices/LaundryServices";
import LaundryDashboard from "../pages/LaundryManagement/LaundryInventory/LaundryDashboard";
import { ExpenseReport } from "../pages/Reports/ExpenseReport/ExpenseReport";

import ChannelINdex from "../pages/ChannelManager/Channel/ChannelINdex";
import ChannelCategoryIndex from "../pages/ChannelManager/ChannelCategory/ChannelCategoryIndex";
import DashboardPage from "../pages/DashBoard/DashboardPage";
import IncomeCategoryIndex from "../pages/IncomeManagement/IncomeCategory/IncomeCategoryIndex";
import IncomeForm from "../pages/IncomeManagement/IncomeList/Components/IncomeForm";
import IncomeListIndex from "../pages/IncomeManagement/IncomeList/IncomeListIndex";
import IncomeTypeListIndex from "../pages/IncomeManagement/IncomeTypeList/IncomeTypeListIndex";
import GuestLaundryTabs from "../pages/LaundryManagement/Laundry/GuestLaundryTabs";
import { SubStorePage } from "../pages/StoreManagement/SubStore";
import StoreDashboard from "../pages/StoreManagement/StoreDashboard";
import MyCalendar from "../pages/Calendar/CalendarPage";
import UserProfile from "../components/UserProfile";
import EmployeeSheduleIndex from "../pages/HrManagement/EmployeeShedule/EmployeeSheduleIndex";
import EmployeeAttendanceIndex from "../pages/HrManagement/EmployeeAttendance/EmployeeAttendanceIndex";
import EmployeePayrollIndex from "../pages/HrManagement/EmployeePayroll/EmployeePayrollIndex";
import UserLogsIndex from "../pages/HrManagement/UserLogs/UserLogsIndex";
import PermissionIndex from "../pages/Permission/PermissionIndex";
import RoomManagementIndex from "../pages/RoomManagement/RoomManagementIndex";
import AttendanceIndex from "../pages/HrManagement/Attendance/AttendanceIndex";

import BedList from "../pages/HotelConfigurations/Bed/BedList";
import FloorPlan from "../pages/HotelConfigurations/Floor/FloorPlan";
import Amenities from "../pages/HotelConfigurations/Amenities/Amenitites";
import MembershipTiers from "../pages/HotelConfigurations/Discount Price";
import PriceManager from "../pages/HotelConfigurations/priceManager/PriceManager";
import RoomServicesIndex from "../pages/RoomManagement/RoomServices/RoomServicesIndex";
import BankIndex from "../pages/FinanceManagement/FinacnceCategory/BankIndex";
import RoomPackageIndex from "../pages/HotelConfigurations/Room/RoomPackage/RoomPackageIndex";
import RoomListIndex from "../pages/HotelConfigurations/Room/RoomList/RoomListIndex";
import RoomTypeIndex from "../pages/HotelConfigurations/Room/RoomList/RoomTypes/RoomTypeIndex";
import { GuestReportIndex } from "../pages/Reports/GuestReport/GuestReportIndex";
import HotelSetupPage from "../pages/HotelConfigurations/Setup/HotelSetupPage";

export const routesConfig: IRoutesConfig[] = [
  {
    path: "*",
    element: <NotFoundPage />,
  },
  {
    path: "/",
    element: <DashboardPage />,
  },
  {
    path: "/login",
    element: <LoginPage />,
  },
  {
    path: "/room-management",
    element: <RoomManagementIndex />,
  },

  {
    path: "/booking-management",
    element: <BookingManagement />,
  },
  {
    path: "/profile",
    element: <UserProfile />,
  },
  {
    path: "/booking-management",
    element: <BookingManagement />,
  },
  {
    path: "/booking-management/view/:id",
    element: <BookingDetails />,
  },
  {
    path: "/booking-management/reservation/:id",
    element: <ReservationForm />,
  },
  {
    path: "/booking-management/reservation/:id/:bookingId",
    element: <ReservationForm />,
  },
  {
    path: "/guest-management",
    element: <GuestIndex />,
  },
  {
    path: "/guest-management/guest-details/:id",
    element: <GuestDetails />,
  },
  // {
  //   path: "/house-keeping",
  //   element: <HouseIndex />,
  // },
  {
    path: "/service-ticketing",
    element: <ServiceIndex />,
  },
  {
    path: "/room-cleaning",
    element: <RoomCleaning />,
  },
  {
    path: "/room-inspection",
    element: <RoomInspection />,
  },
  {
    path: "/lost-found",
    element: <LostFound />,
  },
  {
    path: "/Maintenance",
    element: <Maintenance />,
  },
  {
    path: "/housekeeping-inventory",
    element: <HousekeepingInventory />,
  },
  {
    path: "/laundry-inventory",
    element: <LaundryDashboard />,
  },
  {
    path: "/wakeup-module",
    element: <WakeupIndex />,
  },
  {
    path: "/income-list",
    element: <IncomeListIndex />,
  },
  {
    path: "/income-management/income-form",
    element: <IncomeForm />,
  },
  {
    path: "/income-category",
    element: <IncomeCategoryIndex />,
  },
  {
    path: "/income-type-list",
    element: <IncomeTypeListIndex />,
  },

  {
    path: "/expense-management/category",
    element: <ExpenseCategoryIndex />,
  },
  {
    path: "/expense-management/expenses",
    element: <ExpensesIndex />,
  },
  // {
  //   path: "/employee-schedule",
  //   element: <ExpenseList />,
  // },
  // {
  //   path: "/employee-schedule",
  //   element: <ExpenseList />,
  // },

  {
    path: "/hr-management/employee-management",
    element: <EmployeeManagementIndex />,
  },

  {
    path: "/hr-management/employee-schedule",
    element: <EmployeeSheduleIndex />,
  },
  {
    path: "/hr-management/employee-attendance",
    element: <EmployeeAttendanceIndex />,
  },
  {
    path: "/hr-management/employee-payroll",
    element: <EmployeePayrollIndex />,
  },
  {
    path: "/hr-management/employee-attendance",
    element: <AttendanceIndex />,
  },
  {
    path: "/hr-management/department",
    element: <DepartmentIndex />,
  },
  {
    path: "/hr-management/designations",
    element: <DesignationIndex />,
  },
  // {
  //   path: "/hr-management",
  //   element: <HrIndex />,
  // },

  {
    path: "/customer-complain-box",
    element: <CustomerComplaintIndex />,
  },

  {
    path: "/hr-management/employee-configuration",
    element: <EmployeeConfigurationIndex />,
  },

  {
    path: "/hr-management/leave-tracking",
    element: <LeaveTrackingIndex />,
  },
  {
    path: "/user-logs",
    element: <UserLogsIndex />,
  },
  {
    path: "/inventory-management/inventory-list",
    element: <Inventoryindex />,
  },
  {
    path: "/availability-calendar",
    element: <MyCalendar />,
  },
  {
    path: "/hotel-configurations/room-type",
    element: <RoomTypeIndex />,
  },
  {
    path: "/hotel-config/room-services",
    element: <RoomServicesIndex />,
  },
  {
    path: "/hotel-config/room-list",
    element: <RoomListIndex />,
  },

  {
    path: "/store/main-store",
    element: <SubStorePage />,
  },
  {
    path: "/store/sub-store",
    element: <SubStorePage />,
  },
  {
    path: "/store/dashboard",
    element: <StoreDashboard />,
  },
  {
    path: "/inventory-management/product-category",
    element: <ProductCategory />,
  },
  {
    path: "/inventory-management/product-list",
    element: <ProductList />,
  },
  {
    path: "/inventory-management/purchase",
    element: <InventoryPurchaseIndex />,
  },
  {
    path: "/activity/activity-list",
    element: <ActivityList />,
  },
  {
    path: "/activity/activity-booking",
    element: <ActivityBooking />,
  },
  {
    path: "/channel",
    element: <ChannelINdex />,
  },
  {
    path: "channel-category",
    element: <ChannelCategoryIndex />,
  },
  {
    path: "/reports/guest-reports",
    element: <GuestReportIndex />,
  },
  {
    path: "/reports/expenses-reports",
    element: <ExpenseReport />,
  },
  {
    path: "/transaction-adjustment",
    element: <TransactionAdjustment />,
  },
  {
    path: "/Bank",
    element: <BankIndex />,
  },

  {
    path: "/hotel-config/bed-list",
    element: <BedList />,
  },

  {
    path: "/hotel-config/floor-plan",
    element: <FloorPlan />,
  },
  {
    path: "/hotel-config/room-facilities",
    element: <Amenities />,
  },

  {
    path: "/hotel-config/room-list",
    element: <RoomListIndex />,
  },

  {
    path: "hotel-config/membership-tiers",
    element: <MembershipTiers />,
  },

  {
    path: "/hotel-config/setup",
    element: <HotelSetupPage />,
  },

  {
    path: "hotel-config/room-package",
    element: <RoomPackageIndex />,
  },

  {
    path: "hotel-config/room-type",
    element: <RoomTypeIndex />,
  },

  {
    path: "hotel-config/price-manager",
    element: <PriceManager />,
  },

  //  {
  //   path: "/hotel-config/room-facilities",
  //   element:<RoomCleaningForm/>
  // },
  // {
  //   path: "/Bank",
  //   element: <BankManagementIndex />,
  // },
  {
    path: "/laundry",
    element: <GuestLaundryTabs />,
  },
  {
    path: "/laundry-category",
    element: <LaundryCategory />,
  },
  {
    path: "/laundry-services",
    element: <LaundryServices />,
  },
  {
    path: "/cab-list",
    element: <CabList />,
  },
  {
    path: "/cab-booking",
    element: <CabBooking />,
  },
  {
    path: "/restaurant/food-ordering",
    element: <FoodOrderingIndex />,
  },
  {
    path: "/restaurant/dashboard",
    element: <RestaurantDashboardPage />,
  },
  {
    path: "/restaurant/inventory",
    element: <RestaurantInventoryPage />,
  },
  {
    path: "/permissions",
    element: <PermissionIndex />,
  },
];
