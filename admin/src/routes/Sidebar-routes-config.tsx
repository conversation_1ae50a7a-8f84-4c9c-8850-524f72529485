import { ISidebarDashboardRoutes } from "../Interface/global.interface";
// import { FrontendRoutes } from './FrontendRoutes';
interface IconInterface {
  className?: string;
}

export const PlusIcon: React.FC<IconInterface> = ({ className }) => (
  <svg
    className={className || "w-4 h-4"}
    viewBox="0 0 20 20"
    fill="currentColor"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      fill="currentColor"
      d="M5 10H15M10 15V5"
      stroke="currentColor"
      strokeWidth="1.5"
      strokeMiterlimit="10"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const FoodOrderingIcon: React.FC<IconInterface> = ({
  className = "size-4",
}) => (
  <svg
    className={className}
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M18 8H19C20.0609 8 21.0783 8.42143 21.8284 9.17157C22.5786 9.92172 23 10.9391 23 12C23 13.0609 22.5786 14.0783 21.8284 14.8284C21.0783 15.5786 20.0609 16 19 16H18"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M2 8H18V17C18 18.0609 17.5786 19.0783 16.8284 19.8284C16.0783 20.5786 15.0609 21 14 21H6C4.93913 21 3.92172 20.5786 3.17157 19.8284C2.42143 19.0783 2 18.0609 2 17V8Z"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M6 1V4"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M10 1V4"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M14 1V4"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);
export const DashboardIcon: React.FC<IconInterface> = ({
  className = "size-4",
}) => (
  <svg
    className={className}
    viewBox="0 0 24 25"
    fill="currentColor"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M0 12.2807C0.130801 12.1298 0.261599 11.999 0.482955 12.0895C0.704311 12.1801 0.704322 12.3612 0.714384 12.5524C0.75853 14.7757 1.45426 16.9372 2.71534 18.7688C3.97641 20.6004 5.74737 22.0216 7.80859 22.8561C9.86981 23.6907 12.1306 23.9019 14.3108 23.4636C16.4909 23.0253 18.4945 21.9567 20.073 20.3904C20.1535 20.3199 20.2541 20.2696 20.3447 20.1992H20.2843C19.6404 20.1992 18.9964 20.2797 18.3525 20.2797C17.1502 20.282 15.955 20.0953 14.8108 19.7263C14.6538 19.6792 14.4915 19.6522 14.3278 19.6458H7.62673C7.38661 19.6701 7.14577 19.6072 6.94814 19.4687C6.75051 19.3301 6.60925 19.1252 6.55013 18.8912C6.53475 18.7642 6.53475 18.6358 6.55013 18.5089V12.8945H5.02077C4.88193 12.9045 4.74363 12.8686 4.62725 12.7922C4.51086 12.7158 4.42286 12.6032 4.37682 12.4719C4.32719 12.3458 4.31796 12.2073 4.3504 12.0758C4.38285 11.9442 4.45536 11.8259 4.55793 11.7374C5.16163 11.1739 5.75527 10.6105 6.36903 10.0571C6.44261 10.0003 6.49866 9.92391 6.53071 9.83668C6.56275 9.74945 6.56948 9.65494 6.55013 9.56405C6.28124 7.36905 6.62968 5.14256 7.5563 3.13465C7.79778 2.62151 8.0795 2.12848 8.36123 1.60528C8.19018 1.66565 8.01915 1.71596 7.86822 1.78639C6.05092 2.50069 4.45019 3.67454 3.22266 5.19308C1.99513 6.71162 1.18285 8.52283 0.865303 10.4495C0.865303 10.5803 0.865305 10.701 0.804935 10.8218C0.803414 10.8713 0.790805 10.92 0.768027 10.964C0.745249 11.0081 0.712887 11.0465 0.673315 11.0764C0.633744 11.1063 0.587957 11.127 0.539346 11.1369C0.490736 11.1468 0.440528 11.1457 0.392404 11.1337C0.345716 11.1253 0.301209 11.1076 0.261536 11.0816C0.221863 11.0556 0.187853 11.0218 0.161543 10.9824C0.135232 10.9429 0.117161 10.8985 0.108422 10.8519C0.099683 10.8053 0.100448 10.7574 0.110679 10.7111C0.243047 9.67238 0.517265 8.65675 0.92567 7.69258C1.65987 5.88229 2.83051 4.28174 4.33325 3.03355C5.836 1.78536 7.62417 0.9283 9.53844 0.538744C12.5291 -0.0645488 15.6372 0.486683 18.238 2.08161C20.8388 3.67655 22.7394 6.19692 23.5576 9.13602C24.3759 12.0751 24.0512 15.215 22.6487 17.9245C21.2463 20.6339 18.8702 22.712 15.998 23.7409C15.0399 24.075 14.0419 24.2813 13.0298 24.3547H12.8286H11.3194H11.1383C10.5748 24.2742 10.0114 24.2239 9.45796 24.1031C7.13694 23.5887 5.01943 22.4005 3.37092 20.6876C1.72241 18.9746 0.616263 16.8132 0.191174 14.4741C0.110681 14.0214 0.0603699 13.5686 0 13.1259V12.2807ZM7.18402 9.26219L7.44563 9.02072L11.3697 5.35828C11.5573 5.17693 11.808 5.07555 12.0689 5.07555C12.3299 5.07555 12.5806 5.17693 12.7682 5.35828L13.7744 6.26382C13.9756 6.45499 13.9958 6.65623 13.8348 6.81721C13.6738 6.9782 13.4927 6.95808 13.2914 6.81721L12.3758 5.97204C12.1545 5.76074 12.0639 5.76074 11.8426 5.97204L5.27231 12.0895C5.27231 12.0895 5.20188 12.1801 5.13145 12.2505H6.73125C7.15384 12.2505 7.25446 12.3511 7.25446 12.7838V18.4887C7.25446 18.9013 7.31483 18.9616 7.72736 18.9616H16.491C16.9136 18.9616 16.974 18.9013 16.974 18.4787V12.6731C16.974 12.3109 17.0847 12.1901 17.4469 12.1901H19.0568C19.0254 12.1052 18.9694 12.0316 18.8958 11.9788L14.6699 8.0548C14.6149 8.01314 14.5643 7.96594 14.519 7.91394C14.4626 7.84949 14.4317 7.76672 14.4319 7.68112C14.4322 7.59551 14.4636 7.51293 14.5203 7.44881C14.577 7.3847 14.6552 7.34344 14.7401 7.33275C14.825 7.32206 14.911 7.34267 14.9818 7.39073C15.0335 7.42896 15.0807 7.47283 15.1227 7.52153L19.2278 11.345C19.3888 11.4959 19.5599 11.6468 19.7208 11.8179C19.8011 11.9042 19.8544 12.0121 19.8741 12.1283C19.8937 12.2445 19.8789 12.364 19.8315 12.4719C19.7913 12.5882 19.717 12.6897 19.6183 12.7633C19.5196 12.8369 19.4011 12.879 19.2781 12.8844C19.1274 12.8944 18.9761 12.8944 18.8254 12.8844H17.6884V18.0963C17.6884 18.5893 17.7488 19.1025 17.2054 19.4546C18.4958 19.6085 19.8032 19.537 21.0691 19.2434C21.1295 19.2434 21.1798 19.1629 21.22 19.1126C22.6109 17.2535 23.3988 15.0129 23.4775 12.6925C23.5563 10.3721 22.9221 8.08322 21.6604 6.13419C20.3986 4.18517 18.57 2.66956 16.4207 1.79146C14.2714 0.913364 11.9046 0.714951 9.63906 1.22294C9.51322 1.25391 9.4026 1.32885 9.32716 1.43423C8.17864 2.95856 7.44384 4.754 7.19409 6.64617C7.07967 7.50747 7.07627 8.37991 7.18402 9.24207V9.26219Z"
      fill="currentColor"
    />
    <path
      d="M13.5928 11.979H14.7298C15.2731 11.979 15.5146 12.2305 15.5146 12.7738C15.5146 13.951 15.5146 15.1182 15.5146 16.2853C15.5296 16.3933 15.5194 16.5032 15.4848 16.6065C15.4502 16.7098 15.3921 16.8036 15.315 16.8807C15.238 16.9577 15.1441 17.0158 15.0408 17.0505C14.9375 17.0851 14.8276 17.0953 14.7197 17.0802H9.41721C9.30931 17.0953 9.19938 17.0851 9.09608 17.0505C8.99278 17.0158 8.89891 16.9577 8.82188 16.8807C8.74484 16.8036 8.68673 16.7098 8.6521 16.6065C8.61748 16.5032 8.6073 16.3933 8.62235 16.2853V12.7436C8.60713 12.6397 8.6164 12.5336 8.64945 12.4339C8.68249 12.3342 8.7384 12.2436 8.81268 12.1693C8.88696 12.095 8.97758 12.0391 9.0773 12.0061C9.17702 11.973 9.28308 11.9637 9.38703 11.979H10.5642V11.3451C10.5533 11.2583 10.562 11.1702 10.5897 11.0873C10.6174 11.0043 10.6633 10.9287 10.7242 10.8659C10.785 10.8031 10.8592 10.7548 10.9413 10.7245C11.0233 10.6943 11.1111 10.6828 11.1981 10.6911H12.969C13.0519 10.6835 13.1355 10.6941 13.2139 10.7222C13.2924 10.7502 13.3637 10.795 13.4231 10.8534C13.4825 10.9118 13.5284 10.9825 13.5577 11.0604C13.587 11.1384 13.599 11.2218 13.5928 11.3048V11.979ZM9.33672 16.3558H14.8002V14.6252H12.4558C12.3955 15.1182 12.325 15.249 12.0936 15.249C11.671 15.249 11.7515 14.8868 11.7113 14.6151H9.33672V16.3558ZM14.8002 12.7034H9.34679V13.8806H11.7012C11.7716 13.4178 11.8521 13.287 12.0835 13.297C12.315 13.3071 12.3955 13.4278 12.4558 13.8806H14.8002V12.7034ZM12.8784 11.4558H11.2887V12.0091H12.8784V11.4558Z"
      fill="currentColor"
    />
  </svg>
);
export const BookingsIcon: React.FC<IconInterface> = ({
  className = "size-4",
}) => (
  <svg
    className={className}
    viewBox="0 0 25 16"
    fill="currentColor"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M0.279999 15.5217V15.2017C0.279999 10.8217 0.279999 6.44166 0.279999 2.05166C0.400576 1.4146 0.301885 0.755476 0 0.181666H17.75C17.8369 0.168715 17.9256 0.188004 17.9993 0.235896C18.0729 0.283788 18.1266 0.35697 18.15 0.441661C20.0767 5.39499 22.0167 10.345 23.97 15.2917C23.97 15.3617 23.97 15.4417 24.06 15.5317L0.279999 15.5217ZM22.56 14.5217C22.56 14.4117 22.5 14.3417 22.47 14.2717C20.79 9.99166 19.1233 5.71833 17.47 1.45166C17.4531 1.37201 17.4064 1.30181 17.3395 1.25536C17.2726 1.20891 17.1906 1.1897 17.11 1.20166H1.59L1.69 1.43167C3.51 5.69833 5.33333 9.96499 7.16 14.2317C7.18546 14.3205 7.24166 14.3975 7.31859 14.4488C7.39552 14.5001 7.48816 14.5223 7.58 14.5117H22.58L22.56 14.5217ZM6.17 14.5217L1.32 3.19166V14.5017L6.17 14.5217Z"
      fill="currentColor"
    />
    <path
      d="M16.9305 9.12173C16.9324 9.93306 16.6937 10.7267 16.2443 11.4023C15.795 12.0778 15.1554 12.6049 14.4064 12.9168C13.6574 13.2286 12.8327 13.3112 12.0367 13.1542C11.2408 12.9971 10.5093 12.6074 9.93488 12.0344C9.36048 11.4614 8.969 10.7309 8.80999 9.9353C8.65097 9.1397 8.73158 8.31482 9.04161 7.56506C9.35163 6.8153 9.87713 6.17437 10.5516 5.72341C11.226 5.27245 12.0191 5.03174 12.8305 5.03174C13.9145 5.03697 14.9528 5.46925 15.7203 6.23486C16.4878 7.00046 16.9226 8.0377 16.9305 9.12173ZM12.8205 6.12173C12.0062 6.12173 11.2254 6.44518 10.6496 7.02092C10.0739 7.59666 9.75046 8.37752 9.75046 9.19173C9.75046 10.0059 10.0739 10.7868 10.6496 11.3626C11.2254 11.9383 12.0062 12.2617 12.8205 12.2617C13.6374 12.2565 14.4193 11.9289 14.9961 11.3503C15.5728 10.7716 15.8978 9.98872 15.9005 9.17174C15.9058 8.76485 15.8299 8.36099 15.6773 7.98378C15.5246 7.60657 15.2983 7.2636 15.0115 6.97494C14.7247 6.68627 14.3832 6.45771 14.0069 6.30262C13.6307 6.14753 13.2274 6.06904 12.8205 6.07174V6.12173Z"
      fill="currentColor"
    />
    <path
      d="M17.1691 2.7417V3.7417H3.36914V2.7417H17.1691Z"
      fill="currentColor"
    />
    <path
      d="M10.9707 9.2615L11.7007 8.5615L12.3207 9.3015L13.9907 7.5415L14.7307 8.2515L12.4007 10.7415L10.9707 9.2615Z"
      fill="currentColor"
    />
  </svg>
);
export const GuestIcon: React.FC<IconInterface> = ({
  className = "size-4",
}) => (
  <svg
    className={className}
    viewBox="0 0 24 25"
    fill="currentColor"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M0.703839 23.7244V16.1662C0.703839 15.7491 0.80543 15.6576 1.22198 15.6576H2.7256V14.2741C2.7256 13.8265 2.80688 13.7451 3.2539 13.7451H6.99266C7.38889 13.7451 7.49047 13.8367 7.49047 14.2537V15.6474H8.94331C9.42081 15.6474 9.50208 15.7288 9.50208 16.1865V23.7143C9.64431 23.7143 9.76623 23.7143 9.8983 23.7143C9.97849 23.7282 10.0512 23.77 10.1036 23.8323C10.156 23.8947 10.1847 23.9735 10.1847 24.055C10.1847 24.1365 10.156 24.2154 10.1036 24.2777C10.0512 24.3401 9.97849 24.3819 9.8983 24.3958H0.520958C0.439975 24.4056 0.358119 24.4056 0.277136 24.3958C0.199495 24.3821 0.129155 24.3415 0.0784731 24.281C0.0277912 24.2206 0 24.1441 0 24.0652C0 23.9863 0.0277912 23.9099 0.0784731 23.8494C0.129155 23.7889 0.199495 23.7483 0.277136 23.7346C0.418925 23.7207 0.561549 23.7173 0.703839 23.7244ZM8.83155 23.7244V16.3595H1.42517V23.7041H2.60369V21.4254C2.60369 21.0999 2.71545 20.988 3.02024 20.9778H7.27713C7.57176 20.9778 7.68351 21.1101 7.68351 21.4051C7.68351 22.0866 7.68351 22.7682 7.68351 23.4396V23.7041L8.83155 23.7244ZM3.44694 14.4369V15.6576H6.80978V14.4267H3.44694V14.4369ZM4.7677 23.7244V21.6899H3.28438V23.7244H4.7677ZM5.48902 21.6899V23.7244H6.97234V21.6899H5.48902Z"
      fill="currentColor"
    />
    <path
      d="M8.61767 1.45677C8.79706 1.52403 8.96764 1.61284 9.12565 1.72126C9.25721 1.85 9.42378 1.93702 9.60451 1.9714C9.78523 2.00578 9.97207 1.98599 10.1416 1.91453C10.3131 1.84923 10.461 1.7338 10.5661 1.58322C10.6712 1.43264 10.7286 1.2539 10.7309 1.07021C10.7289 0.970875 10.7481 0.872254 10.787 0.780872C10.826 0.689491 10.8839 0.607428 10.9568 0.540116C11.0298 0.472804 11.1163 0.421778 11.2104 0.390405C11.3046 0.359033 11.4043 0.348028 11.503 0.358127H12.519C12.6168 0.348134 12.7157 0.358873 12.8091 0.389645C12.9026 0.420416 12.9885 0.47053 13.0613 0.536732C13.1341 0.602935 13.1922 0.683745 13.2318 0.773903C13.2714 0.864061 13.2916 0.961552 13.2911 1.06004C13.3036 1.22857 13.3619 1.39045 13.4598 1.52815C13.5576 1.66585 13.6912 1.77411 13.8461 1.84119C14.0011 1.90826 14.1714 1.9316 14.3386 1.90866C14.5058 1.88573 14.6635 1.8174 14.7947 1.71108C14.861 1.64342 14.94 1.58966 15.0272 1.55296C15.1144 1.51626 15.2081 1.49736 15.3027 1.49736C15.3973 1.49736 15.491 1.51626 15.5782 1.55296C15.6655 1.58966 15.7445 1.64342 15.8107 1.71108L16.5015 2.39264C16.5896 2.45743 16.662 2.54113 16.7135 2.6376C16.765 2.73407 16.7942 2.84087 16.7991 2.95015C16.804 3.05942 16.7843 3.1684 16.7416 3.26909C16.6989 3.36977 16.6343 3.4596 16.5523 3.53198C16.4653 3.62572 16.3994 3.73705 16.359 3.85845C16.3186 3.97986 16.3047 4.10853 16.3182 4.23578C16.3317 4.36304 16.3723 4.48592 16.4372 4.59614C16.5021 4.70637 16.5899 4.80138 16.6946 4.87476C16.8458 4.96835 17.0158 5.0274 17.1924 5.04769C17.2992 5.04562 17.4052 5.06636 17.5034 5.10853C17.6016 5.1507 17.6897 5.21335 17.7618 5.29227C17.8339 5.3712 17.8883 5.46461 17.9215 5.56627C17.9548 5.66793 17.966 5.77551 17.9544 5.88184V6.8991C17.9614 6.999 17.9477 7.09928 17.914 7.19356C17.8802 7.28785 17.8273 7.37409 17.7585 7.44679C17.6897 7.5195 17.6066 7.57708 17.5144 7.61589C17.4222 7.6547 17.3229 7.67388 17.2229 7.67222C17.0462 7.67708 16.8746 7.73253 16.7285 7.83203C16.5823 7.93152 16.4677 8.07089 16.3982 8.2336C16.3287 8.39632 16.3072 8.57556 16.3364 8.75011C16.3655 8.92466 16.444 9.08719 16.5625 9.21845C16.6418 9.28626 16.7055 9.37048 16.7492 9.46531C16.7929 9.56015 16.8155 9.66334 16.8155 9.76777C16.8155 9.8722 16.7929 9.97539 16.7492 10.0702C16.7055 10.1651 16.6418 10.2493 16.5625 10.3171L15.8513 11.019C15.7866 11.0893 15.7081 11.1454 15.6207 11.1837C15.5332 11.2221 15.4388 11.2419 15.3433 11.2419C15.2479 11.2419 15.1535 11.2221 15.066 11.1837C14.9786 11.1454 14.9001 11.0893 14.8354 11.019C14.7057 10.8874 14.5396 10.7977 14.3585 10.7614C14.1775 10.7251 13.9897 10.744 13.8194 10.8155C13.643 10.8827 13.4917 11.0028 13.3863 11.1594C13.2808 11.316 13.2262 11.5015 13.2302 11.6904C13.2302 11.7825 13.2117 11.8737 13.1756 11.9585C13.1395 12.0432 13.0866 12.1197 13.0201 12.1835C12.9537 12.2472 12.875 12.2967 12.7888 12.3291C12.7027 12.3615 12.6109 12.3761 12.519 12.372H11.4014C11.3098 12.3747 11.2186 12.3591 11.1331 12.3262C11.0476 12.2933 10.9695 12.2437 10.9033 12.1802C10.8371 12.1168 10.7842 12.0408 10.7476 11.9568C10.7111 11.8727 10.6916 11.7821 10.6902 11.6904C10.6923 11.5059 10.6392 11.325 10.5378 11.171C10.4364 11.0169 10.2914 10.8967 10.1213 10.8257C9.95181 10.7498 9.76294 10.7286 9.58088 10.7651C9.39882 10.8015 9.23261 10.8938 9.10533 11.0292C9.03985 11.0981 8.96105 11.153 8.87372 11.1905C8.7864 11.228 8.69237 11.2474 8.59735 11.2474C8.50233 11.2474 8.4083 11.228 8.32097 11.1905C8.23365 11.153 8.15485 11.0981 8.08937 11.0292C7.83537 10.785 7.58139 10.5307 7.33756 10.2764C7.26998 10.2101 7.21629 10.131 7.17964 10.0436C7.14299 9.95629 7.12411 9.86251 7.12411 9.76777C7.12411 9.67304 7.14299 9.57926 7.17964 9.49192C7.21629 9.40458 7.26998 9.32544 7.33756 9.25914C7.46545 9.12971 7.55238 8.9654 7.58748 8.78676C7.62258 8.60811 7.60429 8.42306 7.53489 8.25477C7.46549 8.08649 7.34806 7.94243 7.19731 7.84062C7.04656 7.73881 6.86918 7.68377 6.68735 7.6824C6.50224 7.67977 6.32546 7.60497 6.19456 7.4739C6.06366 7.34283 5.98895 7.16582 5.98633 6.98048C5.98633 6.59393 5.98633 6.21754 5.98633 5.83098C5.98633 5.44443 6.2708 5.15959 6.7483 5.10872C6.96022 5.10072 7.1624 5.01755 7.31873 4.87407C7.47506 4.73058 7.57535 4.53613 7.60171 4.32544C7.62362 4.19673 7.61578 4.06471 7.57879 3.9395C7.54181 3.8143 7.47667 3.69925 7.38836 3.60319C7.30365 3.52846 7.23709 3.43536 7.19376 3.33098C7.15043 3.2266 7.13148 3.11369 7.13834 3.00086C7.1452 2.88803 7.17769 2.77825 7.23334 2.6799C7.28899 2.58155 7.36634 2.49722 7.45947 2.43334C7.67022 2.20032 7.8941 1.97955 8.13001 1.77212C8.28218 1.6517 8.44547 1.54611 8.61767 1.45677ZM16.1256 3.07421L15.2824 2.22989C15.061 2.45267 14.7762 2.60156 14.4671 2.65618C14.1579 2.7108 13.8395 2.6685 13.5553 2.53506C13.2548 2.42088 12.9964 2.21739 12.8148 1.9519C12.6332 1.68641 12.537 1.37165 12.5393 1.04986H11.3608C11.3511 1.35421 11.2558 1.64965 11.0859 1.90218C10.9159 2.15472 10.6783 2.35409 10.4002 2.47736C10.1221 2.60064 9.81492 2.64281 9.51396 2.59903C9.213 2.55525 8.9305 2.4273 8.69895 2.22989L7.8557 3.05386C8.07322 3.27031 8.22382 3.54495 8.28946 3.84489C8.35509 4.14483 8.33298 4.45736 8.22577 4.74504C8.11856 5.03272 7.93081 5.28337 7.68499 5.46694C7.43916 5.65052 7.14569 5.75925 6.83973 5.78012C6.78894 5.78012 6.70765 5.8615 6.70765 5.90219C6.70765 6.25823 6.70765 6.61427 6.70765 6.91945C7.02723 6.91792 7.34017 7.01073 7.60735 7.1863C7.87453 7.36187 8.08409 7.61239 8.20985 7.90656C8.33561 8.20073 8.37198 8.52549 8.31441 8.84024C8.25685 9.15499 8.10789 9.44577 7.88618 9.67622L8.71927 10.5205C8.93843 10.2914 9.22269 10.1353 9.53347 10.0734C9.84426 10.0115 10.1665 10.0469 10.4566 10.1747C10.7562 10.2901 11.0138 10.4939 11.1952 10.7591C11.3766 11.0243 11.4733 11.3384 11.4725 11.6599H12.6409C12.647 11.3456 12.7438 11.0399 12.9196 10.7796C13.0955 10.5192 13.3428 10.3154 13.6318 10.1928C13.9208 10.0702 14.2391 10.034 14.5482 10.0885C14.8574 10.143 15.1441 10.286 15.3738 10.5002L16.2069 9.66605C15.988 9.43452 15.8418 9.14373 15.7865 8.82975C15.7312 8.51577 15.7692 8.19243 15.8958 7.89989C16.0224 7.60735 16.2321 7.35848 16.4987 7.18418C16.7653 7.00988 17.0772 6.91783 17.3956 6.91945C17.3956 6.6041 17.3956 6.28875 17.3956 5.9734C17.3956 5.65805 17.3956 5.73943 17.1721 5.71909C16.8743 5.69185 16.5901 5.58132 16.352 5.40013C16.1139 5.21894 15.9315 4.97437 15.8256 4.69434C15.7196 4.41432 15.6944 4.11011 15.7529 3.81644C15.8113 3.52278 15.9511 3.25149 16.1561 3.03352L16.1256 3.07421Z"
      fill="currentColor"
    />
    <path
      d="M19.9672 24.4059C19.0211 24.4296 18.0775 24.299 17.1733 24.0194C17.0065 23.9622 16.8436 23.8942 16.6856 23.8159C16.3132 23.6876 16.0022 23.4243 15.8142 23.0778C15.6261 22.7313 15.5745 22.3268 15.6697 21.9441C15.8103 20.9552 16.2951 20.0476 17.0385 19.3814C17.7819 18.7152 18.7363 18.3331 19.7335 18.3024C20.7169 18.2259 21.6931 18.5223 22.4685 19.1328C23.2438 19.7433 23.7617 20.6233 23.9193 21.5983C23.9193 21.8017 23.9701 22.0052 23.9904 22.2086C24.0272 22.5167 23.9653 22.8284 23.8138 23.099C23.6622 23.3695 23.4288 23.585 23.1471 23.7142C22.4389 24.0777 21.6651 24.2956 20.8714 24.3551C20.526 24.3856 20.1805 24.3957 19.9672 24.4059ZM20.0688 23.7345C20.3837 23.7345 20.9425 23.6531 21.5013 23.5718C22.0067 23.5144 22.4928 23.3441 22.9236 23.0733C23.0324 23.0154 23.1248 22.931 23.1923 22.8279C23.2598 22.7248 23.3002 22.6062 23.3097 22.4833C23.3343 22.0454 23.2714 21.607 23.1246 21.1938C22.9779 20.7805 22.7502 20.4008 22.455 20.0768C22.1598 19.7528 21.803 19.491 21.4054 19.3069C21.0079 19.1227 20.5776 19.0198 20.1399 19.0043C19.2636 18.9334 18.3925 19.1938 17.6986 19.7343C17.0046 20.2747 16.5381 21.0559 16.391 21.9238C16.2488 22.6766 16.391 22.9411 17.0818 23.2768H17.1936C18.1167 23.6014 19.0907 23.7564 20.0688 23.7345Z"
      fill="currentColor"
    />
    <path
      d="M19.8746 13.7451C20.2939 13.7431 20.7042 13.8661 21.0535 14.0982C21.4028 14.3304 21.6752 14.6614 21.8361 15.049C21.997 15.4366 22.0391 15.8634 21.957 16.2751C21.875 16.6868 21.6725 17.0647 21.3753 17.3608C21.0782 17.6569 20.6998 17.8578 20.2882 17.938C19.8767 18.0181 19.4506 17.9739 19.0643 17.8109C18.6779 17.648 18.3487 17.3736 18.1185 17.0228C17.8883 16.6719 17.7676 16.2605 17.7716 15.8407C17.7795 15.2856 18.004 14.7557 18.3969 14.3641C18.7899 13.9725 19.3203 13.7504 19.8746 13.7451ZM19.8746 14.4267C19.5168 14.457 19.1835 14.6206 18.9405 14.8853C18.6975 15.15 18.5627 15.4964 18.5627 15.856C18.5627 16.2155 18.6975 16.5619 18.9405 16.8266C19.1835 17.0913 19.5168 17.2549 19.8746 17.2852C20.0722 17.3019 20.2711 17.2774 20.4587 17.2131C20.6463 17.1489 20.8186 17.0463 20.9646 16.912C21.1105 16.7776 21.2271 16.6144 21.3068 16.4326C21.3865 16.2508 21.4277 16.0545 21.4277 15.856C21.4277 15.6574 21.3865 15.4611 21.3068 15.2793C21.2271 15.0975 21.1105 14.9343 20.9646 14.7999C20.8186 14.6656 20.6463 14.563 20.4587 14.4988C20.2711 14.4345 20.0722 14.41 19.8746 14.4267Z"
      fill="currentColor"
    />
    <path
      d="M12.6311 20.754C12.306 20.754 12.1435 20.6421 12.1333 20.4285C12.1231 20.2148 12.2755 20.0928 12.5295 20.0724L13.4642 19.9503C13.7385 19.8995 13.9214 19.9503 13.962 20.2047C14.0027 20.459 13.8706 20.5607 13.6166 20.6116L13.1086 20.7031L12.6311 20.754Z"
      fill="currentColor"
    />
    <path
      d="M20.2416 10.8056C20.14 10.9073 20.0588 11.0396 19.9368 11.0904C19.9014 11.1084 19.8623 11.1182 19.8225 11.1189C19.7828 11.1197 19.7433 11.1114 19.7072 11.0948C19.6711 11.0782 19.6391 11.0536 19.6138 11.0229C19.5885 10.9922 19.5704 10.9562 19.5609 10.9175C19.4289 10.5106 19.3171 10.1037 19.2155 9.68661C19.2025 9.61 19.2174 9.53129 19.2573 9.46466C19.2972 9.39804 19.3596 9.34791 19.4332 9.32334C19.5069 9.29877 19.5868 9.30138 19.6587 9.33069C19.7306 9.36 19.7896 9.41408 19.8251 9.48316C19.9775 9.90024 20.0994 10.3275 20.2416 10.8056Z"
      fill="currentColor"
    />
    <path
      d="M4.76669 9.65582C4.76669 9.71685 4.76669 9.77789 4.76669 9.83893C4.68541 10.1543 4.59397 10.4798 4.50253 10.7952C4.41109 11.1105 4.27903 11.1512 4.086 11.1003C4.03867 11.0903 3.99407 11.0701 3.95528 11.0412C3.91649 11.0122 3.88441 10.9752 3.86124 10.9327C3.83808 10.8902 3.82439 10.8431 3.82109 10.7948C3.8178 10.7465 3.82498 10.698 3.84216 10.6527C3.9336 10.2763 4.04535 9.89997 4.15711 9.53375C4.16738 9.49392 4.18585 9.45668 4.21135 9.42442C4.23685 9.39217 4.2688 9.36561 4.30515 9.34645C4.34151 9.32729 4.38147 9.31596 4.42246 9.31318C4.46345 9.31039 4.50457 9.31622 4.54318 9.3303C4.66509 9.3303 4.75653 9.50323 4.85812 9.59479L4.76669 9.65582Z"
      fill="currentColor"
    />
    <path
      d="M11.2293 20.7441L10.2743 20.5915C10.1853 20.5767 10.1059 20.5271 10.0536 20.4537C10.0012 20.3802 9.98004 20.2889 9.99486 20.1999C10.0097 20.1109 10.0592 20.0314 10.1326 19.9789C10.2059 19.9265 10.2971 19.9053 10.386 19.9202L11.402 20.0728C11.447 20.0724 11.4916 20.0817 11.5327 20.1C11.5738 20.1183 11.6105 20.1453 11.6404 20.179C11.6702 20.2127 11.6925 20.2525 11.7057 20.2956C11.7189 20.3387 11.7227 20.3841 11.7169 20.4288C11.6864 20.6221 11.5239 20.7543 11.2293 20.7441Z"
      fill="currentColor"
    />
    <path
      d="M4.29947 12.4026C4.29947 12.5654 4.29947 12.7383 4.29947 12.9113C4.30082 12.9553 4.29348 12.9993 4.27788 13.0405C4.26227 13.0818 4.2387 13.1195 4.2085 13.1516C4.17831 13.1838 4.1421 13.2096 4.10192 13.2277C4.06175 13.2458 4.0184 13.2558 3.97437 13.2571C3.9265 13.2619 3.87816 13.2559 3.83286 13.2397C3.78757 13.2235 3.74643 13.1974 3.71241 13.1634C3.67839 13.1293 3.65236 13.0881 3.63617 13.0428C3.61998 12.9974 3.61403 12.949 3.61878 12.9011C3.60103 12.5622 3.60103 12.2227 3.61878 11.8838C3.61997 11.8406 3.63003 11.798 3.64834 11.7588C3.66665 11.7197 3.69283 11.6847 3.72523 11.656C3.75763 11.6274 3.79556 11.6058 3.83668 11.5925C3.87779 11.5791 3.92119 11.5744 3.9642 11.5786C4.00866 11.5786 4.05266 11.5876 4.09349 11.6052C4.13432 11.6228 4.17112 11.6486 4.20159 11.6811C4.23206 11.7135 4.25556 11.7518 4.27065 11.7937C4.28574 11.8356 4.29209 11.8801 4.28932 11.9245V12.4331L4.29947 12.4026Z"
      fill="currentColor"
    />
    <path
      d="M20.3423 12.4125C20.3423 12.5753 20.3423 12.7482 20.3423 12.9212C20.3423 12.9661 20.3333 13.0106 20.3158 13.052C20.2983 13.0934 20.2727 13.1309 20.2405 13.1622C20.2083 13.1935 20.1701 13.218 20.1282 13.2343C20.0864 13.2505 20.0417 13.2582 19.9968 13.2568C19.9501 13.257 19.9039 13.2475 19.8611 13.2287C19.8183 13.21 19.7798 13.1825 19.7483 13.148C19.7167 13.1135 19.6927 13.0728 19.6778 13.0285C19.6628 12.9842 19.6573 12.9372 19.6615 12.8906V11.8835C19.6615 11.6598 19.7936 11.5173 19.9968 11.5173C20.0439 11.5142 20.0911 11.5213 20.1351 11.5382C20.1791 11.5551 20.219 11.5814 20.2518 11.6153C20.2847 11.6492 20.3098 11.6898 20.3254 11.7344C20.341 11.7789 20.3468 11.8264 20.3423 11.8734C20.3423 12.0361 20.3423 12.2091 20.3423 12.382V12.4125Z"
      fill="currentColor"
    />
    <path
      d="M5.75303 7.74319C5.72263 7.8341 5.68527 7.92253 5.64127 8.00768C5.51105 8.24877 5.36863 8.48304 5.21457 8.70959C5.08249 8.92321 4.89961 8.97407 4.72689 8.86218C4.68633 8.83848 4.65118 8.80653 4.62374 8.76837C4.5963 8.73021 4.57717 8.6867 4.56759 8.64067C4.55801 8.59463 4.55821 8.5471 4.56815 8.50115C4.57809 8.45519 4.59756 8.41184 4.62531 8.3739C4.77204 8.10429 4.93842 7.84588 5.12312 7.60078C5.17178 7.55266 5.23154 7.51728 5.2971 7.49776C5.36266 7.47825 5.43202 7.47519 5.49904 7.48888C5.65143 7.50922 5.65143 7.65164 5.75303 7.74319Z"
      fill="currentColor"
    />
    <path
      d="M18.6358 7.44824C18.6358 7.44824 18.7984 7.51945 18.8492 7.60083C19.0321 7.86532 19.1946 8.12981 19.3572 8.40447C19.3822 8.44255 19.399 8.48547 19.4064 8.53045C19.4138 8.57544 19.4117 8.62147 19.4002 8.66559C19.3887 8.7097 19.3681 8.75091 19.3397 8.78654C19.3113 8.82216 19.2757 8.85142 19.2353 8.87241C19.198 8.89732 19.1559 8.914 19.1117 8.92133C19.0676 8.92867 19.0223 8.92649 18.9791 8.91494C18.9358 8.90338 18.8955 8.88273 18.8609 8.85435C18.8262 8.82596 18.798 8.7905 18.7781 8.75033C18.6054 8.48584 18.4428 8.23153 18.2904 7.95687C18.138 7.68221 18.3107 7.45841 18.6358 7.44824Z"
      fill="currentColor"
    />
    <path
      d="M14.7346 20.3473C14.6127 20.2252 14.4603 20.1439 14.4399 20.032C14.4326 19.964 14.4418 19.8952 14.4665 19.8315C14.4913 19.7678 14.5309 19.7109 14.5822 19.6658C14.875 19.5002 15.185 19.3671 15.5067 19.269C15.5739 19.2567 15.6432 19.2624 15.7074 19.2857C15.7717 19.3089 15.8286 19.3488 15.8724 19.4013C15.9334 19.4928 15.8724 19.7471 15.8724 19.7878C15.5067 20.0218 15.1308 20.154 14.7346 20.3473Z"
      fill="currentColor"
    />
    <path
      d="M5.09224 19.8286H3.06031C2.7352 19.8286 2.62344 19.7167 2.61328 19.3912V17.7636C2.61328 17.4482 2.7352 17.3262 3.03998 17.3262H7.2664C7.58135 17.3262 7.6931 17.4482 7.6931 17.7534C7.6931 18.3027 7.6931 18.8521 7.6931 19.4116C7.6931 19.7066 7.57119 19.8286 7.25624 19.8286H5.09224ZM6.9413 19.1369V17.9874H3.2635V19.0962L6.9413 19.1369Z"
      fill="currentColor"
    />
    <path
      d="M11.9808 3.26759C12.6011 3.26573 13.2078 3.44911 13.7235 3.79429C14.2392 4.13947 14.6404 4.63078 14.8758 5.20541C15.1112 5.78005 15.1701 6.41192 15.0451 7.02026C14.92 7.62861 14.6166 8.18579 14.1736 8.6206C13.7307 9.05542 13.1683 9.34811 12.5584 9.46127C11.9485 9.57444 11.3187 9.50294 10.7496 9.2559C10.1804 9.00887 9.69779 8.59752 9.36333 8.07444C9.02887 7.55136 8.85776 6.94031 8.8719 6.31937C8.88787 5.50442 9.22244 4.72826 9.80374 4.15763C10.385 3.587 11.1667 3.26744 11.9808 3.26759ZM11.9808 3.94915C11.4972 3.94715 11.024 4.08916 10.6213 4.35711C10.2185 4.62506 9.90442 5.00685 9.71892 5.45397C9.53342 5.90109 9.48489 6.39334 9.57948 6.86815C9.67407 7.34296 9.90751 7.77888 10.2501 8.12052C10.5928 8.46216 11.0291 8.69407 11.5037 8.78679C11.9783 8.87951 12.4697 8.82885 12.9155 8.64124C13.3613 8.45363 13.7413 8.13755 14.0072 7.73317C14.2731 7.32879 14.4129 6.85438 14.4089 6.37023C14.4062 5.72719 14.1492 5.1114 13.6941 4.65765C13.2391 4.2039 12.623 3.94915 11.9808 3.94915Z"
      fill="currentColor"
    />
    <path
      d="M11.5433 6.79718C12.031 6.30889 12.4882 5.84095 12.9555 5.38319C13.0063 5.33232 13.0571 5.27129 13.1079 5.2306C13.1702 5.17372 13.2517 5.14247 13.3361 5.14308C13.4204 5.14368 13.5014 5.17609 13.563 5.23386C13.6245 5.29162 13.662 5.37049 13.6681 5.45473C13.6741 5.53897 13.6482 5.6224 13.5956 5.68837C13.5331 5.76441 13.4652 5.8358 13.3924 5.90199L11.8684 7.42788C11.6246 7.67202 11.4722 7.67202 11.2284 7.42788C10.9845 7.18374 10.568 6.76666 10.2124 6.41062C9.85681 6.05458 9.99904 6.01389 10.1514 5.8613C10.3038 5.70871 10.4969 5.73923 10.7001 5.94268L11.5433 6.79718Z"
      fill="currentColor"
    />
  </svg>
);

export const AvailabilityIcon: React.FC<IconInterface> = ({
  className = "size-4",
}) => (
  <svg
    className={className}
    viewBox="0 0 24 25"
    fill="currentColor"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M19.5 3.78418H16.875V2.65918C16.875 2.55972 16.8355 2.46434 16.7652 2.39401C16.6948 2.32369 16.5995 2.28418 16.5 2.28418C16.4005 2.28418 16.3052 2.32369 16.2348 2.39401C16.1645 2.46434 16.125 2.55972 16.125 2.65918V3.78418H7.875V2.65918C7.875 2.55972 7.83549 2.46434 7.76517 2.39401C7.69484 2.32369 7.59946 2.28418 7.5 2.28418C7.40054 2.28418 7.30516 2.32369 7.23483 2.39401C7.16451 2.46434 7.125 2.55972 7.125 2.65918V3.78418H4.5C4.20163 3.78418 3.91548 3.90271 3.7045 4.11368C3.49353 4.32466 3.375 4.61081 3.375 4.90918V19.9092C3.375 20.2075 3.49353 20.4937 3.7045 20.7047C3.91548 20.9157 4.20163 21.0342 4.5 21.0342H19.5C19.7984 21.0342 20.0845 20.9157 20.2955 20.7047C20.5065 20.4937 20.625 20.2075 20.625 19.9092V4.90918C20.625 4.61081 20.5065 4.32466 20.2955 4.11368C20.0845 3.90271 19.7984 3.78418 19.5 3.78418ZM4.5 4.53418H7.125V5.65918C7.125 5.75864 7.16451 5.85402 7.23483 5.92434C7.30516 5.99467 7.40054 6.03418 7.5 6.03418C7.59946 6.03418 7.69484 5.99467 7.76517 5.92434C7.83549 5.85402 7.875 5.75864 7.875 5.65918V4.53418H16.125V5.65918C16.125 5.75864 16.1645 5.85402 16.2348 5.92434C16.3052 5.99467 16.4005 6.03418 16.5 6.03418C16.5995 6.03418 16.6948 5.99467 16.7652 5.92434C16.8355 5.85402 16.875 5.75864 16.875 5.65918V4.53418H19.5C19.5995 4.53418 19.6948 4.57369 19.7652 4.64401C19.8355 4.71434 19.875 4.80972 19.875 4.90918V8.28418H4.125V4.90918C4.125 4.80972 4.16451 4.71434 4.23484 4.64401C4.30516 4.57369 4.40054 4.53418 4.5 4.53418ZM19.5 20.2842H4.5C4.40054 20.2842 4.30516 20.2447 4.23484 20.1743C4.16451 20.104 4.125 20.0086 4.125 19.9092V9.03418H19.875V19.9092C19.875 20.0086 19.8355 20.104 19.7652 20.1743C19.6948 20.2447 19.5995 20.2842 19.5 20.2842ZM10.125 11.6592V17.6592C10.125 17.7586 10.0855 17.854 10.0152 17.9243C9.94484 17.9947 9.84946 18.0342 9.75 18.0342C9.65054 18.0342 9.55516 17.9947 9.48483 17.9243C9.41451 17.854 9.375 17.7586 9.375 17.6592V12.2657L8.41781 12.7448C8.3288 12.7893 8.22575 12.7966 8.13134 12.7652C8.03693 12.7337 7.95888 12.666 7.91437 12.577C7.86987 12.488 7.86255 12.3849 7.89402 12.2905C7.92549 12.1961 7.99317 12.1181 8.08219 12.0736L9.58219 11.3236C9.63938 11.2949 9.70294 11.2814 9.76683 11.2843C9.83072 11.2872 9.89282 11.3063 9.94721 11.34C10.0016 11.3736 10.0465 11.4206 10.0776 11.4764C10.1087 11.5323 10.125 11.5952 10.125 11.6592ZM15.75 14.2842L13.5 17.2842H15.75C15.8495 17.2842 15.9448 17.3237 16.0152 17.394C16.0855 17.4643 16.125 17.5597 16.125 17.6592C16.125 17.7586 16.0855 17.854 16.0152 17.9243C15.9448 17.9947 15.8495 18.0342 15.75 18.0342H12.75C12.6804 18.0342 12.6121 18.0148 12.5529 17.9782C12.4936 17.9416 12.4457 17.8892 12.4146 17.8269C12.3834 17.7646 12.3703 17.6949 12.3765 17.6255C12.3828 17.5561 12.4082 17.4899 12.45 17.4342L15.1481 13.837C15.2962 13.6421 15.3759 13.4039 15.375 13.1592C15.3753 12.9113 15.2937 12.6703 15.1429 12.4735C14.9921 12.2768 14.7805 12.1354 14.5411 12.0712C14.3016 12.0071 14.0477 12.0238 13.8188 12.1189C13.5898 12.2139 13.3987 12.3818 13.275 12.5967C13.2231 12.6783 13.1417 12.7367 13.0477 12.7597C12.9538 12.7827 12.8546 12.7686 12.7709 12.7202C12.6871 12.6718 12.6253 12.5929 12.5984 12.5C12.5714 12.4071 12.5814 12.3074 12.6262 12.2217C12.8326 11.8643 13.1512 11.5849 13.5325 11.427C13.9138 11.269 14.3366 11.2413 14.7353 11.3482C15.134 11.455 15.4862 11.6904 15.7375 12.0178C15.9888 12.3452 16.125 12.7464 16.125 13.1592C16.1264 13.5651 15.9946 13.9603 15.75 14.2842Z"
      fill="currentColor"
    />
  </svg>
);

export const WakeupIcon: React.FC<IconInterface> = ({
  className = "size-4",
}) => (
  <svg
    className={className}
    viewBox="0 0 24 25"
    fill="currentColor"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M14.6611 20.1633C13.8675 19.3826 13.0593 18.617 12.2733 17.8299C11.7478 17.3027 11.1466 17.1785 10.4552 17.4018C10.0256 17.5399 9.5928 17.6723 9.17076 17.8312C8.94618 17.9157 8.81433 17.8691 8.66167 17.6855C7.33219 16.0858 6.15556 14.3651 5.14719 12.5459C5.0513 12.3731 5.04688 12.2526 5.17936 12.0981C5.52065 11.6996 5.85816 11.2966 6.17548 10.8785C6.54579 10.3898 6.62464 9.84931 6.37987 9.27544C5.91493 8.18444 5.45693 7.09093 4.98064 6.00498C4.60213 5.14354 3.95046 4.89129 3.08051 5.24255C2.41496 5.51057 1.8043 5.87949 1.193 6.24841C0.379833 6.73904 -0.102138 7.40498 0.0183548 8.32003C0.0144565 8.90397 0.0732525 9.48665 0.193732 10.0581C0.522406 11.6895 1.1501 13.2112 1.96643 14.6503C3.36314 17.1154 4.95288 19.4431 6.98612 21.4409C8.12166 22.5534 9.38336 23.4735 10.8734 24.0707C11.6305 24.3753 12.3124 24.2599 12.9552 23.8027C13.5767 23.3708 14.1577 22.8837 14.6914 22.3472C15.4238 21.5936 15.4181 20.9074 14.6611 20.1633ZM14.0504 21.6415C13.5292 22.1749 12.9559 22.6547 12.3389 23.0736C12.1275 23.2278 11.8705 23.3068 11.609 23.2982C11.3176 23.2717 10.9889 23.0989 10.6589 22.9437C9.74329 22.5131 8.90279 21.9382 8.1696 21.241C5.36956 18.6017 3.16414 15.3963 1.70021 11.8383C1.22076 10.678 0.948232 9.46021 0.952648 8.18823C0.952648 7.78021 1.09648 7.46931 1.41695 7.24228C2.05905 6.78426 2.7519 6.40187 3.48174 6.10273C3.82303 5.96588 3.95172 6.02264 4.09934 6.36318C4.57122 7.45165 5.03363 8.54453 5.50299 9.63427C5.62222 9.90985 5.55345 10.1457 5.37682 10.3652C4.99031 10.8356 4.60339 11.3063 4.21605 11.7772C3.9309 12.1253 3.91955 12.2678 4.13088 12.66C5.27138 14.781 6.63546 16.774 8.19988 18.605C8.52288 18.9834 8.60804 19.0111 9.06793 18.8604C9.61678 18.6807 10.1662 18.5047 10.7126 18.3168C11.0646 18.1951 11.351 18.2436 11.6267 18.5211C12.4197 19.3182 13.2341 20.0939 14.0359 20.8816C14.3576 21.1906 14.3576 21.3167 14.0504 21.6415Z"
      fill="currentColor"
    />
    <path
      d="M20.4691 8.57022C20.2042 8.55256 20.0528 8.43652 19.9941 8.22274C19.959 8.12165 19.9635 8.01101 20.0069 7.91316C20.0502 7.8153 20.129 7.73753 20.2275 7.69553C20.4994 7.56184 20.7839 7.45463 21.0646 7.33922C21.539 7.14499 22.0109 6.94949 22.4904 6.76157C22.86 6.61589 23.1263 6.6985 23.231 6.98166C23.3313 7.2503 23.1988 7.49373 22.8569 7.63499C22.1503 7.92592 21.4421 8.21244 20.7322 8.49454C20.6461 8.52513 20.5583 8.5504 20.4691 8.57022Z"
      fill="currentColor"
    />
    <path
      d="M17.8651 1.67312C17.8536 1.74462 17.8363 1.81506 17.8133 1.88375C17.4929 2.64619 17.1755 3.40988 16.8424 4.16664C16.7207 4.44538 16.4273 4.55195 16.1927 4.41889C15.958 4.28583 15.8659 4.08781 15.9706 3.83367C16.2942 3.04979 16.621 2.26718 16.9573 1.48898C17.0557 1.26195 17.2519 1.16294 17.4992 1.21718C17.7345 1.26826 17.8373 1.43916 17.8651 1.67312Z"
      fill="currentColor"
    />
    <path
      d="M13.9356 2.15477C13.9356 2.54324 13.9438 2.93234 13.9356 3.32017C13.9261 3.67459 13.7463 3.87072 13.4618 3.87072C13.1773 3.87072 12.9988 3.68153 12.9956 3.32081C12.9884 2.53378 12.9884 1.74633 12.9956 0.95846C12.9988 0.599631 13.171 0.40918 13.4612 0.40918C13.7514 0.40918 13.9261 0.604045 13.9349 0.95846C13.9431 1.35702 13.9356 1.75747 13.9356 2.15477Z"
      fill="currentColor"
    />
    <path
      d="M9.67515 1.1565C9.76569 1.15341 9.85489 1.17893 9.93009 1.22942C10.0053 1.27991 10.0627 1.35281 10.094 1.43776C10.4145 2.21407 10.7356 2.98975 11.0403 3.7711C11.1513 4.05614 11.0403 4.29074 10.788 4.40173C10.547 4.50515 10.2972 4.40741 10.1761 4.12047C9.85305 3.35677 9.5351 2.59056 9.23987 1.81551C9.09982 1.45037 9.32061 1.1483 9.67515 1.1565Z"
      fill="currentColor"
    />
    <path
      d="M17.7342 20.2497C17.7096 20.4899 17.6042 20.6615 17.3683 20.7119C17.1052 20.768 16.9128 20.6526 16.8119 20.4117C16.5652 19.8227 16.3293 19.2293 16.0902 18.6371C16.0042 18.4462 15.929 18.2506 15.8649 18.0513C15.7905 17.7833 15.9179 17.543 16.1558 17.4673C16.4176 17.3841 16.6302 17.485 16.7305 17.7196C17.0667 18.5097 17.3809 19.3088 17.7014 20.1052C17.7167 20.1523 17.7276 20.2006 17.7342 20.2497Z"
      fill="currentColor"
    />
    <path
      d="M23.2062 14.8673C23.1942 15.2135 22.8611 15.4077 22.4895 15.2595C21.8776 15.0142 21.2726 14.75 20.6651 14.4933C20.5112 14.4302 20.3497 14.3672 20.2058 14.2909C19.9598 14.1578 19.8709 13.9125 19.9686 13.681C19.9912 13.6248 20.0248 13.5737 20.0675 13.5307C20.1101 13.4877 20.161 13.4536 20.217 13.4306C20.2731 13.4075 20.3332 13.3959 20.3938 13.3965C20.4544 13.3971 20.5142 13.4098 20.5698 13.4338C21.3553 13.7536 22.135 14.0859 22.9147 14.4208C23.0035 14.4559 23.0792 14.5177 23.1314 14.5977C23.1836 14.6777 23.2098 14.7718 23.2062 14.8673Z"
      fill="currentColor"
    />
    <path
      d="M22.2837 10.4844C22.683 10.4844 23.0824 10.4756 23.4823 10.4844C23.8223 10.4939 24.0002 10.6654 23.9996 10.9504C23.999 11.2355 23.8173 11.4165 23.483 11.4209C22.6742 11.431 21.8652 11.431 21.0561 11.4209C20.7406 11.4171 20.5299 11.2147 20.5293 10.9517C20.5287 10.6887 20.7318 10.4945 21.0561 10.4857C21.4642 10.4762 21.8736 10.4844 22.2837 10.4844Z"
      fill="currentColor"
    />
    <path
      d="M21.0585 3.79404C21.0583 3.87323 21.0413 3.95147 21.0087 4.02363C20.976 4.0958 20.9285 4.16026 20.8693 4.21278C20.3053 4.77467 19.7457 5.34098 19.1786 5.89909C18.9363 6.13684 18.6556 6.14377 18.4581 5.93819C18.2607 5.7326 18.2739 5.48035 18.5073 5.24449C19.0751 4.66873 19.6467 4.10242 20.2157 3.53044C20.381 3.36458 20.5746 3.31476 20.7891 3.40431C20.868 3.43464 20.936 3.48806 20.984 3.55759C21.0321 3.62712 21.058 3.70953 21.0585 3.79404Z"
      fill="currentColor"
    />
    <path
      d="M18.7727 15.79C18.8454 15.7909 18.9172 15.8062 18.9838 15.8353C19.0505 15.8643 19.1106 15.9064 19.1607 15.959C19.7285 16.5266 20.3057 17.0942 20.871 17.6687C21.1094 17.9109 21.117 18.1959 20.9145 18.3901C20.7221 18.5749 20.4401 18.5648 20.2136 18.3422C19.632 17.7696 19.0566 17.19 18.4819 16.6099C18.3286 16.4554 18.275 16.2737 18.3753 16.0656C18.4108 15.9883 18.4664 15.922 18.5363 15.8735C18.6062 15.825 18.6879 15.7962 18.7727 15.79Z"
      fill="currentColor"
    />
    <path
      d="M8.05241 6.08465C7.97355 6.0878 7.86 6.01717 7.76032 5.91753C7.18814 5.34555 6.61343 4.77672 6.0463 4.19969C5.82297 3.97329 5.81414 3.68951 6.00087 3.49843C6.18761 3.30735 6.4778 3.30924 6.70238 3.5287C7.28718 4.10257 7.86378 4.68465 8.44227 5.26546C8.51795 5.33245 8.56938 5.42256 8.58854 5.52178C8.60771 5.621 8.59355 5.72377 8.54826 5.81411C8.46688 5.99132 8.31989 6.08213 8.05241 6.08465Z"
      fill="currentColor"
    />
    <path
      d="M9.19727 10.7807C9.19727 8.41581 11.1725 6.41482 13.5028 6.41797C15.9815 6.42113 17.9144 8.37293 17.9081 10.8702C17.9024 13.1903 15.9133 15.1648 13.5855 15.1623C13.0095 15.1629 12.439 15.05 11.9067 14.8301C11.3743 14.6101 10.8906 14.2875 10.483 13.8806C10.0755 13.4736 9.75218 12.9904 9.53156 12.4585C9.31095 11.9266 9.19735 11.3565 9.19727 10.7807Z"
      strokeMiterlimit="10"
    />
    <path
      d="M13.3476 8.28906C13.6485 8.33699 13.8239 8.48393 13.829 8.79357C13.8378 9.31762 13.8599 9.84168 13.8555 10.3657C13.8508 10.4673 13.8685 10.5686 13.9073 10.6625C13.9461 10.7564 14.0051 10.8407 14.08 10.9093C14.5765 11.4012 15.0547 11.9114 15.5436 12.4102C15.7878 12.6593 15.8035 12.9078 15.5512 13.1525C15.2988 13.3972 15.0654 13.3152 14.8446 13.0989C14.2205 12.4872 13.596 11.8761 12.971 11.2656C12.9292 11.2296 12.8957 11.185 12.8727 11.1348C12.8497 11.0846 12.8378 11.0301 12.8379 10.9749C12.8423 10.2447 12.8474 9.51375 12.8423 8.78348C12.8398 8.42402 13.0738 8.33384 13.3476 8.28906Z"
      fill="currentColor"
    />
  </svg>
);

export const LaundaryIcon: React.FC<IconInterface> = ({
  className = "size-4",
}) => (
  <svg
    className={className}
    viewBox="0 0 24 25"
    fill="currentColor"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M0.404877 24.2946C0.309145 24.1703 0.243086 24.0355 0.209416 23.8957L0 7.01177C0 6.76245 8.52981e-06 6.56298 0.488665 6.56298V5.84494C0.488665 5.18674 0.488665 4.5385 0.488665 3.85038C0.488665 3.49136 0.488662 3.43153 1.07505 3.43153H2.02443V2.35446C2.02443 1.94558 2.09423 1.88574 2.6527 1.88574L12.3141 1.82591C12.8307 1.82591 12.9145 1.87576 12.9284 2.25473V3.34177H15.3578C15.9162 3.34177 15.986 3.39164 16 3.79055C16 4.5784 16 5.36624 16 6.1541C15.9933 6.24379 15.9933 6.33366 16 6.42335H17.3962V6.13416C17.3962 4.29917 17.3962 2.47413 17.3962 0.63914C17.3962 0.310038 17.4939 0.240234 17.9546 0.240234H22.5201C23.1065 0.240234 23.1902 0.300072 23.1902 0.708957C23.1902 2.52401 23.1902 4.32908 23.1902 6.13416V6.40342C23.7906 6.4832 23.8045 6.50314 23.8045 6.90206C23.879 12.5001 23.9442 18.0915 24 23.6763C24 24.1251 24 24.155 23.3298 24.155L5.31937 24.2647C5.09598 24.2647 4.88656 24.2647 4.66317 24.2647L0.404877 24.2946ZM0.781846 10.7914L0.935425 23.7561L23.274 23.6264L23.1204 10.6618L0.781846 10.7914ZM0.781846 10.2629L23.0227 10.1332V6.96189L0.781846 7.09154V10.2629ZM18.0803 0.788726V6.43333H22.2688V0.788726H18.0803ZM1.24258 6.45327L15.2042 6.3735V5.37622L1.24258 5.45599V6.45327ZM15.2042 3.91022L1.24258 3.99997V4.99725L15.2042 4.91748V3.91022ZM12.2024 2.44421H2.79232V3.44149H12.1885L12.2024 2.44421Z"
      fill="currentColor"
    />
    <path
      d="M3.92384 16.7354C3.92685 15.5915 4.40623 14.4742 5.30096 13.5255C6.19569 12.5769 7.46531 11.8398 8.94826 11.4082C10.4312 10.9765 12.0604 10.8697 13.6286 11.1015C15.1967 11.3332 16.6329 11.893 17.7543 12.7095C18.8757 13.5261 19.6317 14.5625 19.926 15.6869C20.2203 16.8112 20.0396 17.9727 19.407 19.0235C18.7743 20.0743 17.7183 20.9669 16.3733 21.5877C15.0283 22.2085 13.4552 22.5294 11.854 22.5096C9.73567 22.4834 7.71668 21.8632 6.23297 20.7829C4.74927 19.7026 3.91986 18.2488 3.92384 16.7354ZM12.0216 21.9911C13.4758 21.989 14.8965 21.6787 16.1035 21.0993C17.3105 20.5199 18.2496 19.6976 18.8016 18.7366C19.3536 17.7755 19.4937 16.7191 19.2042 15.7011C18.9147 14.6831 18.2086 13.7495 17.1754 13.0185C16.1422 12.2874 14.8285 11.792 13.4007 11.5949C11.9728 11.3979 10.4952 11.508 9.15514 11.9115C7.81506 12.3149 6.67286 12.9935 5.87328 13.8612C5.0737 14.7288 4.65275 15.7465 4.66381 16.7852C4.68943 18.1688 5.47504 19.4899 6.85134 20.4636C8.22763 21.4374 10.0844 21.9859 12.0216 21.9911Z"
      fill="currentColor"
    />
    <path
      d="M4.99839 9.35559H2.63888C2.19211 9.35559 2.09439 9.2858 2.08042 8.96667C2.06646 8.64754 2.08042 8.27854 2.08042 7.96939C2.08042 7.66023 2.22004 7.61035 2.58304 7.61035H7.41375C7.77675 7.61035 7.9024 7.69013 7.91636 7.94942V9.03648C7.91636 9.2858 7.80468 9.36558 7.44167 9.37556H5.01235L4.99839 9.35559ZM2.83434 8.79712H7.14847V8.10899H2.79246L2.83434 8.79712Z"
      fill="currentColor"
    />
    <path
      d="M19.713 8.61762C19.5594 8.61762 19.3081 8.61763 19.2801 8.37828C19.2522 8.13893 19.4337 8.09903 19.7828 8.08906C20.1318 8.07909 20.3692 8.17881 20.3552 8.35832C20.3412 8.53783 20.1737 8.61762 19.713 8.61762Z"
      fill="currentColor"
    />
    <path
      d="M21.6412 8.61742C21.2643 8.61742 21.0548 8.48777 21.1246 8.29829C21.1945 8.1088 21.4178 8.08887 21.6412 8.08887C21.8646 8.08887 22.1159 8.08886 22.1997 8.29829C22.2834 8.50772 22.0601 8.61742 21.6412 8.61742Z"
      fill="currentColor"
    />
    <path
      d="M16.126 8.73698C15.9334 8.66071 15.7591 8.56309 15.6094 8.44776C15.6094 8.3979 15.6094 8.19846 15.763 8.18849C16.0065 8.14876 16.2594 8.14876 16.5029 8.18849C16.5521 8.2214 16.5901 8.2618 16.6143 8.3067C16.6385 8.3516 16.6481 8.39982 16.6425 8.44776C16.4899 8.56079 16.3161 8.65813 16.126 8.73698Z"
      fill="currentColor"
    />
    <path
      d="M17.9677 8.72685C17.7776 8.648 17.6038 8.55069 17.4512 8.43766C17.4512 8.38779 17.521 8.18833 17.6048 8.17836C17.853 8.13846 18.1105 8.13846 18.3587 8.17836C18.3587 8.17836 18.5262 8.39777 18.4843 8.43766C18.3317 8.55069 18.1579 8.648 17.9677 8.72685Z"
      fill="currentColor"
    />
    <path
      d="M11.9782 12.9258C13.0391 12.9219 14.0778 13.1429 14.9628 13.5608C15.8478 13.9788 16.5393 14.5749 16.9498 15.2737C17.3603 15.9725 17.4713 16.7426 17.2687 17.4865C17.0661 18.2304 16.5591 18.9147 15.8119 19.4526C15.0646 19.9906 14.1107 20.3581 13.0709 20.5086C12.031 20.6591 10.9521 20.5859 9.97059 20.2981C8.98908 20.0104 8.14916 19.5211 7.55713 18.8923C6.96511 18.2634 6.6476 17.5232 6.64482 16.7654C6.63743 16.2622 6.77003 15.763 7.03492 15.2967C7.2998 14.8305 7.6917 14.4064 8.18787 14.0492C8.68404 13.692 9.27459 13.4088 9.92526 13.2159C10.5759 13.0231 11.2737 12.9245 11.9782 12.9258ZM16.4179 16.0074C16.0717 16.1855 15.7616 16.3969 15.4965 16.6357C15.4074 16.7786 15.2725 16.9044 15.1027 17.0032C14.933 17.1019 14.7331 17.1708 14.5192 17.2041C14.1339 17.2624 13.7399 17.2858 13.3464 17.274C12.2993 17.274 11.2522 17.1343 10.205 17.0845C9.46507 17.0845 8.69719 17.0147 7.94326 17.0147C7.44065 17.0147 7.37084 17.1044 7.46858 17.3936C7.62027 17.9827 8.00031 18.5296 8.5655 18.9721C9.1307 19.4146 9.85836 19.735 10.6658 19.8968C11.4588 20.0867 12.3113 20.1107 13.1227 19.9658C13.9341 19.8209 14.6706 19.5132 15.2452 19.079C15.8149 18.6925 16.2254 18.2018 16.4322 17.6602C16.639 17.1187 16.634 16.5471 16.4179 16.0074ZM7.39876 16.5061C7.88742 16.5061 8.33419 16.4462 8.79492 16.5061C9.81412 16.5559 10.8473 16.6457 11.8665 16.7055C12.6204 16.7055 13.3743 16.7554 14.1282 16.7654C14.2832 16.774 14.4376 16.7439 14.5649 16.6803C14.6923 16.6166 14.7847 16.5233 14.8263 16.4163C14.8899 16.3072 14.9907 16.2112 15.1195 16.1371C15.4686 15.9276 15.8455 15.7282 16.2225 15.5188C15.828 14.8376 15.1238 14.2712 14.2236 13.9108C13.3234 13.5504 12.2794 13.4171 11.2601 13.5323C10.2408 13.6475 9.30556 14.0045 8.60504 14.5458C7.90453 15.0871 7.4796 15.7812 7.39876 16.516V16.5061Z"
      fill="currentColor"
    />
  </svg>
);

export const ExpenseIcon: React.FC<IconInterface> = ({
  className = "size-4",
}) => (
  <svg
    className={className}
    viewBox="0 0 24 24"
    fill="currentColor"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M23.4933 11.0186C23.4933 10.6386 23.3733 10.5186 22.9933 10.5186C22.0933 10.5186 21.1933 10.5186 20.2933 10.5186C20.1233 10.5186 20.0733 10.5186 20.0733 10.2986C20.0733 9.47193 20.0733 8.64194 20.0733 7.8086C20.0733 7.6186 20.1333 7.5786 20.3133 7.5786H23.0633C23.1272 7.58839 23.1925 7.58175 23.253 7.5593C23.3136 7.53685 23.3674 7.49934 23.4095 7.45029C23.4515 7.40125 23.4803 7.34231 23.4933 7.27902C23.5062 7.21574 23.5028 7.1502 23.4833 7.0886C23.3885 6.28047 23.0382 5.52373 22.4833 4.9286C22.0985 4.53119 21.6354 4.21792 21.1233 4.0086C21.3435 3.81301 21.5211 3.57427 21.6452 3.30719C21.7693 3.04011 21.8373 2.75038 21.8448 2.45596C21.8523 2.16155 21.7992 1.86874 21.6888 1.59568C21.5785 1.32262 21.4132 1.07515 21.2033 0.868599C20.9978 0.680153 20.7571 0.53403 20.4952 0.438576C20.2332 0.343123 19.9549 0.300208 19.6764 0.312279C19.1138 0.336659 18.5839 0.583541 18.2033 0.998604C17.8227 1.41367 17.6226 1.96292 17.647 2.52553C17.6714 3.08814 17.9182 3.61802 18.3333 3.9986H18.2233C17.6409 4.25184 17.1315 4.64737 16.7419 5.14885C16.3522 5.65033 16.0948 6.24169 15.9933 6.8686C15.9033 7.3286 15.9933 7.4986 16.5133 7.4986H19.1933C19.3433 7.4986 19.3933 7.4986 19.3933 7.6986C19.3933 8.5486 19.3933 9.3986 19.3933 10.2586C19.3933 10.4086 19.3933 10.4386 19.2033 10.4386C18.7933 10.4386 18.3733 10.4386 17.9633 10.4386C17.5533 10.4386 17.4533 10.6186 17.5133 10.9786C17.6582 11.6947 17.6582 12.4325 17.5133 13.1486C17.1952 14.628 16.3106 15.924 15.0489 16.7593C13.7871 17.5946 12.2485 17.9028 10.7624 17.6178C9.27633 17.3329 7.96082 16.4775 7.09751 15.2348C6.2342 13.992 5.89167 12.4607 6.14329 10.9686C6.20329 10.6186 6.05334 10.4386 5.69334 10.4386C5.33334 10.4386 4.89332 10.4386 4.48332 10.4386C4.30332 10.4386 4.26332 10.4386 4.26332 10.2186C4.26332 9.39193 4.26332 8.56193 4.26332 7.7286C4.26332 7.5286 4.32334 7.4986 4.50334 7.4986H7.16331C7.60331 7.4986 7.74331 7.3386 7.66331 6.8986C7.46564 5.98835 6.94307 5.18134 6.19334 4.6286C5.88455 4.39028 5.54893 4.18891 5.19334 4.0286C6.12334 2.8986 6.19331 1.7786 5.29331 0.9086C4.91409 0.531326 4.40574 0.312172 3.87107 0.295464C3.33641 0.278756 2.81535 0.465744 2.41331 0.818604C2.03148 1.137 1.78191 1.58619 1.7133 2.0786C1.65422 2.43484 1.68757 2.80028 1.81013 3.13995C1.93269 3.47963 2.14038 3.78216 2.41331 4.0186L2.3133 4.0786C1.70279 4.33502 1.17011 4.74692 0.76832 5.27328C0.366535 5.79963 0.109658 6.42207 0.0232945 7.0786C-0.0467055 7.4686 0.0932957 7.6386 0.493296 7.6386H3.19331C3.37331 7.6386 3.42332 7.63861 3.42332 7.8586C3.42332 8.68527 3.42332 9.51527 3.42332 10.3486C3.42332 10.5486 3.3533 10.5786 3.1833 10.5786C2.2833 10.5786 1.38332 10.5786 0.483316 10.5786C0.133316 10.5786 0.00330549 10.7086 0.00330549 11.0486V13.1286C-0.00999622 13.239 0.017127 13.3504 0.07963 13.4423C0.142133 13.5342 0.235784 13.6004 0.343302 13.6286C0.833302 13.7986 1.34332 13.9986 1.80332 14.1786C1.8519 14.1915 1.8957 14.2182 1.92942 14.2555C1.96314 14.2927 1.98531 14.339 1.9933 14.3886C2.22702 15.4317 2.6398 16.4265 3.2133 17.3286C3.24786 17.3841 3.26618 17.4482 3.26618 17.5136C3.26618 17.579 3.24786 17.6431 3.2133 17.6986C2.9933 18.1586 2.80332 18.6386 2.58332 19.0986C2.53101 19.1887 2.51234 19.2944 2.53065 19.3969C2.54896 19.4995 2.60304 19.5922 2.6833 19.6586C3.1833 20.1486 3.68332 20.6586 4.17332 21.1486C4.24188 21.2321 4.33819 21.2882 4.44468 21.3066C4.55118 21.3249 4.66076 21.3044 4.75334 21.2486C5.21334 21.0386 5.6933 20.8386 6.1533 20.6186C6.20534 20.5846 6.26613 20.5665 6.32829 20.5665C6.39044 20.5665 6.4513 20.5846 6.50334 20.6186C7.40048 21.1787 8.38356 21.5875 9.41331 21.8286C9.46999 21.8379 9.52313 21.8624 9.56712 21.8993C9.61111 21.9363 9.64433 21.9844 9.66331 22.0386C9.84331 22.5386 10.0433 23.0386 10.2333 23.5186C10.2587 23.6136 10.3167 23.6967 10.3971 23.7533C10.4775 23.81 10.5753 23.8366 10.6733 23.8286H12.8133C12.9149 23.8351 13.0157 23.8071 13.0992 23.7489C13.1828 23.6908 13.2441 23.6061 13.2733 23.5086C13.4533 23.0186 13.6433 22.5086 13.8333 22.0486C13.8445 21.9961 13.8701 21.9477 13.9071 21.9089C13.9442 21.87 13.9914 21.8422 14.0433 21.8286C15.0772 21.6008 16.0647 21.1984 16.9633 20.6386C17.021 20.5999 17.0889 20.5792 17.1583 20.5792C17.2278 20.5792 17.2956 20.5999 17.3533 20.6386C17.8233 20.8586 18.2933 21.0486 18.7533 21.2686C18.845 21.3178 18.9509 21.3338 19.053 21.3137C19.1551 21.2937 19.2471 21.2389 19.3133 21.1586L20.8033 19.6686C20.8879 19.6031 20.945 19.5083 20.9635 19.4029C20.982 19.2975 20.9606 19.189 20.9033 19.0986C20.6933 18.6486 20.5033 18.1886 20.2933 17.7386C20.2512 17.6748 20.2287 17.6 20.2287 17.5236C20.2287 17.4471 20.2512 17.3724 20.2933 17.3086C20.8468 16.4212 21.2489 15.4479 21.4833 14.4286C21.491 14.3725 21.515 14.3198 21.5523 14.2771C21.5897 14.2345 21.6387 14.2037 21.6933 14.1886L23.1533 13.6286C23.2563 13.6007 23.346 13.5371 23.4065 13.4492C23.4669 13.3613 23.4941 13.2547 23.4833 13.1486C23.4833 12.3886 23.4933 11.6986 23.4933 11.0186ZM18.2633 2.4086C18.2633 2.04791 18.4066 1.70199 18.6617 1.44694C18.9167 1.19189 19.2626 1.0486 19.6233 1.0486C19.984 1.0486 20.33 1.19189 20.585 1.44694C20.84 1.70199 20.9833 2.04791 20.9833 2.4086C20.9833 2.76929 20.84 3.11522 20.585 3.37027C20.33 3.62532 19.984 3.7686 19.6233 3.7686C19.2626 3.7686 18.9167 3.62532 18.6617 3.37027C18.4066 3.11522 18.2633 2.76929 18.2633 2.4086ZM16.9533 6.8186C16.7733 6.8186 16.7233 6.8186 16.7833 6.6086C16.9509 6.11127 17.2433 5.66521 17.6324 5.31303C18.0216 4.96085 18.4945 4.71431 19.006 4.59695C19.5176 4.4796 20.0506 4.49535 20.5544 4.64269C21.0581 4.79004 21.5156 5.06406 21.8833 5.4386C22.2089 5.76328 22.4523 6.16096 22.5933 6.5986C22.6533 6.7886 22.5933 6.8286 22.4133 6.8186H16.9533ZM2.35331 2.3886C2.35332 2.12005 2.43283 1.85751 2.58183 1.63408C2.73083 1.41065 2.94264 1.23633 3.19056 1.13311C3.43849 1.02989 3.71145 1.00238 3.97499 1.05405C4.23852 1.10572 4.48084 1.23425 4.67143 1.42345C4.86202 1.61266 4.99234 1.85405 5.04594 2.1172C5.09954 2.38036 5.07404 2.65349 4.97264 2.90216C4.87123 3.15084 4.69845 3.36392 4.47611 3.51456C4.25378 3.66519 3.99185 3.74663 3.72331 3.7486C3.36169 3.74861 3.01474 3.60564 2.7581 3.35088C2.50146 3.09611 2.35595 2.75021 2.35331 2.3886ZM1.07331 6.8186C0.803313 6.8186 0.79332 6.8186 0.89332 6.5586C1.08845 6.0029 1.44284 5.51692 1.9123 5.16126C2.38177 4.80561 2.94552 4.59601 3.5333 4.5586C4.22946 4.49126 4.92725 4.66926 5.50608 5.06184C6.08491 5.45443 6.50837 6.03691 6.70329 6.7086C6.70558 6.75191 6.70558 6.7953 6.70329 6.8386H1.05332L1.07331 6.8186ZM22.5833 12.9686L21.1933 13.4986C21.0984 13.5285 21.0131 13.5832 20.9463 13.657C20.8795 13.7308 20.8336 13.8211 20.8133 13.9186C20.5766 15.0725 20.1181 16.1695 19.4633 17.1486C19.4171 17.222 19.3926 17.3069 19.3926 17.3936C19.3926 17.4803 19.4171 17.5652 19.4633 17.6386C19.6633 18.0786 19.8533 18.5186 20.0533 18.9486C20.1006 19.0151 20.1197 19.0975 20.1066 19.178C20.0935 19.2585 20.0492 19.3305 19.9833 19.3786C19.6833 19.6586 19.3833 19.9486 19.1133 20.2486C19.0893 20.2837 19.0585 20.3137 19.0227 20.3368C18.987 20.3599 18.947 20.3757 18.9051 20.3831C18.8632 20.3906 18.8202 20.3896 18.7787 20.3802C18.7372 20.3709 18.698 20.3533 18.6633 20.3286C18.2333 20.1286 17.8033 19.9486 17.3733 19.7386C17.2901 19.6893 17.1951 19.6633 17.0983 19.6633C17.0016 19.6633 16.9066 19.6893 16.8233 19.7386C15.8899 20.3663 14.8442 20.8077 13.7433 21.0386C13.6188 21.0515 13.5009 21.1011 13.4047 21.1813C13.3085 21.2614 13.2384 21.3684 13.2033 21.4886C13.0533 21.9386 12.8633 22.3686 12.6933 22.8186C12.6816 22.8697 12.6519 22.9149 12.6097 22.9461C12.5675 22.9772 12.5156 22.9923 12.4633 22.9886H11.0633C11.037 22.9944 11.0098 22.9948 10.9833 22.9899C10.9568 22.9849 10.9316 22.9747 10.9092 22.9597C10.8868 22.9448 10.8676 22.9255 10.8529 22.9029C10.8381 22.8804 10.828 22.8551 10.8233 22.8286L10.2833 21.4386C10.2556 21.3452 10.2026 21.2612 10.1303 21.196C10.058 21.1307 9.96908 21.0866 9.87333 21.0686C8.72569 20.8372 7.63488 20.3818 6.66331 19.7286C6.58649 19.6818 6.49826 19.657 6.4083 19.657C6.31835 19.657 6.23012 19.6818 6.1533 19.7286L4.81333 20.3286C4.78314 20.3496 4.74906 20.3645 4.71311 20.3724C4.67716 20.3802 4.64002 20.3809 4.6038 20.3744C4.56758 20.3679 4.53296 20.3544 4.50199 20.3345C4.47102 20.3146 4.44431 20.2888 4.42332 20.2586C4.13332 19.9486 3.83332 19.6486 3.51332 19.3586C3.45624 19.3132 3.41765 19.2486 3.40476 19.1768C3.39188 19.105 3.40558 19.031 3.44331 18.9686C3.65331 18.5286 3.84332 18.0786 4.05332 17.6286C4.09695 17.5448 4.11542 17.4502 4.10655 17.3562C4.09768 17.2622 4.06183 17.1727 4.00331 17.0986C3.37333 16.1519 2.93194 15.0925 2.70332 13.9786C2.68402 13.8625 2.63256 13.7541 2.55479 13.6657C2.47702 13.5773 2.37605 13.5125 2.26332 13.4786C1.81332 13.3186 1.37332 13.1286 0.923319 12.9586C0.869201 12.9488 0.821073 12.9182 0.789286 12.8733C0.757498 12.8285 0.744573 12.7729 0.753305 12.7186C0.753305 12.2986 0.753305 11.8786 0.753305 11.4686C0.753305 11.3086 0.803297 11.2586 0.963297 11.2586H5.06333C5.19333 11.2586 5.23331 11.2586 5.22331 11.4386C5.14638 12.3133 5.24669 13.1945 5.51817 14.0296C5.78965 14.8646 6.22674 15.6364 6.80332 16.2986C7.92598 17.6026 9.51795 18.4108 11.2332 18.5474C12.9484 18.6841 14.6483 18.1383 15.9633 17.0286C17.188 16.0199 17.9995 14.5964 18.2433 13.0286C18.3033 12.5003 18.3033 11.9669 18.2433 11.4386C18.2433 11.3086 18.2433 11.2786 18.3833 11.2786C19.7633 11.2786 21.1533 11.2786 22.5333 11.2786C22.6833 11.2786 22.7133 11.3386 22.7133 11.4786V12.7586C22.7202 12.8034 22.7109 12.8491 22.6871 12.8876C22.6633 12.9261 22.6264 12.9548 22.5833 12.9686Z"
      fill="currentColor"
    />
    <path
      d="M12.4129 7.6284H15.0929C15.4829 7.6284 15.6329 7.45839 15.5629 7.07839C15.4764 6.45094 15.2331 5.85542 14.8555 5.34682C14.478 4.83823 13.9784 4.43294 13.4029 4.1684L13.1829 4.04839C13.5078 3.7645 13.7383 3.38824 13.8437 2.96987C13.9491 2.5515 13.9243 2.11093 13.7727 1.70701C13.6211 1.30309 13.3498 0.955047 12.9952 0.709358C12.6405 0.463669 12.2194 0.332031 11.7879 0.332031C11.3565 0.332031 10.9353 0.463669 10.5806 0.709358C10.226 0.955047 9.95474 1.30309 9.80312 1.70701C9.6515 2.11093 9.62675 2.5515 9.73214 2.96987C9.83752 3.38824 10.068 3.7645 10.3929 4.04839L10.2429 4.11839C9.65359 4.3753 9.1395 4.77828 8.74929 5.28923C8.35908 5.80018 8.10561 6.40221 8.0129 7.0384C7.9329 7.4484 8.08294 7.61839 8.49294 7.61839H11.193C11.353 7.61839 11.4129 7.6184 11.4129 7.8184C11.4129 8.6684 11.4129 9.5084 11.4129 10.3584C11.4129 10.5384 11.343 10.5584 11.193 10.5584H9.0129C8.9019 10.5497 8.79137 10.5798 8.70015 10.6437C8.60894 10.7075 8.54271 10.8011 8.5129 10.9084C8.30566 11.5094 8.26081 12.1545 8.38289 12.7784C8.47676 13.2459 8.66599 13.6891 8.93874 14.0802C9.21149 14.4713 9.56196 14.802 9.96822 15.0517C10.3745 15.3014 10.8278 15.4646 11.2999 15.5313C11.7721 15.5979 12.253 15.5666 12.7125 15.4391C13.172 15.3117 13.6002 15.0909 13.9706 14.7906C14.341 14.4902 14.6455 14.1168 14.8651 13.6935C15.0847 13.2702 15.2147 12.8063 15.247 12.3305C15.2793 11.8548 15.2133 11.3775 15.0529 10.9284C14.9529 10.6384 14.8529 10.5684 14.5529 10.5684C13.8329 10.5684 13.1129 10.5684 12.3929 10.5684C12.2029 10.5684 12.1629 10.5084 12.1629 10.3284C12.1629 9.50839 12.1629 8.68839 12.1629 7.86839C12.1029 7.62839 12.1829 7.5684 12.4129 7.6284ZM10.3529 2.44839C10.3367 2.25932 10.36 2.06894 10.4212 1.88933C10.4825 1.70971 10.5804 1.54478 10.7087 1.40498C10.837 1.26519 10.993 1.15358 11.1667 1.07723C11.3404 1.00088 11.5281 0.961456 11.7179 0.961456C11.9077 0.961456 12.0954 1.00088 12.2691 1.07723C12.4429 1.15358 12.5988 1.26519 12.7271 1.40498C12.8555 1.54478 12.9534 1.70971 13.0146 1.88933C13.0758 2.06894 13.0991 2.25932 13.0829 2.44839C13.0536 2.79025 12.8971 3.10867 12.6444 3.34069C12.3916 3.57272 12.061 3.70145 11.7179 3.70145C11.3748 3.70145 11.0442 3.57272 10.7915 3.34069C10.5387 3.10867 10.3822 2.79025 10.3529 2.44839ZM14.1829 11.3184C14.3429 11.3184 14.413 11.3184 14.443 11.5384C14.5888 12.2425 14.449 12.9758 14.0542 13.5769C13.6595 14.1779 13.0421 14.5975 12.3379 14.7434C11.6338 14.8893 10.9005 14.7494 10.2995 14.3547C9.6984 13.9599 9.27876 13.3425 9.13289 12.6384C9.06756 12.2814 9.06756 11.9154 9.13289 11.5584C9.13289 11.3684 9.25294 11.3184 9.43294 11.3184H14.193H14.1829ZM11.7829 6.86839H9.0129C8.8429 6.86839 8.75291 6.86839 8.83291 6.64839C9.00508 6.1502 9.30226 5.70453 9.69594 5.35403C10.0896 5.00354 10.5667 4.75994 11.0814 4.64655C11.5962 4.53316 12.1314 4.55378 12.6359 4.70643C13.1405 4.85908 13.5974 5.13865 13.9629 5.51839C14.2553 5.83827 14.4828 6.21184 14.6329 6.61839C14.7029 6.80839 14.6329 6.87839 14.4529 6.86839H11.7829Z"
      fill="currentColor"
    />
  </svg>
);

export const PermissionIcon: React.FC<IconInterface> = ({
  className = "size-4",
}) => (
  <svg
    className={className}
    fill="currentColor"
    xmlns="http://www.w3.org/2000/svg"
    width="1em"
    height="1em"
    viewBox="0 0 2048 2048"
  >
    <path
      fill="currentColor"
      d="M2048 1573v475h-512v-256h-256v-256h-256v-207q-74 39-155 59t-165 20q-97 0-187-25t-168-71t-142-110t-111-143t-71-168T0 704q0-97 25-187t71-168t110-142T349 96t168-71T704 0q97 0 187 25t168 71t142 110t111 143t71 168t25 187q0 51-8 101t-23 98zm-128 54l-690-690q22-57 36-114t14-119q0-119-45-224t-124-183t-183-123t-224-46q-119 0-224 45T297 297T174 480t-46 224q0 119 45 224t124 183t183 123t224 46q97 0 190-33t169-95h89v256h256v256h256v256h256zM512 384q27 0 50 10t40 27t28 41t10 50q0 27-10 50t-27 40t-41 28t-50 10q-27 0-50-10t-40-27t-28-41t-10-50q0-27 10-50t27-40t41-28t50-10"
    ></path>
  </svg>
);

export const HouseKeepingIcon: React.FC<IconInterface> = ({
  className = "size-4",
}) => (
  <svg
    className={className}
    viewBox="0 0 24 25"
    fill="currentColor"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M0.86077 11.4188H0.465847C0.141795 11.4188 0.0101266 11.2867 0 10.9615V8.36008C0 8.02475 0.141784 7.89264 0.486089 7.89264C0.830395 7.89264 1.22533 7.89264 1.62027 7.89264C1.56637 7.35978 1.67967 6.82334 1.94433 6.35822C2.09297 6.13561 2.28401 5.94469 2.50647 5.79646C2.72893 5.64822 2.9784 5.54559 3.24053 5.49448V4.32588C3.24053 3.82795 3.32155 3.7365 3.84814 3.69585L3.93927 2.24272C3.93927 2.01917 3.93927 1.79561 3.93927 1.56189C3.93927 1.32817 4.09117 1.17575 4.34433 1.17575C5.08695 1.17575 5.8262 1.17575 6.56207 1.17575C6.82536 1.17575 6.95701 1.32817 6.97726 1.6127C6.97726 2.2224 7.04815 2.8321 7.07853 3.43164V3.68569H7.18991C7.5646 3.68569 7.66587 3.81779 7.66587 4.2141C7.66587 4.61041 7.66587 5.0372 7.66587 5.45383C7.93018 5.50251 8.18221 5.60348 8.40725 5.75087C8.63229 5.89826 8.82583 6.08912 8.97663 6.31232C9.12743 6.53552 9.23247 6.7866 9.28561 7.05094C9.33874 7.31528 9.33892 7.58759 9.28613 7.852H10.1165V6.01272C10.1165 5.68755 10.2482 5.55545 10.5823 5.55545C10.6802 5.55036 10.7782 5.55036 10.876 5.55545V4.64089H10.6633H9.82284C9.39752 4.64089 9.27602 4.51895 9.29627 4.10232C9.30869 3.61596 9.42869 3.13846 9.64762 2.70437C9.86654 2.27028 10.1789 1.89045 10.5621 1.59237C11.1503 1.09289 11.8979 0.822379 12.6684 0.830245C14.0254 0.830245 15.3722 0.830245 16.7191 0.830245C17.0735 0.830245 17.2052 0.952187 17.2052 1.30785V2.95405C17.2052 3.28939 17.0735 3.42148 16.7292 3.43164C16.3849 3.4418 16.0609 3.43164 15.7165 3.43164C15.3722 3.43164 15.3823 3.5231 15.4431 3.80763C15.4964 4.07031 15.6182 4.31408 15.7962 4.51404C15.9741 4.714 16.2017 4.86298 16.4558 4.94574L16.8406 5.05752C16.895 5.06579 16.947 5.08535 16.9934 5.11497C17.0398 5.14459 17.0796 5.18361 17.1101 5.22953C17.1406 5.27544 17.1612 5.32725 17.1706 5.38162C17.18 5.43599 17.178 5.49174 17.1646 5.54528C17.1509 5.59628 17.1268 5.64389 17.0939 5.68518C17.0611 5.72647 17.0201 5.76055 16.9736 5.78532C16.9271 5.81008 16.876 5.82501 16.8235 5.82917C16.771 5.83332 16.7183 5.82663 16.6685 5.80949C16.3144 5.75063 15.9764 5.61805 15.6765 5.42029C15.3765 5.22253 15.1211 4.96397 14.9267 4.66122L14.876 4.57992L14.1267 4.64089V5.5148C14.7039 5.5148 14.7444 5.60625 14.7444 6.2058V7.86215H15.0279C15.6153 7.86215 16.2026 7.86215 16.79 7.86215C17.114 7.86215 17.2558 8.00442 17.2558 8.32959C17.2558 9.19334 17.2558 10.0571 17.2558 10.931C17.2558 11.246 17.114 11.3883 16.79 11.3883H16.4153C16.4153 11.5 16.4153 11.5813 16.4153 11.6728C16.4153 13.4511 16.4153 15.2294 16.4153 17.0077C16.4086 17.086 16.4252 17.1645 16.4631 17.2332C16.501 17.302 16.5583 17.3579 16.6279 17.3939L17.0836 17.719C17.1309 17.7466 17.1717 17.784 17.2035 17.8288C17.2352 17.8735 17.257 17.9244 17.2676 17.9783C17.2781 18.0321 17.2771 18.0876 17.2646 18.141C17.2521 18.1944 17.2284 18.2446 17.195 18.2881C17.1634 18.3301 17.1236 18.3652 17.0779 18.3912C17.0323 18.4172 16.9818 18.4335 16.9297 18.4393C16.8775 18.445 16.8247 18.4399 16.7746 18.4244C16.7244 18.4089 16.678 18.3832 16.6381 18.349L16.4457 18.2271V19.4872C16.8193 19.1629 17.2748 18.9484 17.7621 18.8673C18.1459 18.8058 18.5388 18.8384 18.9074 18.9623C19.2759 19.0861 19.6091 19.2976 19.8786 19.5786C20.0001 19.7006 20.0811 19.7514 20.2533 19.6802C20.4639 19.6008 20.6882 19.5642 20.9131 19.5724C21.138 19.5806 21.3591 19.6335 21.5634 19.728C21.7678 19.8225 21.9515 19.9567 22.1038 20.123C22.2561 20.2892 22.374 20.4841 22.4508 20.6964C22.5216 20.8895 22.6128 20.9505 22.7951 20.92C22.8861 20.9149 22.9774 20.9149 23.0685 20.92C23.303 20.9226 23.5271 21.018 23.6921 21.1854C23.857 21.3529 23.9495 21.5788 23.9495 21.8142C23.9495 22.5357 23.9495 23.2673 23.9495 23.9888C23.9496 24.1097 23.9253 24.2294 23.878 24.3407C23.8307 24.4519 23.7614 24.5523 23.6743 24.6359C23.5872 24.7195 23.4842 24.7845 23.3713 24.827C23.2585 24.8695 23.1382 24.8885 23.0178 24.883H3.36205C2.77446 24.9032 2.19964 24.7081 1.74493 24.3341C1.29023 23.9601 0.986743 23.4328 0.891148 22.8507C0.876018 22.6647 0.876018 22.4778 0.891148 22.2918V11.4188H0.86077ZM8.04055 18.847C7.85427 18.9015 7.65697 18.9058 7.46851 18.8594C7.28006 18.813 7.10712 18.7176 6.96713 18.5828C6.83302 18.4385 6.73256 18.2661 6.67301 18.078C6.61346 17.89 6.59632 17.691 6.62282 17.4955C5.61016 17.4955 5.2051 17.1398 5.2051 16.144V11.4391H1.6304V22.2512C1.60693 22.4999 1.63848 22.7508 1.72281 22.9858C1.80713 23.2209 1.94213 23.4344 2.11819 23.6111C2.29425 23.7878 2.50701 23.9232 2.74128 24.0079C2.97555 24.0925 3.22558 24.1241 3.47346 24.1006H12.2735V21.9056C12.2572 21.7683 12.2722 21.629 12.3173 21.4982C12.3625 21.3675 12.4366 21.2488 12.5341 21.1509C12.6315 21.0531 12.7499 20.9788 12.8801 20.9335C13.0104 20.8882 13.1493 20.8731 13.2862 20.8895H15.7165C15.7165 19.8733 15.7165 18.7962 15.7165 17.7698C15.6972 17.6781 15.6427 17.5978 15.5647 17.5463L11.8482 14.9652C11.7839 14.9193 11.7162 14.8785 11.6456 14.8433V15.1481C11.6456 16.022 11.6456 16.8858 11.6456 17.7597C11.6568 18.0431 11.5571 18.3196 11.368 18.5304C11.1788 18.7411 10.915 18.8693 10.633 18.8876C10.5015 18.8977 10.3695 18.8977 10.238 18.8876C10.0153 19.5278 9.67094 19.8225 9.15448 19.8327C8.63802 19.8428 8.30384 19.4872 8.04055 18.847ZM11.595 8.68526H5.99498C5.99498 8.79704 5.99498 8.88849 5.99498 8.96978V16.2253C5.98993 16.3099 5.98993 16.3947 5.99498 16.4793C5.99498 16.652 6.15701 16.7638 6.31904 16.7232C6.43885 16.668 6.54064 16.5799 6.61269 16.4691C6.61269 16.4691 6.61269 16.3065 6.61269 16.2253V13.9287C6.6021 13.8444 6.6021 13.759 6.61269 13.6747C6.63247 13.5898 6.68027 13.5142 6.74829 13.4601C6.81632 13.406 6.90059 13.3765 6.9874 13.3765C7.07421 13.3765 7.15845 13.406 7.22647 13.4601C7.29449 13.5142 7.34229 13.5898 7.36208 13.6747C7.37254 13.7658 7.37254 13.8579 7.36208 13.949V17.6072C7.36208 17.6885 7.36208 17.78 7.36208 17.8308C7.42327 17.948 7.51865 18.0438 7.63549 18.1052C7.67222 18.1157 7.7108 18.1182 7.74856 18.1123C7.78633 18.1064 7.82236 18.0923 7.85416 18.071C7.88596 18.0498 7.91278 18.0218 7.93272 17.9891C7.95267 17.9564 7.96529 17.9197 7.96968 17.8816C7.97972 17.7904 7.97972 17.6984 7.96968 17.6072C7.96968 16.3878 7.96968 15.1786 7.96968 13.9693C7.96461 13.8847 7.96461 13.7999 7.96968 13.7153C7.97099 13.6686 7.98146 13.6226 8.00049 13.5799C8.01953 13.5373 8.04674 13.4988 8.0806 13.4668C8.11445 13.4347 8.15427 13.4096 8.19779 13.393C8.2413 13.3763 8.28768 13.3685 8.33423 13.3698C8.42939 13.3613 8.5241 13.3906 8.59796 13.4514C8.67182 13.5122 8.71894 13.5997 8.72918 13.695C8.73422 13.7762 8.73422 13.8576 8.72918 13.9389V18.471C8.72918 18.5523 8.72918 18.6539 8.72918 18.7149C8.72918 18.7758 8.92156 18.9588 9.02283 18.9588C9.1241 18.9588 9.25574 18.847 9.33675 18.7454C9.41777 18.6437 9.33675 18.5726 9.33675 18.4812V14.0202C9.3265 13.912 9.3265 13.8031 9.33675 13.695C9.34068 13.639 9.35774 13.5848 9.38654 13.5367C9.41534 13.4887 9.45506 13.4481 9.50246 13.4184C9.54986 13.3887 9.60359 13.3706 9.65927 13.3657C9.71495 13.3608 9.771 13.3692 9.82284 13.3901C9.8694 13.3874 9.91603 13.3939 9.96009 13.4093C10.0041 13.4246 10.0448 13.4486 10.0796 13.4798C10.1144 13.5109 10.1427 13.5486 10.1629 13.5908C10.1832 13.633 10.1949 13.6787 10.1975 13.7255C10.2028 13.8101 10.2028 13.8949 10.1975 13.9795V17.6174C10.1878 17.6984 10.1878 17.7803 10.1975 17.8613C10.2013 17.9014 10.213 17.9404 10.2319 17.9759C10.2508 18.0115 10.2765 18.0429 10.3076 18.0684C10.3387 18.0939 10.3745 18.113 10.413 18.1245C10.4514 18.1361 10.4918 18.1399 10.5317 18.1357C10.6096 18.1357 10.6843 18.1046 10.7394 18.0493C10.7944 17.9941 10.8254 17.9191 10.8254 17.841C10.8306 17.7665 10.8306 17.6918 10.8254 17.6174V12.8414C10.8254 12.3841 11.0785 12.2215 11.4735 12.4248C11.4735 12.4248 11.4735 12.4248 11.5241 12.4248L11.595 8.68526ZM18.1469 21.6821C16.5469 21.6821 14.9469 21.6821 13.3368 21.6821C13.1241 21.6821 13.0431 21.7431 13.0532 21.9666C13.0532 22.5763 13.0532 23.1962 13.0532 23.8059C13.0532 24.0193 13.1039 24.1107 13.3368 24.1107H22.9267C23.1697 24.1107 23.2305 24.0193 23.2204 23.7957C23.2204 23.1996 23.2204 22.6 23.2204 21.9971C23.2204 21.7634 23.1596 21.6821 22.9065 21.6821C21.3267 21.6923 19.7368 21.6821 18.1469 21.6821ZM15.676 11.4391H12.3849C12.3849 11.8557 12.3849 12.2723 12.3849 12.6788C12.4284 12.9265 12.3989 13.1816 12.3 13.4126C12.201 13.6437 12.037 13.8408 11.8279 13.9795L15.676 16.713V11.4391ZM10.0861 3.81779H10.1368C11.4735 3.81779 12.8102 3.81779 14.1368 3.81779C14.174 3.81142 14.2091 3.79607 14.2391 3.77306C14.2691 3.75005 14.2931 3.72006 14.3089 3.68569C14.4484 3.32947 14.7011 3.02942 15.0279 2.8321C15.0542 2.81485 15.0767 2.79245 15.0941 2.76626C15.1115 2.74007 15.1234 2.71062 15.1292 2.67968C15.1292 2.31386 15.1292 1.94804 15.1292 1.60254C14.1165 1.60254 13.1545 1.53141 12.1925 1.60254C11.647 1.68688 11.1447 1.94987 10.7637 2.35055C10.3827 2.75123 10.1445 3.2671 10.0861 3.81779ZM8.57726 7.88248C8.57183 7.78772 8.57183 7.69272 8.57726 7.59795C8.59317 7.4187 8.56935 7.23814 8.50751 7.06921C8.44566 6.90029 8.34732 6.74719 8.21954 6.62092C8.09176 6.49464 7.93773 6.39831 7.76847 6.33884C7.5992 6.27937 7.41893 6.25822 7.24056 6.27693C6.04562 6.27693 4.86081 6.27693 3.66587 6.27693C3.46523 6.26718 3.26529 6.30708 3.08363 6.3931C2.90198 6.47913 2.74416 6.60865 2.62412 6.77026C2.50408 6.93187 2.4255 7.12061 2.39525 7.31988C2.365 7.51914 2.38402 7.72282 2.45066 7.91296L8.57726 7.88248ZM0.77977 10.6261H5.19497V8.69541H0.77977V10.6261ZM16.5267 8.69541H12.4051V10.6261H16.5267V8.69541ZM10.9773 6.33789V7.87232H14.0153V6.33789H10.9773ZM16.5469 20.8895H21.7318C21.6666 20.7667 21.5771 20.6586 21.4686 20.5719C21.3602 20.4853 21.2352 20.4219 21.1013 20.3857C20.9674 20.3495 20.8276 20.3412 20.6904 20.3615C20.5533 20.3817 20.4217 20.43 20.3039 20.5033C19.9596 20.7371 19.8178 20.7167 19.5849 20.3712C19.4353 20.1351 19.2289 19.9406 18.9846 19.8057C18.7404 19.6707 18.4663 19.5996 18.1874 19.5989C17.8069 19.588 17.4344 19.711 17.1348 19.9468C16.8352 20.1825 16.6272 20.516 16.5469 20.8895ZM6.25828 1.96836H4.75953L4.65826 3.69585H6.40004L6.25828 1.96836ZM4.04055 5.44367H6.94688V4.48846H4.04055V5.44367ZM11.757 4.64089V5.53512H13.3368V4.62057H11.9393L11.757 4.64089ZM16.4254 1.59237H15.8988V2.60855H16.395L16.4254 1.59237Z"
      fill="currentColor"
    />
    <path
      d="M21.2855 5.26076C21.8207 5.25875 22.3444 5.41616 22.7904 5.71307C23.2364 6.00997 23.5845 6.433 23.7907 6.92859C23.9969 7.42418 24.0519 7.97002 23.9487 8.497C23.8455 9.02397 23.5888 9.50837 23.2111 9.88883C22.8334 10.2693 22.3516 10.5287 21.8268 10.6342C21.3021 10.7397 20.7579 10.6866 20.2633 10.4816C19.7686 10.2765 19.3458 9.92875 19.0482 9.48235C18.7507 9.03595 18.5918 8.51099 18.5918 7.97394C18.5905 7.61812 18.6592 7.26554 18.7939 6.93642C18.9287 6.6073 19.1269 6.30812 19.3772 6.05605C19.6274 5.80397 19.9248 5.60396 20.2523 5.46748C20.5798 5.33101 20.9309 5.26076 21.2855 5.26076ZM19.3614 7.96378C19.3615 8.34678 19.4744 8.72122 19.6861 9.03994C19.8977 9.35866 20.1986 9.60742 20.5509 9.7549C20.9031 9.90238 21.291 9.94198 21.6656 9.86872C22.0402 9.79547 22.3849 9.61263 22.6562 9.34322C22.9274 9.07381 23.1132 8.72988 23.1901 8.35473C23.2671 7.97958 23.2316 7.58999 23.0883 7.23501C22.945 6.88002 22.7003 6.57552 22.3849 6.35983C22.0695 6.14413 21.6975 6.0269 21.3159 6.02289C21.0575 6.02288 20.8017 6.07427 20.5632 6.1741C20.3248 6.27393 20.1084 6.42021 19.9267 6.60448C19.7449 6.78875 19.6014 7.00736 19.5043 7.24766C19.4073 7.48795 19.3588 7.74517 19.3614 8.00442V7.96378Z"
      fill="currentColor"
    />
    <path
      d="M20.9615 13.6745C20.9588 14.0402 20.8129 14.3902 20.5552 14.6488C20.2975 14.9074 19.9487 15.0538 19.5843 15.0565C19.2197 15.0486 18.8724 14.8991 18.6155 14.6394C18.3586 14.3797 18.2122 14.0302 18.207 13.6643C18.2124 13.2986 18.3609 12.9497 18.6205 12.693C18.8801 12.4363 19.2299 12.2924 19.5944 12.2925C19.9579 12.2952 20.3056 12.442 20.5617 12.7008C20.8178 12.9597 20.9615 13.3097 20.9615 13.6745ZM20.1817 13.6745C20.1928 13.5899 20.1857 13.5039 20.161 13.4223C20.1363 13.3407 20.0945 13.2653 20.0384 13.2011C19.9823 13.137 19.9133 13.0856 19.8358 13.0504C19.7584 13.0152 19.6743 12.997 19.5893 12.997C19.5043 12.997 19.4203 13.0152 19.3428 13.0504C19.2654 13.0856 19.1963 13.137 19.1402 13.2011C19.0842 13.2653 19.0424 13.3407 19.0176 13.4223C18.9929 13.5039 18.9859 13.5899 18.9969 13.6745C18.9859 13.7591 18.9929 13.845 19.0176 13.9267C19.0424 14.0083 19.0842 14.0837 19.1402 14.1478C19.1963 14.2119 19.2654 14.2633 19.3428 14.2985C19.4203 14.3337 19.5043 14.3519 19.5893 14.3519C19.6743 14.3519 19.7584 14.3337 19.8358 14.2985C19.9133 14.2633 19.9823 14.2119 20.0384 14.1478C20.0945 14.0837 20.1363 14.0083 20.161 13.9267C20.1857 13.845 20.1928 13.7591 20.1817 13.6745Z"
      fill="currentColor"
    />
    <path
      d="M22.7135 7.97387C22.7176 8.02461 22.7117 8.07568 22.6962 8.12413C22.6806 8.17257 22.6556 8.21745 22.6226 8.25617C22.5897 8.29489 22.5494 8.32669 22.5042 8.34975C22.459 8.3728 22.4096 8.38665 22.3591 8.3905C22.309 8.39611 22.2584 8.39153 22.2101 8.37706C22.1619 8.36259 22.1171 8.3385 22.0783 8.30625C22.0396 8.27401 22.0077 8.23425 21.9846 8.18937C21.9615 8.14448 21.9476 8.09538 21.9439 8.045C21.9452 7.95474 21.9286 7.86512 21.8952 7.78134C21.8617 7.69756 21.8119 7.6213 21.7488 7.55699C21.6856 7.49269 21.6103 7.44163 21.5274 7.40678C21.4444 7.37194 21.3553 7.35399 21.2654 7.354C21.2126 7.35208 21.1609 7.33868 21.1138 7.31474C21.0668 7.29079 21.0254 7.25687 20.9927 7.21532C20.9599 7.17377 20.9366 7.12558 20.9242 7.07408C20.9119 7.02258 20.9108 6.969 20.9211 6.91705C20.9384 6.81651 20.9929 6.72622 21.0736 6.66409C21.1542 6.60197 21.2552 6.57256 21.3565 6.58172C21.7151 6.60182 22.0532 6.75583 22.3043 7.01346C22.5554 7.2711 22.7014 7.61369 22.7135 7.97387Z"
      fill="currentColor"
    />
  </svg>
);
export const CabIcon: React.FC<IconInterface> = ({ className = "size-4" }) => (
  <svg
    className={className}
    viewBox="0 0 26 13"
    fill="currentColor"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M24.9977 8.90863C25.0008 8.80053 24.9785 8.6932 24.9325 8.59533C24.8864 8.49747 24.818 8.4118 24.7328 8.34528C24.6252 8.27347 24.5132 8.20852 24.3974 8.15079V7.3678C24.4041 6.97933 24.2757 6.60059 24.0341 6.2963C23.7925 5.99202 23.4528 5.78108 23.0729 5.69955C21.7014 5.39608 20.3282 5.10267 18.9551 4.8059C18.8589 4.79312 18.7717 4.74316 18.712 4.66675C18.4571 4.33142 18.1922 4.01453 17.9374 3.68088C17.4322 2.99692 16.7193 2.49465 15.9053 2.24904C15.5699 2.15012 15.2346 2.1082 14.8691 2.03611C14.8691 1.92042 14.8775 1.77622 14.8691 1.63371C14.8533 1.34212 14.7245 1.06815 14.5101 0.869882C14.2957 0.671614 14.0125 0.564633 13.7206 0.57161C13.4286 0.578587 13.1509 0.698976 12.9462 0.907259C12.7415 1.11554 12.626 1.39535 12.6241 1.68736V2.07634C11.9534 2.07634 11.2929 2.08473 10.6373 2.07634C10.0534 2.0536 9.47176 2.16233 8.93548 2.39448C8.39919 2.62664 7.92191 2.97631 7.53887 3.41765C6.95373 4.06483 6.38032 4.71872 5.79853 5.36758C5.7537 5.41232 5.69408 5.43914 5.63086 5.44302C5.39781 5.44302 5.1614 5.44302 4.92667 5.44302C4.30632 5.44302 3.68597 5.43464 3.06561 5.44302C2.81396 5.45938 2.57587 5.5629 2.39228 5.73578C2.20869 5.90866 2.09106 6.14011 2.05963 6.39033C2.03281 6.66529 2.04119 6.94361 2.03784 7.22026C2.03448 7.4969 2.03784 7.75678 2.03784 8.04348C1.91377 8.04348 1.80143 8.04348 1.68742 8.04348C1.50261 8.05765 1.32972 8.14026 1.20259 8.27514C1.07546 8.41003 1.00322 8.58749 1 8.77282V9.4988C1 10.0169 1.30347 10.3204 1.82323 10.3204H4.40524C4.48112 10.738 4.66953 11.1269 4.95015 11.4454H2.51065C2.43569 11.4404 2.36105 11.4589 2.29707 11.4983C2.23308 11.5376 2.18292 11.5959 2.15353 11.665C2.12566 11.7227 2.1132 11.7865 2.11738 11.8504C2.12155 11.9143 2.1422 11.976 2.17733 12.0296C2.21245 12.0831 2.26084 12.1266 2.31779 12.1559C2.37474 12.1851 2.43829 12.1991 2.50226 12.1965C3.17292 12.1965 3.84357 12.1965 4.51422 12.1965H23.4401C23.4965 12.2 23.5531 12.1943 23.6077 12.1797C23.6959 12.1557 23.7721 12.1 23.8219 12.0234C23.8717 11.9467 23.8915 11.8545 23.8776 11.7641C23.8637 11.6738 23.8171 11.5917 23.7466 11.5336C23.6761 11.4754 23.5867 11.4452 23.4954 11.4487C22.8583 11.4487 22.2228 11.4487 21.5857 11.4487H21.4281C21.7081 11.1299 21.8964 10.7411 21.973 10.3237H22.6621H23.3328C23.653 10.3237 23.9732 10.3237 24.2918 10.3153C24.3869 10.3129 24.4805 10.2918 24.5674 10.2532C24.6542 10.2145 24.7326 10.1591 24.798 10.0901C24.8635 10.0211 24.9146 9.93987 24.9486 9.85106C24.9826 9.76226 24.9987 9.66762 24.996 9.57257C25.001 9.34623 25.001 9.12324 24.9977 8.90863ZM13.3803 1.63707C13.3908 1.54687 13.4341 1.4637 13.502 1.40334C13.5698 1.34297 13.6575 1.30963 13.7483 1.30963C13.8391 1.30963 13.9267 1.34297 13.9946 1.40334C14.0624 1.4637 14.1057 1.54687 14.1163 1.63707C14.1297 1.7712 14.1163 1.90868 14.1163 2.0512H13.3803C13.373 1.91325 13.373 1.77501 13.3803 1.63707ZM6.61505 11.442C6.31867 11.44 6.02954 11.3502 5.78419 11.1839C5.53885 11.0177 5.34831 10.7824 5.23665 10.5078C5.125 10.2333 5.09724 9.93179 5.15689 9.64147C5.21654 9.35116 5.36092 9.08504 5.57177 8.87676C5.78263 8.66847 6.05051 8.52738 6.34154 8.47131C6.63257 8.41523 6.93369 8.44669 7.20684 8.56172C7.47999 8.67674 7.71292 8.87016 7.87617 9.11753C8.03942 9.3649 8.12567 9.65512 8.12402 9.9515C8.12292 10.1486 8.08295 10.3436 8.0064 10.5252C7.92985 10.7069 7.81821 10.8717 7.67789 11.0101C7.53757 11.1486 7.37132 11.258 7.18865 11.3321C7.00599 11.4062 6.81049 11.4436 6.61337 11.442H6.61505ZM8.32689 11.442C8.44174 11.2726 8.54698 11.0968 8.6421 10.9156C8.72256 10.7205 8.79252 10.5212 8.85168 10.3187H17.5081C17.569 10.5191 17.6406 10.7161 17.7227 10.9089C17.8201 11.0921 17.9269 11.27 18.043 11.442H8.32689ZM19.7481 11.442C19.4515 11.442 19.1616 11.354 18.915 11.1892C18.6685 11.0243 18.4763 10.79 18.363 10.5159C18.2496 10.2419 18.2202 9.9403 18.2783 9.64945C18.3364 9.3586 18.4795 9.09153 18.6894 8.88204C18.8994 8.67254 19.1668 8.53005 19.4578 8.47259C19.7487 8.41514 20.0502 8.4453 20.3241 8.55926C20.5979 8.67322 20.8318 8.86587 20.9961 9.1128C21.1604 9.35974 21.2477 9.64987 21.247 9.94647C21.2457 10.3427 21.0877 10.7223 20.8075 11.0025C20.5273 11.2827 20.1477 11.4407 19.7515 11.442H19.7481ZM24.2432 9.56419H21.9881C21.9297 9.36873 21.8603 9.17673 21.7802 8.98911C21.6074 8.61898 21.3367 8.3031 20.9974 8.07561C20.6582 7.84812 20.2632 7.71767 19.8551 7.69834C19.4471 7.67901 19.0415 7.77153 18.6823 7.96593C18.323 8.16032 18.0237 8.44919 17.8166 8.80132C17.7048 9.01795 17.6171 9.24623 17.5551 9.48204C17.5316 9.5491 17.5098 9.56922 17.4444 9.56922C16.8978 9.56922 16.3513 9.56922 15.8047 9.56922H9.747C9.47203 9.56922 9.19874 9.56922 8.92545 9.56922C8.85838 9.56922 8.83323 9.55078 8.81982 9.48204C8.72677 9.03764 8.50174 8.63167 8.17417 8.31726C7.8466 8.00286 7.43175 7.79465 6.98391 7.7199C6.53586 7.64483 6.07557 7.7085 5.66473 7.90238C5.25389 8.09627 4.91213 8.4111 4.68524 8.80468C4.56143 9.05098 4.45281 9.30462 4.35998 9.56419H1.74946V8.78791H3.33387C3.38606 8.7936 3.43886 8.78834 3.48889 8.77246C3.53893 8.75658 3.5851 8.73044 3.62445 8.69569C3.6638 8.66095 3.69547 8.61837 3.71743 8.57069C3.73939 8.52301 3.75116 8.47128 3.75198 8.41879C3.7528 8.3663 3.74265 8.31421 3.72219 8.26587C3.70173 8.21753 3.67141 8.17399 3.63316 8.13803C3.59491 8.10208 3.54959 8.0745 3.50007 8.05706C3.45056 8.03963 3.39795 8.03272 3.34561 8.03678C3.17795 8.03678 2.99352 8.03678 2.81747 8.03678C2.81747 8.03678 2.80406 8.03678 2.79064 8.02337C2.78883 7.99489 2.78883 7.96633 2.79064 7.93786V6.59655C2.78597 6.54077 2.7936 6.48464 2.813 6.43213C2.8324 6.37963 2.8631 6.33202 2.90292 6.29268C2.94274 6.25334 2.99071 6.22322 3.04345 6.20446C3.09619 6.18569 3.1524 6.17875 3.20812 6.1841C4.06824 6.1841 4.92835 6.1841 5.78679 6.1841C5.87968 6.18865 5.97233 6.17111 6.05714 6.13293C6.14195 6.09475 6.21651 6.03702 6.27469 5.96446C6.88834 5.26195 7.51037 4.56615 8.1324 3.86867C8.55017 3.39252 9.10759 3.06041 9.72521 2.91969C9.98988 2.85613 10.2611 2.82405 10.5333 2.82412C11.9674 2.8163 13.4009 2.8163 14.8339 2.82412C15.3281 2.82492 15.8142 2.95004 16.2473 3.18795C16.6534 3.40778 17.0079 3.7119 17.2868 4.07992C17.597 4.4907 17.9256 4.88974 18.2425 5.29548C18.3247 5.40236 18.4447 5.47373 18.5778 5.495C20.0153 5.8035 21.4527 6.11536 22.8901 6.43056C23.109 6.47288 23.3054 6.59222 23.4438 6.76693C23.5823 6.94164 23.6535 7.16017 23.6446 7.38289V8.16755H23.2992C23.2493 8.16553 23.1995 8.17354 23.1527 8.19111C23.1059 8.20868 23.0631 8.23545 23.0268 8.26984C22.9906 8.30422 22.9616 8.34553 22.9416 8.39132C22.9215 8.43711 22.9109 8.48644 22.9103 8.53642C22.9083 8.58741 22.917 8.63826 22.9358 8.68573C22.9545 8.73319 22.9829 8.77625 23.0192 8.81218C23.0554 8.84811 23.0987 8.87613 23.1464 8.89446C23.194 8.91279 23.2449 8.92104 23.2959 8.91869C23.5692 8.91869 23.8425 8.91869 24.1174 8.91869H24.2465L24.2432 9.56419Z"
      fill="currentColor"
      stroke="currentColor"
      strokeWidth="0.2"
      strokeMiterlimit="10"
    />
    <path
      d="M17.4949 5.54515C17.297 5.29868 17.1026 5.04216 16.9047 4.8024C16.7116 4.52775 16.4814 4.28117 16.2206 4.0697C15.8212 3.75643 15.3315 3.58007 14.824 3.56671C13.3637 3.56671 11.9016 3.55833 10.4413 3.56671C9.85151 3.56484 9.28404 3.79207 8.85855 4.20049C8.65735 4.38995 8.46453 4.58946 8.27005 4.78563C8.01855 5.04048 7.76706 5.297 7.51892 5.55688C7.46907 5.6047 7.43463 5.6663 7.41999 5.7338C7.40536 5.8013 7.41119 5.87163 7.43676 5.9358C7.45701 6.00184 7.49599 6.0606 7.54896 6.10494C7.60193 6.14928 7.66662 6.17731 7.7352 6.18562C7.79377 6.19067 7.85267 6.19067 7.91125 6.18562H13.652C14.829 6.18562 16.0027 6.18562 17.173 6.18562C17.2513 6.19424 17.3304 6.17845 17.3994 6.14039C17.4684 6.10233 17.524 6.04388 17.5585 5.97303C17.593 5.90218 17.6048 5.82239 17.5922 5.7446C17.5796 5.6668 17.5433 5.5948 17.4882 5.53844L17.4949 5.54515ZM12.1297 5.43617H8.68585C8.86693 5.24838 9.03795 5.07904 9.20058 4.90299C9.38065 4.70437 9.60279 4.54847 9.8508 4.44666C10.0988 4.34484 10.3664 4.29969 10.6341 4.3145C11.1254 4.32623 11.6166 4.3145 12.1179 4.3145L12.1297 5.43617ZM12.8992 5.43617V4.31953C12.9272 4.31813 12.9551 4.31813 12.9831 4.31953C13.5732 4.31953 14.1567 4.31953 14.7519 4.31953C15.0115 4.31632 15.2682 4.37429 15.5013 4.48877C15.7343 4.60325 15.9371 4.771 16.0932 4.97844C16.2106 5.12431 16.3263 5.27018 16.4587 5.43952L12.8992 5.43617Z"
      fill="currentColor"
      stroke="currentColor"
      strokeWidth="0.2"
      strokeMiterlimit="10"
    />
    <path
      d="M16.1091 8.23792C16.0682 8.24947 16.0258 8.25512 15.9833 8.25469H10.0061C9.9548 8.25812 9.90332 8.25098 9.85488 8.2337C9.80643 8.21642 9.76205 8.18938 9.72449 8.15425C9.68692 8.11911 9.65698 8.07664 9.63651 8.02946C9.61603 7.98228 9.60547 7.93139 9.60547 7.87996C9.60547 7.82853 9.61603 7.77764 9.63651 7.73046C9.65698 7.68328 9.68692 7.6408 9.72449 7.60567C9.76205 7.57054 9.80643 7.54349 9.85488 7.52622C9.90332 7.50894 9.9548 7.5018 10.0061 7.50523C10.1151 7.50523 10.2241 7.50523 10.3331 7.50523H13.0056C13.9982 7.50523 14.9891 7.50523 15.9816 7.50523C16.0719 7.50285 16.16 7.53307 16.2298 7.59035C16.2996 7.64764 16.3464 7.72815 16.3617 7.81714C16.377 7.90613 16.3597 7.99765 16.313 8.07493C16.2663 8.15221 16.1933 8.21007 16.1074 8.23792H16.1091Z"
      fill="currentColor"
      stroke="currentColor"
      strokeWidth="0.2"
      strokeMiterlimit="10"
    />
    <path
      d="M7.36799 9.96175C7.36598 10.11 7.32002 10.2544 7.23594 10.3765C7.15186 10.4987 7.03343 10.5932 6.89564 10.648C6.75786 10.7028 6.6069 10.7155 6.46189 10.6845C6.31687 10.6535 6.18431 10.5802 6.08099 10.4738C5.97767 10.3674 5.90823 10.2328 5.88146 10.0869C5.85468 9.94106 5.87178 9.79053 5.93059 9.6544C5.98939 9.51827 6.08727 9.40264 6.21182 9.32215C6.33636 9.24166 6.48199 9.19993 6.63026 9.20224C6.72881 9.20288 6.82624 9.22312 6.91688 9.26179C7.00752 9.30046 7.08956 9.35678 7.15822 9.42747C7.22688 9.49816 7.28079 9.5818 7.3168 9.67353C7.35281 9.76526 7.37021 9.86324 7.36799 9.96175Z"
      fill="currentColor"
      stroke="currentColor"
      strokeWidth="0.2"
      strokeMiterlimit="10"
    />
    <path
      d="M20.4972 9.98496C20.489 10.1333 20.437 10.2759 20.3477 10.3947C20.2584 10.5135 20.1359 10.6032 19.9957 10.6524C19.8555 10.7015 19.7038 10.708 19.5599 10.6709C19.416 10.6339 19.2863 10.5549 19.1873 10.4442C19.0882 10.3334 19.0243 10.1957 19.0035 10.0486C18.9827 9.90142 19.006 9.75142 19.0705 9.61754C19.135 9.48366 19.2378 9.37192 19.3658 9.29645C19.4938 9.22098 19.6413 9.18519 19.7897 9.19358C19.8883 9.1985 19.985 9.22294 20.0741 9.26549C20.1632 9.30804 20.2429 9.36785 20.3088 9.44147C20.3746 9.51508 20.4251 9.60102 20.4575 9.69432C20.4898 9.78761 20.5033 9.8864 20.4972 9.98496Z"
      fill="currentColor"
      stroke="currentColor"
      strokeWidth="0.2"
      strokeMiterlimit="10"
    />
  </svg>
);
export const InventoryIcon: React.FC<IconInterface> = ({
  className = "size-4",
}) => (
  <svg
    className={className}
    viewBox="0 0 24 23"
    fill="currentColor"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M2.7426 19.2254V10.0954C2.10936 10.0025 1.51782 9.72417 1.0426 9.29545C0.7324 9.01392 0.483511 8.6715 0.311463 8.28956C0.139415 7.90761 0.0478961 7.49432 0.0426029 7.07544C-0.0313635 6.73772 -0.00868286 6.38598 0.108048 6.06055C0.224779 5.73512 0.43084 5.44916 0.702607 5.23545L4.1226 1.23545C4.18213 1.15529 4.25881 1.08944 4.34705 1.04273C4.43529 0.996015 4.53284 0.969606 4.6326 0.965444L18.2026 0.885442C18.3283 0.878431 18.4537 0.903152 18.5672 0.957327C18.6808 1.0115 18.779 1.0934 18.8526 1.19546C20.0726 2.74546 21.3026 4.27544 22.5226 5.82544C22.6291 5.97078 22.6885 6.1453 22.6926 6.32544C22.7119 6.82135 22.675 7.31785 22.5826 7.80546C22.4526 8.32016 22.1904 8.792 21.8221 9.17434C21.4538 9.55668 20.9921 9.83627 20.4826 9.98545L20.3226 10.0355V11.0355C20.7226 10.7555 21.0526 10.9354 21.3826 11.1354C22.1026 11.5554 22.8226 11.9754 23.5526 12.3854C23.6894 12.4551 23.8029 12.5632 23.8793 12.6964C23.9557 12.8296 23.9916 12.9822 23.9826 13.1354C23.9826 14.0454 23.9826 14.9554 23.9826 15.8554C23.9916 16.0087 23.9557 16.1613 23.8793 16.2945C23.8029 16.4277 23.6894 16.5357 23.5526 16.6054L21.2426 17.9854C21.1033 18.0827 20.9375 18.1348 20.7676 18.1348C20.5977 18.1348 20.4319 18.0827 20.2926 17.9854V19.1954H20.7526C21.169 19.1954 21.5683 19.3608 21.8628 19.6553C22.1572 19.9497 22.3226 20.3491 22.3226 20.7654C22.3226 21.1818 22.1572 21.5812 21.8628 21.8756C21.5683 22.17 21.169 22.3355 20.7526 22.3355H2.0726C1.70437 22.3603 1.33915 22.2547 1.04106 22.0371C0.742978 21.8194 0.531102 21.5038 0.442612 21.1455C0.384498 20.9235 0.376375 20.6914 0.418854 20.4659C0.461333 20.2404 0.553351 20.0272 0.688248 19.8416C0.823145 19.656 0.997558 19.5027 1.19891 19.3926C1.40027 19.2826 1.62354 19.2187 1.8526 19.2055C2.1426 19.2155 2.4826 19.2254 2.7426 19.2254ZM19.5026 19.2254C19.5026 18.7054 19.5026 18.2255 19.5026 17.6755C19.4932 17.6373 19.4759 17.6016 19.4518 17.5706C19.4276 17.5396 19.3973 17.514 19.3626 17.4955C18.8926 17.2155 18.4326 16.9454 17.9626 16.6854C17.8204 16.618 17.7017 16.5096 17.6215 16.3742C17.5413 16.2388 17.5034 16.0825 17.5126 15.9255C17.5126 15.0155 17.5126 14.1054 17.5126 13.2055C17.5049 13.0524 17.5413 12.9003 17.6176 12.7674C17.6938 12.6344 17.8066 12.5261 17.9426 12.4555C18.4226 12.1954 18.9426 11.9154 19.3626 11.6354C19.4226 11.6354 19.5026 11.5255 19.5026 11.4755C19.5026 11.0355 19.5026 10.5955 19.5026 10.1455C18.9504 10.1338 18.4105 9.9794 17.9356 9.69727C17.4607 9.41514 17.0669 9.01489 16.7926 8.53545C16.5185 9.01783 16.1215 9.41896 15.6419 9.69802C15.1624 9.97708 14.6174 10.1241 14.0626 10.1241C13.5078 10.1241 12.9629 9.97708 12.4833 9.69802C12.0038 9.41896 11.6067 9.01783 11.3326 8.53545C11.0597 9.01139 10.6672 9.40779 10.194 9.68543C9.72086 9.96307 9.18336 10.1123 8.63477 10.1184C8.08617 10.1244 7.54551 9.9871 7.0663 9.71999C6.58709 9.45288 6.18595 9.06524 5.9026 8.59545C5.5871 8.94615 5.25331 9.27995 4.9026 9.59545C4.51052 9.88724 4.04821 10.0701 3.56261 10.1254V19.2354H6.8826C6.8826 19.1254 6.8826 19.0355 6.8826 18.9355C6.90325 18.0864 7.25249 17.2784 7.85678 16.6815C8.46108 16.0847 9.27332 15.7456 10.1226 15.7354C10.9426 15.7354 11.7726 15.7354 12.6026 15.7354C13.3567 15.7453 14.0842 16.0155 14.6619 16.5002C15.2396 16.985 15.632 17.6545 15.7726 18.3955C15.7726 18.6655 15.8326 18.9454 15.8626 19.2254H19.5026ZM11.3326 20.0154H1.86261C1.68405 20.0334 1.51737 20.1132 1.39139 20.241C1.2654 20.3688 1.18802 20.5367 1.17261 20.7155C1.12261 21.2455 1.48261 21.6054 2.08261 21.6054H20.7626C20.8807 21.5993 20.9959 21.5666 21.0997 21.51C21.2036 21.4533 21.2933 21.3741 21.3624 21.2781C21.4315 21.1822 21.4782 21.0719 21.499 20.9555C21.5198 20.839 21.5142 20.7194 21.4826 20.6054C21.4329 20.4121 21.3135 20.2439 21.1474 20.1332C20.9813 20.0225 20.7802 19.977 20.5826 20.0054L11.3326 20.0154ZM6.73261 5.76545H15.9226C15.1589 4.59852 14.6291 3.29438 14.3626 1.92545C14.3626 1.73545 14.2426 1.68545 14.0626 1.68545H9.06261H8.2926C8.0626 2.41545 7.8826 3.12545 7.6226 3.78545C7.36261 4.44545 7.04261 5.10544 6.73261 5.74544V5.76545ZM15.0226 19.2254C15.0264 19.1922 15.0264 19.1587 15.0226 19.1255C15.0471 18.461 14.809 17.8136 14.3599 17.3232C13.9108 16.8329 13.2867 16.5392 12.6226 16.5055C11.7726 16.5055 10.9226 16.5055 10.0626 16.5055C9.54633 16.5147 9.0457 16.6843 8.63005 16.9907C8.2144 17.297 7.90427 17.7251 7.7426 18.2155C7.62133 18.534 7.58346 18.8782 7.6326 19.2155H8.6326C8.6326 18.8555 8.78261 18.7054 9.00261 18.6954C9.22261 18.6854 9.35261 18.8155 9.44261 19.2155H13.2126C13.2726 18.8355 13.3826 18.7054 13.6126 18.6954C13.8426 18.6854 13.9526 18.8155 14.0326 19.2155L15.0226 19.2254ZM15.1126 1.66544V1.79545C15.384 3.17973 15.9501 4.48938 16.7726 5.63544C16.8105 5.68929 16.8618 5.73236 16.9214 5.76041C16.9811 5.78846 17.0469 5.80054 17.1126 5.79545H21.4826C21.4602 5.74963 21.4334 5.70609 21.4026 5.66544L18.3126 1.78545C18.2794 1.74918 18.2393 1.71987 18.1947 1.69927C18.15 1.67867 18.1017 1.66716 18.0526 1.66544H15.1226H15.1126ZM1.2426 5.79545H5.48261C5.55957 5.80014 5.63639 5.78405 5.705 5.74886C5.77362 5.71367 5.8315 5.66069 5.8726 5.59545C6.58535 4.58918 7.09798 3.45527 7.3826 2.25545C7.3826 2.09545 7.46261 1.92545 7.50261 1.73545H7.36261H4.92261C4.85298 1.72989 4.78311 1.74266 4.71996 1.77248C4.6568 1.80231 4.60255 1.84815 4.56261 1.90545C4.05261 2.51545 3.5626 3.12545 3.0126 3.72545L1.2426 5.79545ZM17.2426 6.57544C17.1606 7.05383 17.2268 7.54586 17.4323 7.98556C17.6379 8.42525 17.9729 8.79159 18.3926 9.03545C18.7809 9.27971 19.235 9.39828 19.6931 9.37502C20.1513 9.35176 20.5911 9.18778 20.9526 8.90545C21.3237 8.65248 21.6149 8.29887 21.7921 7.88614C21.9692 7.47342 22.0249 7.01872 21.9526 6.57544H17.2426ZM6.33261 6.57544C6.24858 7.02937 6.29837 7.49793 6.47592 7.92406C6.65348 8.35019 6.95113 8.71549 7.33261 8.97545C7.71228 9.24091 8.16435 9.38326 8.62761 9.38326C9.09087 9.38326 9.54294 9.24091 9.92261 8.97545C10.3032 8.71461 10.6001 8.3492 10.7775 7.92333C10.955 7.49746 11.0054 7.02932 10.9226 6.57544H6.33261ZM0.8526 6.57544C0.772251 7.06212 0.84445 7.56174 1.0593 8.00576C1.27414 8.44977 1.62112 8.81644 2.05261 9.05546C2.44545 9.29419 2.9027 9.40508 3.36125 9.37284C3.81981 9.3406 4.25705 9.1668 4.61261 8.87545C4.96043 8.61261 5.22863 8.25856 5.3875 7.85257C5.54636 7.44659 5.58966 7.00454 5.5126 6.57544H0.8526ZM16.4526 6.57544H11.7826C11.6996 7.04996 11.7645 7.53852 11.9684 7.97495C12.1723 8.41138 12.5054 8.77461 12.9226 9.01545C13.3017 9.2592 13.7463 9.38122 14.1967 9.36513C14.6471 9.34905 15.0819 9.19563 15.4426 8.92545C15.8071 8.66105 16.0903 8.29981 16.26 7.88268C16.4296 7.46555 16.4791 7.00923 16.4026 6.56545L16.4526 6.57544ZM18.3326 13.5754C18.3326 14.3854 18.3326 15.1355 18.3326 15.8955C18.3326 15.9555 18.4226 16.0455 18.5026 16.0855L20.1626 17.0855L20.4626 17.2455C20.4626 16.5555 20.4626 15.9255 20.4626 15.3055C20.5026 15.1228 20.4709 14.9317 20.3742 14.7717C20.2774 14.6117 20.1229 14.4949 19.9426 14.4455C19.3726 14.1455 18.8626 13.8454 18.2826 13.5454L18.3326 13.5754ZM23.2226 13.5754L21.3326 14.6455C21.306 14.6734 21.2852 14.7063 21.2714 14.7424C21.2577 14.7785 21.2513 14.8169 21.2526 14.8554C21.2526 15.4154 21.2526 15.9755 21.2026 16.5355V17.1354C21.7826 16.7954 22.3126 16.4654 22.8726 16.1354C22.993 16.0906 23.0939 16.0051 23.1578 15.8937C23.2218 15.7823 23.2447 15.652 23.2226 15.5254C23.1526 14.9054 23.1726 14.2555 23.1726 13.5655L23.2226 13.5754ZM18.7426 12.9054C19.4226 13.2554 20.0626 13.5754 20.6926 13.9054C20.7462 13.9403 20.8087 13.9589 20.8726 13.9589C20.9365 13.9589 20.9991 13.9403 21.0526 13.9054L22.6226 13.0154L22.8126 12.8955L20.8826 11.7855C20.8532 11.7705 20.8206 11.7627 20.7876 11.7627C20.7546 11.7627 20.7221 11.7705 20.6926 11.7855L18.7426 12.9054Z"
      fill="currentColor"
    />
    <path
      d="M14.0615 12.7156C14.0596 13.2543 13.8983 13.7803 13.598 14.2275C13.2977 14.6747 12.8718 15.0231 12.3739 15.2287C11.8761 15.4344 11.3285 15.4882 10.8001 15.3834C10.2718 15.2785 9.78627 15.0197 9.40468 14.6395C9.0231 14.2593 8.76251 13.7747 8.65574 13.2467C8.54896 12.7188 8.60076 12.171 8.80462 11.6724C9.00847 11.1738 9.35528 10.7467 9.80137 10.4447C10.2475 10.1428 10.7729 9.97961 11.3115 9.97566C11.6726 9.97301 12.0305 10.042 12.3647 10.1786C12.6989 10.3153 13.0027 10.5168 13.2584 10.7717C13.5142 11.0265 13.7169 11.3295 13.8547 11.6632C13.9926 11.9969 14.0629 12.3546 14.0615 12.7156ZM13.2815 12.7156C13.2915 12.3181 13.1827 11.9266 12.9691 11.5912C12.7554 11.2558 12.4467 10.9917 12.0822 10.8326C11.7177 10.6736 11.3141 10.6267 10.9229 10.6982C10.5317 10.7696 10.1707 10.956 9.88596 11.2337C9.60123 11.5113 9.40573 11.8674 9.32442 12.2567C9.24311 12.646 9.27969 13.0506 9.42948 13.419C9.57927 13.7874 9.83549 14.1028 10.1654 14.3248C10.4953 14.5469 10.8839 14.6655 11.2815 14.6656C11.5426 14.6723 11.8024 14.6265 12.0455 14.5309C12.2885 14.4353 12.5099 14.2918 12.6964 14.109C12.883 13.9262 13.0309 13.7078 13.1314 13.4667C13.2319 13.2256 13.283 12.9668 13.2815 12.7057V12.7156Z"
      fill="currentColor"
    />
  </svg>
);
export const ReportsIcon: React.FC<IconInterface> = ({
  className = "size-4",
}) => (
  <svg
    className={className}
    viewBox="0 0 24 24"
    fill="currentColor"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clip-path="url(#clip0_2479_3727)">
      <path
        d="M16.7865 8.21338L12.8465 12.0867C12.4214 11.882 11.9377 11.8341 11.4808 11.9515C11.0239 12.0689 10.6232 12.344 10.3495 12.7283C10.0758 13.1125 9.94676 13.5811 9.98515 14.0513C10.0235 14.5215 10.2269 14.963 10.5593 15.2978C10.8917 15.6325 11.3318 15.839 11.8017 15.8807C12.2716 15.9224 12.7411 15.7967 13.1273 15.5257C13.5135 15.2548 13.7914 14.856 13.912 14.3999C14.0327 13.9439 13.9882 13.4598 13.7865 13.0334L17.7331 9.16005L16.7865 8.21338Z"
        fill="currentColor"
      />
      <path
        d="M12.0001 2.83326C9.90189 2.83086 7.84683 3.42895 6.07762 4.55691C4.30841 5.68486 2.89894 7.29557 2.01568 9.19879C1.13241 11.102 0.812254 13.2182 1.09301 15.2976C1.37376 17.3769 2.24369 19.3324 3.60008 20.9333L3.80008 21.1666H20.2001L20.4001 20.9333C21.7565 19.3324 22.6264 17.3769 22.9071 15.2976C23.1879 13.2182 22.8677 11.102 21.9845 9.19879C21.1012 7.29557 19.6917 5.68486 17.9225 4.55691C16.1533 3.42895 14.0983 2.83086 12.0001 2.83326ZM19.5601 19.8333H4.44008C3.24386 18.3364 2.5219 16.5164 2.36674 14.6066H4.66674V13.2733H2.36674C2.47789 11.1563 3.28661 9.13566 4.66674 7.52659L6.30008 9.15993L7.24008 8.21993L5.62008 6.57993C7.20865 5.17519 9.21764 4.33596 11.3334 4.19326V6.52659H12.6667V4.19993C15.0037 4.36941 17.1991 5.38367 18.843 7.05334C20.4869 8.72301 21.467 10.9339 21.6001 13.2733H19.2801V14.6066H21.6334C21.4782 16.5164 20.7563 18.3364 19.5601 19.8333Z"
        fill="currentColor"
      />
    </g>
    <defs>
      <clipPath id="clip0_2479_3727">
        <rect width="17" height="17" fill="white" />
      </clipPath>
    </defs>
  </svg>
);
export const ChannelIcon: React.FC<IconInterface> = ({
  className = "size-4",
}) => (
  <svg
    className={className}
    viewBox="0 0 24 25"
    fill="currentColor"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M7.01953 12.5448C7.01963 11.5469 7.31651 10.5716 7.87242 9.74294C8.42833 8.91426 9.21815 8.26964 10.1414 7.89107C11.0647 7.5125 12.0797 7.41708 13.0573 7.61695C14.035 7.81682 14.9311 8.30295 15.6317 9.0135C16.3323 9.72406 16.8058 10.6269 16.9918 11.6073C17.1779 12.5877 17.0682 13.6012 16.6766 14.5191C16.2851 15.4369 15.6294 16.2176 14.793 16.7617C13.9565 17.3059 12.9772 17.589 11.9794 17.5751C10.6575 17.5566 9.39606 17.0184 8.46788 16.0771C7.53969 15.1357 7.0194 13.8668 7.01953 12.5448ZM10.5608 13.5508L10.4301 13.3698C10.0988 12.8899 9.91684 12.3229 9.9069 11.7399C9.9069 11.2063 10.1189 10.6945 10.4962 10.3172C10.8736 9.93983 11.3854 9.72784 11.919 9.72784C12.2151 9.70096 12.5134 9.74001 12.7925 9.84217C13.0717 9.94434 13.3247 10.1071 13.5335 10.3187C13.7422 10.5304 13.9015 10.7856 13.9998 11.0662C14.0981 11.3467 14.1331 11.6455 14.1021 11.9412C14.0423 12.5086 13.8264 13.0483 13.4784 13.5005C13.9315 13.5926 14.3613 13.7756 14.7417 14.0384C15.1221 14.3012 15.4453 14.6385 15.6917 15.0297C16.4175 14.1065 16.7468 12.9328 16.6071 11.7668C16.4675 10.6009 15.8704 9.53808 14.9472 8.81233C14.024 8.08657 12.8503 7.75728 11.6843 7.8969C10.5184 8.03652 9.45558 8.63361 8.72982 9.55681C8.04876 10.282 7.63644 11.2184 7.5614 12.2104C7.48636 13.2024 7.75311 14.1902 8.31733 15.0096C8.56544 14.6156 8.8932 14.2778 9.27956 14.0179C9.66592 13.758 10.1024 13.5818 10.5608 13.5005V13.5508ZM11.748 17.0016C12.3748 17.03 13.0009 16.9315 13.5887 16.7119C14.1765 16.4923 14.7139 16.1563 15.1686 15.7239C15.2893 15.5931 15.3295 15.4825 15.2289 15.3517C15.0625 15.104 14.8703 14.8747 14.6555 14.6676C14.2028 14.3134 13.6651 14.0845 13.0961 14.0036C13.0168 13.9836 12.9339 13.9836 12.8546 14.0036C12.6081 14.1448 12.3289 14.219 12.0448 14.219C11.7606 14.219 11.4815 14.1448 11.2349 14.0036C11.1501 13.9744 11.0581 13.9744 10.9733 14.0036C10.5909 14.1098 10.2147 14.2374 9.84654 14.3859C9.41545 14.5756 9.06098 14.9053 8.84049 15.3215C8.79672 15.3816 8.7785 15.4566 8.78981 15.5301C8.80112 15.6036 8.84103 15.6697 8.90085 15.7139C9.65343 16.4869 10.6705 16.9469 11.748 17.0016ZM13.5891 11.8305C13.6134 11.6171 13.5903 11.401 13.5214 11.1975C13.4525 10.9941 13.3395 10.8084 13.1906 10.6536C13.0416 10.4989 12.8603 10.379 12.6596 10.3024C12.459 10.2259 12.2438 10.1946 12.0297 10.2107C11.6831 10.187 11.3397 10.2903 11.0638 10.5014C10.7879 10.7125 10.5983 11.0169 10.5307 11.3576C10.4672 11.7252 10.4986 12.1029 10.6219 12.455C10.7451 12.8071 10.9561 13.1219 11.2349 13.3698C11.3385 13.4826 11.4645 13.5726 11.6048 13.6342C11.7451 13.6959 11.8966 13.7277 12.0498 13.7277C12.203 13.7277 12.3545 13.6959 12.4948 13.6342C12.6351 13.5726 12.761 13.4826 12.8647 13.3698C13.2851 12.9648 13.5337 12.4137 13.5589 11.8305H13.5891Z"
      fill="currentColor"
    />
    <path
      d="M17.5901 18.5309C17.1273 18.0681 16.6746 17.6153 16.2319 17.1526C16.1885 17.1096 16.1551 17.0576 16.1342 17.0001C16.1133 16.9427 16.1055 16.8814 16.1112 16.8206C16.1112 16.5892 16.413 16.5489 16.6042 16.7401C17.0066 17.1324 17.409 17.5349 17.8014 17.9373C17.8632 18.0036 17.9204 18.0742 17.9724 18.1486C18.4059 17.7709 18.923 17.5018 19.4811 17.3636C20.0391 17.2254 20.6221 17.222 21.1817 17.3538C22.0523 17.5448 22.8181 18.0589 23.3246 18.7924C23.7197 19.3488 23.9505 20.005 23.9908 20.6862C24.031 21.3674 23.8791 22.0463 23.5522 22.6453C23.2254 23.2443 22.7368 23.7395 22.1423 24.0744C21.5477 24.4092 20.8709 24.5703 20.1892 24.5392C19.5076 24.5081 18.8483 24.2861 18.2867 23.8985C17.725 23.5109 17.2836 22.9733 17.0126 22.347C16.7417 21.7207 16.6522 21.0309 16.7543 20.3561C16.8564 19.6814 17.146 19.049 17.5901 18.5309ZM23.395 21.2472C23.4939 20.615 23.3893 19.9677 23.0963 19.3988C22.8034 18.8299 22.3371 18.3688 21.765 18.0822C21.1929 17.7956 20.5445 17.6982 19.9134 17.8042C19.2823 17.9102 18.7012 18.2139 18.2541 18.6717C17.8464 19.061 17.5559 19.5566 17.4154 20.1024C17.275 20.6483 17.2902 21.2226 17.4593 21.7603C17.7812 21.4283 18.1032 21.0963 18.4654 20.7542C18.8275 20.4122 18.7873 20.5631 18.9885 20.7542C19.1897 20.9454 19.4613 21.2472 19.7129 21.5088L21.2622 19.9494C21.7048 19.5067 21.7048 19.5067 22.1576 19.9494L23.395 21.2472ZM21.8055 23.6517C22.4595 23.307 22.9643 22.7342 23.224 22.042C23.2265 22.0031 23.2207 21.964 23.2068 21.9276C23.1929 21.8911 23.1713 21.8581 23.1435 21.8307L22.1375 20.7643C22.0146 20.6344 21.9004 20.4966 21.7954 20.3518L20.1857 21.9615L20.2863 22.0722L21.8055 23.6517ZM17.7008 22.4142C18.0339 23.0238 18.5671 23.4997 19.2104 23.7618C19.8537 24.0239 20.5677 24.0561 21.232 23.8529L18.737 21.378L17.7008 22.4142Z"
      fill="currentColor"
    />
    <path
      d="M6.04088 6.98113C5.32345 7.59454 4.39639 7.90635 3.45409 7.85117C2.51179 7.79598 1.62746 7.37809 0.986544 6.68513C0.345628 5.99217 -0.00208439 5.07797 0.01632 4.13424C0.0347245 3.19051 0.417814 2.29055 1.08526 1.6231C1.75271 0.955655 2.65266 0.572566 3.5964 0.554162C4.54013 0.535757 5.45433 0.88347 6.14729 1.52439C6.84025 2.1653 7.25814 3.04963 7.31333 3.99193C7.36851 4.93423 7.05671 5.86129 6.4433 6.57872C6.85578 7.00126 7.27832 7.4238 7.70087 7.83629L7.86183 8.00731C7.88831 8.03267 7.90938 8.06312 7.92377 8.09683C7.93817 8.13055 7.94559 8.16683 7.94559 8.20349C7.94559 8.24015 7.93817 8.27643 7.92377 8.31014C7.90938 8.34386 7.88831 8.37432 7.86183 8.39967C7.83637 8.42775 7.80531 8.45018 7.77066 8.46554C7.73601 8.48089 7.69853 8.48882 7.66062 8.48882C7.62272 8.48882 7.58523 8.48089 7.55058 8.46554C7.51593 8.45018 7.48487 8.42775 7.45941 8.39967C7.3085 8.25883 7.16765 8.10791 7.0268 7.95701C6.71493 7.64513 6.37287 7.31313 6.04088 6.98113ZM3.69677 7.27289C4.30819 7.27277 4.90573 7.09059 5.41321 6.74958C5.9207 6.40856 6.31515 5.92415 6.54628 5.3581C6.7774 4.79205 6.83474 4.16999 6.71098 3.57123C6.58722 2.97246 6.28796 2.42411 5.85137 1.99607C5.41477 1.56804 4.8606 1.27971 4.2595 1.16782C3.65841 1.05594 3.03761 1.12558 2.47624 1.36787C1.91488 1.61016 1.43838 2.01412 1.10747 2.52826C0.776571 3.0424 0.606258 3.64343 0.618247 4.25473C0.634053 5.0607 0.965359 5.82832 1.54099 6.39266C2.11662 6.95701 2.89065 7.27305 3.69677 7.27289Z"
      fill="currentColor"
    />
    <path
      d="M17.6143 6.619C17.2925 6.23876 17.0504 5.79763 16.9027 5.32191C16.7549 4.84618 16.7044 4.34557 16.7541 3.84991C16.8039 3.35425 16.9529 2.87368 17.1923 2.43682C17.4317 1.99995 17.7565 1.61572 18.1475 1.30703C18.6939 0.891012 19.3469 0.637995 20.031 0.577176C20.7151 0.516357 21.4025 0.65022 22.0137 0.963311C22.625 1.2764 23.1353 1.75596 23.4857 2.34664C23.8362 2.93733 24.0124 3.61505 23.9941 4.30161C23.9759 4.98816 23.7639 5.65557 23.3826 6.22679C23.0012 6.79801 22.4662 7.24977 21.8391 7.52992C21.212 7.81008 20.5185 7.90722 19.8386 7.81012C19.1587 7.71303 18.5202 7.42565 17.9966 6.98117L17.0811 7.89669L16.6786 8.30916C16.4774 8.51038 16.3064 8.54056 16.1656 8.39971C16.0448 8.26893 16.075 8.10796 16.2662 7.90675L17.4131 6.7699C17.4751 6.71325 17.5425 6.66272 17.6143 6.619ZM17.3024 4.19441C17.2924 4.80291 17.4636 5.40063 17.7942 5.91158C18.1248 6.42253 18.5999 6.82364 19.159 7.0639C19.7182 7.30417 20.3361 7.37274 20.9344 7.2609C21.5326 7.14905 22.084 6.86185 22.5186 6.4358C22.9532 6.00975 23.2512 5.4641 23.3749 4.86822C23.4986 4.27233 23.4422 3.65313 23.2131 3.08934C22.984 2.52555 22.5923 2.04262 22.088 1.70197C21.5837 1.36132 20.9895 1.17832 20.3809 1.17625C19.9795 1.16822 19.5805 1.24035 19.2074 1.38843C18.8342 1.53651 18.4943 1.75755 18.2076 2.03861C17.9209 2.31968 17.6932 2.65512 17.5378 3.02529C17.3823 3.39547 17.3023 3.79293 17.3024 4.19441Z"
      fill="currentColor"
    />
    <path
      d="M6.0407 18.1485C6.45318 17.7259 6.87572 17.3134 7.29826 16.8909C7.34746 16.8266 7.40497 16.769 7.46929 16.7199C7.49376 16.6937 7.52336 16.6728 7.55624 16.6585C7.58912 16.6442 7.62459 16.6368 7.66044 16.6368C7.69629 16.6368 7.73176 16.6442 7.76464 16.6585C7.79752 16.6728 7.82712 16.6937 7.85159 16.7199C7.87966 16.7453 7.90211 16.7764 7.91746 16.811C7.93281 16.8457 7.94074 16.8832 7.94074 16.9211C7.94074 16.959 7.93281 16.9965 7.91746 17.0311C7.90211 17.0658 7.87966 17.0968 7.85159 17.1223C7.71074 17.2732 7.55983 17.414 7.41898 17.5549L6.41293 18.5609C6.79398 19.0062 7.06162 19.537 7.19302 20.1081C7.32441 20.6792 7.31564 21.2737 7.16747 21.8407C6.95804 22.6952 6.43802 23.4412 5.7087 23.9333C4.98529 24.4236 4.10969 24.6374 3.2417 24.5358C2.37372 24.4341 1.57123 24.0237 0.980699 23.3795C0.390171 22.7353 0.0509842 21.9002 0.0250435 21.0267C-0.000897223 20.1532 0.288139 19.2994 0.8394 18.6213C1.14744 18.2452 1.52727 17.9342 1.95676 17.7064C2.38625 17.4787 2.85678 17.3387 3.34093 17.2947C3.82508 17.2507 4.31315 17.3035 4.77667 17.4501C5.24019 17.5967 5.66989 17.8341 6.0407 18.1485ZM3.69659 24.0037C4.30746 24.0157 4.90809 23.8456 5.422 23.5151C5.93591 23.1847 6.33986 22.7088 6.58244 22.148C6.82501 21.5872 6.89524 20.9669 6.78417 20.3661C6.6731 19.7653 6.38576 19.2111 5.95874 18.7742C5.53171 18.3372 4.98431 18.0371 4.38622 17.9122C3.78813 17.7873 3.1664 17.8432 2.60019 18.0728C2.03397 18.3024 1.54886 18.6953 1.20664 19.2014C0.864412 19.7076 0.680541 20.3041 0.678427 20.9151C0.675632 21.723 0.990508 22.4996 1.55513 23.0774C2.11976 23.6552 2.88888 23.9879 3.69659 24.0037Z"
      fill="currentColor"
    />
    <path
      d="M12.297 4.15436V6.39787C12.297 6.47258 12.2673 6.54423 12.2145 6.59706C12.1617 6.64988 12.09 6.67956 12.0153 6.67956C11.9406 6.67956 11.8689 6.64988 11.8161 6.59706C11.7633 6.54423 11.7336 6.47258 11.7336 6.39787C11.7336 6.11617 11.7336 5.84454 11.7336 5.56284V4.15436C11.4506 4.10142 11.1839 3.98282 10.955 3.8081C10.7261 3.63337 10.5414 3.40739 10.4157 3.14831C10.2193 2.77045 10.1692 2.33345 10.2748 1.92092C10.3531 1.60407 10.516 1.31445 10.7462 1.08303C10.9763 0.851598 11.265 0.687062 11.5814 0.60701C11.8978 0.526957 12.23 0.534403 12.5425 0.62854C12.855 0.722676 13.1361 0.89997 13.3556 1.14147C13.5752 1.38297 13.7249 1.67959 13.789 1.99962C13.853 2.31966 13.8288 2.65107 13.7191 2.95844C13.6093 3.26581 13.4181 3.53757 13.1658 3.74467C12.9136 3.95177 12.6098 4.08643 12.2869 4.13425L12.297 4.15436ZM12.0052 1.1362C11.7605 1.1362 11.5213 1.20878 11.3178 1.34475C11.1143 1.48072 10.9557 1.67399 10.862 1.9001C10.7683 2.12621 10.7438 2.37502 10.7916 2.61506C10.8393 2.85511 10.9572 3.07559 11.1302 3.24865C11.3033 3.42171 11.5238 3.53957 11.7638 3.58732C12.0039 3.63506 12.2527 3.61056 12.4788 3.5169C12.7049 3.42324 12.8982 3.26464 13.0341 3.06114C13.1701 2.85764 13.2427 2.6184 13.2427 2.37365C13.2427 2.04453 13.1127 1.72873 12.8809 1.49507C12.6491 1.2614 12.3344 1.12879 12.0052 1.12614V1.1362Z"
      fill="currentColor"
    />
    <path
      d="M11.7446 20.9855V18.8325C11.7446 18.581 11.8553 18.4402 12.0364 18.4502C12.2175 18.4603 12.3281 18.6212 12.3181 18.8225V20.9855C12.6017 21.0369 12.8691 21.1549 13.0982 21.3298C13.3273 21.5047 13.5116 21.7315 13.636 21.9915C13.8131 22.3647 13.8558 22.7877 13.7567 23.1887C13.6802 23.5066 13.5186 23.7976 13.2892 24.0306C13.0599 24.2636 12.7714 24.4297 12.4548 24.5112C12.1382 24.5927 11.8054 24.5865 11.492 24.4932C11.1787 24.3999 10.8966 24.2231 10.6762 23.9816C10.4557 23.7402 10.3051 23.4433 10.2405 23.1228C10.176 22.8023 10.1999 22.4703 10.3097 22.1624C10.4195 21.8544 10.6111 21.5822 10.8639 21.3748C11.1167 21.1675 11.4211 21.0329 11.7446 20.9855ZM12.0263 24.0036C12.2691 23.9937 12.5036 23.9126 12.7006 23.7703C12.8975 23.628 13.0483 23.4309 13.134 23.2035C13.2196 22.9762 13.2365 22.7286 13.1824 22.4917C13.1283 22.2548 13.0056 22.0391 12.8297 21.8715C12.6539 21.7038 12.4325 21.5917 12.1933 21.549C11.9541 21.5063 11.7076 21.535 11.4846 21.6315C11.2616 21.728 11.0719 21.888 10.9393 22.0916C10.8066 22.2951 10.7368 22.5333 10.7386 22.7762C10.7399 22.9405 10.7738 23.1028 10.8385 23.2538C10.9031 23.4047 10.9972 23.5413 11.1152 23.6556C11.2332 23.7698 11.3727 23.8594 11.5257 23.9192C11.6787 23.9789 11.842 24.0076 12.0062 24.0036H12.0263Z"
      fill="currentColor"
    />
    <path
      d="M3.61744 12.2833H5.71004C5.98167 12.2833 6.11246 12.3839 6.11246 12.575C6.11246 12.7662 5.97161 12.8567 5.68992 12.8567H3.61744C3.56305 13.138 3.44379 13.4026 3.26914 13.6297C3.09449 13.8567 2.86927 14.0399 2.61139 14.1646C2.23047 14.3728 1.78519 14.4303 1.36389 14.3256C0.94671 14.2193 0.581034 13.9679 0.332441 13.6164C0.0838472 13.265 -0.0314 12.8364 0.00735692 12.4077C0.0461139 11.9789 0.236343 11.578 0.543934 11.2768C0.851525 10.9756 1.25635 10.7939 1.68582 10.7641C2.13788 10.7372 2.58411 10.877 2.94007 11.1569C3.29603 11.4369 3.53702 11.8376 3.61744 12.2833ZM0.599282 12.5549C0.59529 12.8009 0.664717 13.0425 0.798686 13.2488C0.932656 13.4552 1.12509 13.6169 1.35141 13.7134C1.57774 13.8098 1.82767 13.8366 2.0693 13.7904C2.31093 13.7441 2.53329 13.6268 2.70796 13.4536C2.88264 13.2803 3.00171 13.059 3.04997 12.8177C3.09823 12.5765 3.07349 12.3263 2.9789 12.0992C2.88431 11.8721 2.72417 11.6784 2.51893 11.5427C2.31369 11.407 2.07268 11.3356 1.82667 11.3376C1.50368 11.3402 1.1945 11.4689 0.96517 11.6964C0.735836 11.9238 0.604524 12.2319 0.599282 12.5549Z"
      fill="currentColor"
    />
    <path
      d="M20.4077 12.8565H18.2749C18.0234 12.8565 17.8926 12.7559 17.8926 12.5748C17.8926 12.3938 18.0234 12.2831 18.2749 12.2831H20.3977C20.4838 11.8145 20.7473 11.3973 21.1334 11.1181C21.5194 10.8389 21.9982 10.7192 22.4701 10.7841C22.9019 10.8477 23.2956 11.0669 23.5772 11.4003C23.8588 11.7338 24.0089 12.1586 23.9993 12.595C23.9901 13.0347 23.8221 13.4563 23.5262 13.7818C23.2303 14.1073 22.8266 14.3147 22.3896 14.3656C21.9312 14.4035 21.475 14.2702 21.109 13.9915C20.743 13.7129 20.4932 13.3085 20.4077 12.8565ZM23.4259 12.5547C23.4181 12.2326 23.286 11.9259 23.0572 11.699C22.8284 11.4721 22.5207 11.3425 22.1985 11.3374C22.0273 11.3219 21.8547 11.3423 21.6918 11.3972C21.5289 11.4521 21.3793 11.5403 21.2524 11.6562C21.1254 11.7722 21.0241 11.9133 20.9547 12.0706C20.8854 12.2279 20.8496 12.3979 20.8496 12.5698C20.8496 12.7417 20.8854 12.9117 20.9547 13.069C21.0241 13.2263 21.1254 13.3674 21.2524 13.4834C21.3793 13.5993 21.5289 13.6876 21.6918 13.7425C21.8547 13.7974 22.0273 13.8177 22.1985 13.8022C22.5276 13.7996 22.8423 13.667 23.0741 13.4333C23.3059 13.1996 23.4359 12.8838 23.4359 12.5547H23.4259Z"
      fill="currentColor"
    />
    <path
      d="M19.8433 20.3013C19.6594 20.3013 19.4797 20.2465 19.3271 20.1438C19.1745 20.0412 19.0559 19.8955 18.9865 19.7252C18.917 19.5549 18.8999 19.3677 18.9373 19.1877C18.9747 19.0076 19.0648 18.8427 19.1963 18.7141C19.3278 18.5855 19.4946 18.499 19.6754 18.4656C19.8563 18.4322 20.0429 18.4534 20.2117 18.5266C20.3804 18.5997 20.5235 18.7215 20.6228 18.8763C20.722 19.0311 20.7729 19.212 20.7689 19.3959C20.7676 19.5161 20.7426 19.6349 20.6954 19.7455C20.6481 19.856 20.5796 19.9562 20.4936 20.0403C20.4077 20.1244 20.306 20.1907 20.1944 20.2355C20.0829 20.2803 19.9636 20.3027 19.8433 20.3013ZM19.8433 19.7279C19.8939 19.7365 19.9457 19.734 19.9951 19.7205C20.0446 19.7071 20.0905 19.683 20.1297 19.6499C20.1689 19.6169 20.2004 19.5757 20.222 19.5292C20.2436 19.4827 20.2547 19.4321 20.2547 19.3808C20.2547 19.3295 20.2436 19.2789 20.222 19.2324C20.2004 19.1859 20.1689 19.1447 20.1297 19.1117C20.0905 19.0786 20.0446 19.0545 19.9951 19.041C19.9457 19.0276 19.8939 19.0251 19.8433 19.0337C19.7535 19.0388 19.669 19.0782 19.6072 19.1437C19.5455 19.2092 19.5112 19.2958 19.5113 19.3858C19.5139 19.4757 19.5507 19.5613 19.6143 19.6249C19.6779 19.6885 19.7635 19.7254 19.8534 19.7279H19.8433Z"
      fill="currentColor"
    />
    <path
      d="M3.69745 5.17041H5.00533C5.23672 5.17041 5.37757 5.28108 5.35744 5.46217C5.33732 5.64326 5.19648 5.74386 4.99526 5.74386H2.32922C2.09783 5.74386 1.96704 5.63319 1.9771 5.4521C1.98716 5.27101 2.13807 5.17041 2.33928 5.17041C2.79201 5.18047 3.24473 5.17041 3.69745 5.17041Z"
      fill="currentColor"
    />
    <path
      d="M3.69745 3.22872H2.32922C2.09783 3.22872 1.96704 3.11806 1.9771 2.93697C1.98716 2.75588 2.12801 2.65527 2.31916 2.65527H5.00533C5.22666 2.65527 5.3675 2.776 5.34738 2.95708C5.32726 3.13817 5.20654 3.22872 5.00533 3.22872H3.69745Z"
      fill="currentColor"
    />
    <path
      d="M3.69852 4.44625H2.39065C2.12908 4.44625 1.99829 4.33559 2.00835 4.1545C2.01841 3.97341 2.17938 3.8728 2.37053 3.8728H5.01645C5.2579 3.8728 5.39875 3.99353 5.37863 4.17462C5.35851 4.35571 5.21766 4.44625 5.02651 4.44625H3.69852Z"
      fill="currentColor"
    />
    <path
      d="M22.1606 4.20461C22.1634 4.30727 22.1377 4.4087 22.0861 4.49754C22.0346 4.58638 21.9594 4.65912 21.8688 4.70763C21.1948 5.11005 20.5207 5.50241 19.8567 5.88471C19.7688 5.94081 19.6671 5.97142 19.5629 5.97313C19.4586 5.97484 19.3559 5.94758 19.2663 5.8944C19.1766 5.84122 19.1034 5.76419 19.0549 5.67189C19.0064 5.5796 18.9845 5.47565 18.9915 5.37162C18.9915 4.59696 18.9915 3.81224 18.9915 3.03758C18.9861 2.93395 19.0089 2.83079 19.0575 2.7391C19.1061 2.64741 19.1787 2.57063 19.2675 2.51694C19.3563 2.46324 19.458 2.43465 19.5618 2.43421C19.6655 2.43377 19.7675 2.4615 19.8567 2.51443C20.5509 2.89673 21.235 3.30922 21.8688 3.70158C21.9594 3.75009 22.0346 3.82283 22.0861 3.91167C22.1377 4.00051 22.1634 4.10194 22.1606 4.20461ZM21.6073 4.20461L19.5952 3.0074V5.39175L21.6073 4.20461Z"
      fill="currentColor"
    />
    <path
      d="M4.25205 19.718C4.25205 20.4323 4.25205 21.1466 4.25205 21.8509C4.22938 22.127 4.09792 22.3829 3.88661 22.5621C3.67529 22.7413 3.40143 22.8293 3.12527 22.8066C2.84911 22.7839 2.59327 22.6525 2.41403 22.4412C2.23479 22.2299 2.14684 21.956 2.16952 21.6798C2.19884 21.5391 2.25658 21.4058 2.33921 21.2882C2.42183 21.1705 2.5276 21.071 2.65004 20.9956C2.77248 20.9203 2.90901 20.8707 3.05127 20.85C3.19353 20.8292 3.33853 20.8378 3.47739 20.875L3.68866 20.9354C3.68866 20.4323 3.68866 19.9293 3.68866 19.4565C3.68866 19.2452 3.68866 19.054 3.94017 18.9736C4.05216 18.9228 4.17766 18.9101 4.29755 18.9373C4.41743 18.9646 4.52512 19.0303 4.60417 19.1245C4.78893 19.3207 4.96035 19.5291 5.11726 19.7482C5.14456 19.7993 5.15884 19.8563 5.15884 19.9142C5.15884 19.9721 5.14456 20.0291 5.11726 20.0802C5.06955 20.1176 5.01384 20.1434 4.95449 20.1556C4.89515 20.1678 4.83378 20.1661 4.7752 20.1506C4.58601 20.0253 4.41065 19.8803 4.25205 19.718ZM2.69267 21.8811C2.6899 21.9444 2.70015 22.0076 2.7228 22.0667C2.74545 22.1259 2.78001 22.1798 2.82434 22.2251C2.86867 22.2703 2.92183 22.306 2.98052 22.3299C3.0392 22.3538 3.10217 22.3654 3.16552 22.364C3.29092 22.364 3.41119 22.3141 3.49986 22.2255C3.58854 22.1368 3.63836 22.0165 3.63836 21.8911C3.63836 21.7657 3.58854 21.6454 3.49986 21.5568C3.41119 21.4681 3.29092 21.4183 3.16552 21.4183C3.1039 21.4169 3.04264 21.4279 2.98532 21.4506C2.92801 21.4732 2.87579 21.5071 2.83175 21.5502C2.78771 21.5933 2.75272 21.6448 2.72884 21.7016C2.70495 21.7584 2.69265 21.8194 2.69267 21.8811Z"
      fill="currentColor"
    />
  </svg>
);

export const HrIcon: React.FC<IconInterface> = ({ className = "size-4" }) => (
  <svg
    className={className}
    viewBox="0 0 24 25"
    fill="currentColor"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M11.7811 13.9026C12.9488 13.9993 14.0388 14.5268 14.8391 15.3825C15.6394 16.2383 16.0928 17.3611 16.1111 18.5326C16.1111 20.0226 16.1111 21.5326 16.1111 23.0126C16.1111 23.3226 16.0111 23.4226 15.7011 23.4226H0.431133C0.101133 23.4226 0.00112554 23.3226 0.00112554 22.9726V18.7126C-0.0245045 17.5374 0.388375 16.3948 1.15928 15.5074C1.93019 14.62 3.0039 14.0515 4.17114 13.9126H4.29113L3.94113 13.5226C3.21022 12.5707 2.83843 11.3916 2.89114 10.1926C2.89853 9.46483 3.07663 8.74897 3.41113 8.1026C3.78113 7.3826 4.12113 6.63259 4.55113 5.94259C4.95126 5.28207 5.53804 4.75484 6.23745 4.42738C6.93686 4.09992 7.71758 3.9869 8.48114 4.1026C9.11157 4.12614 9.72594 4.30793 10.2677 4.63122C10.8094 4.95452 11.2611 5.40893 11.5811 5.9526C12.2198 6.89353 12.7215 7.92048 13.0711 9.00261C13.3134 9.73918 13.3633 10.5254 13.2163 11.2867C13.0693 12.0481 12.7302 12.7592 12.2311 13.3526C12.0711 13.5226 11.9411 13.6826 11.7811 13.9026ZM11.4611 14.5226C11.4921 14.6083 11.5288 14.6919 11.5711 14.7726L12.0511 15.7726C12.0823 15.8169 12.1018 15.8685 12.1076 15.9223C12.1134 15.9762 12.1055 16.0307 12.0845 16.0807C12.0635 16.1307 12.0302 16.1745 11.9876 16.2081C11.945 16.2416 11.8946 16.2638 11.8411 16.2726L11.6011 16.3526C11.3811 16.4326 11.0711 16.4526 10.9611 16.6026C10.8511 16.7526 10.9611 17.0726 10.9611 17.3026C10.9811 17.4033 10.9719 17.5075 10.9347 17.6031C10.8975 17.6988 10.8338 17.7818 10.7511 17.8426L8.55113 19.6826C8.49042 19.7222 8.44161 19.7775 8.40991 19.8427C8.37821 19.9078 8.36481 19.9804 8.37114 20.0526V22.5526C8.37114 22.6326 8.37114 22.7126 8.37114 22.8026H12.7211V22.5626C12.7211 21.8626 12.7211 21.1526 12.7211 20.4426C12.7211 20.1626 12.8311 20.0226 13.0311 20.0226C13.2311 20.0226 13.3511 20.1626 13.3511 20.4326V22.8026H15.4611C15.4611 22.8026 15.4611 22.7326 15.4611 22.7126C15.4611 21.3426 15.4611 19.9726 15.4611 18.6026C15.4562 17.7766 15.2 16.9716 14.7267 16.2947C14.2534 15.6177 13.5853 15.1008 12.8111 14.8126C12.3863 14.6176 11.928 14.5056 11.4611 14.4826V14.5226ZM7.74113 22.7826V22.5126C7.74113 21.6926 7.74113 20.8826 7.74113 20.0626C7.74583 19.9856 7.72973 19.9088 7.69455 19.8402C7.65936 19.7716 7.60638 19.7137 7.54113 19.6726L4.94113 17.8326C4.84737 17.7804 4.77205 17.7005 4.72551 17.6038C4.67896 17.5071 4.66346 17.3984 4.68113 17.2926C4.68113 17.0526 4.68113 16.8026 4.68113 16.5426L3.76114 16.2626C3.41114 16.1626 3.34114 16.0026 3.51114 15.6726L4.10113 14.5726H4.07113C3.12699 14.7366 2.27041 15.2269 1.65084 15.9579C1.03128 16.689 0.688085 17.6144 0.681133 18.5726C0.681133 19.9026 0.681133 21.2326 0.681133 22.5726C0.681133 22.6426 0.681133 22.7226 0.681133 22.7926H2.79113V20.4026C2.79113 20.1426 2.89114 20.0126 3.09114 20.0026C3.29114 19.9926 3.41114 20.1326 3.42114 20.4026V22.7926L7.74113 22.7826ZM8.74113 8.1926V8.1026L10.5211 8.3226C10.573 8.32623 10.6235 8.34047 10.6696 8.36441C10.7157 8.38835 10.7565 8.42149 10.7893 8.46179C10.8221 8.50209 10.8463 8.54869 10.8603 8.59871C10.8744 8.64873 10.8781 8.70111 10.8711 8.75261C10.8711 9.32261 10.8711 9.8926 10.8711 10.4626C10.8477 11.328 10.5181 12.1571 9.94113 12.8026C9.8038 12.9129 9.71311 13.071 9.68716 13.2452C9.66121 13.4194 9.70191 13.597 9.80113 13.7426C9.82439 13.7776 9.85549 13.8066 9.89195 13.8274C9.9284 13.8483 9.96921 13.8603 10.0111 13.8626C10.4041 13.8394 10.7859 13.7231 11.125 13.5231C11.4641 13.3231 11.7507 13.0453 11.9611 12.7126C12.29 12.2445 12.5116 11.7097 12.6101 11.1462C12.7086 10.5828 12.6817 10.0045 12.5311 9.4526C12.2073 8.31011 11.6971 7.22892 11.0211 6.25261C10.7619 5.82282 10.4049 5.46026 9.97923 5.19441C9.55352 4.92856 9.07108 4.76692 8.57113 4.72261C7.93764 4.60392 7.28332 4.67104 6.68713 4.91591C6.09094 5.16077 5.57833 5.57291 5.21113 6.1026C4.76725 6.82496 4.36007 7.56923 3.99113 8.33259C3.7253 8.82696 3.56845 9.37254 3.53114 9.9326C3.43174 11.0469 3.74095 12.1594 4.40113 13.0626C4.55645 13.2815 4.75424 13.467 4.98274 13.6078C5.21124 13.7487 5.46576 13.8422 5.73114 13.8826C5.90114 13.8826 6.00114 13.8826 6.03114 13.6726C6.0718 13.4869 6.12186 13.3033 6.18113 13.1226C6.21227 13.0398 6.21941 12.9499 6.20172 12.8632C6.18403 12.7765 6.14222 12.6966 6.08113 12.6326C5.90424 12.4088 5.75641 12.1635 5.64114 11.9026C5.33461 11.1921 5.17648 10.4264 5.17648 9.6526C5.17648 8.87878 5.33461 8.11312 5.64114 7.4026C5.70082 7.26329 5.81182 7.15229 5.95114 7.0926C6.13114 7.0326 6.23113 7.2026 6.32113 7.3426C6.44725 7.55863 6.62256 7.74186 6.83281 7.87739C7.04305 8.01293 7.28231 8.09694 7.53114 8.1226C7.94114 8.1326 8.34113 8.1326 8.74113 8.1526V8.1926ZM6.07113 8.0526C5.84125 8.76174 5.76623 9.51198 5.85113 10.2526C5.88612 11.0345 6.17783 11.7831 6.68113 12.3826C6.8968 12.6593 7.18902 12.8665 7.52148 12.9785C7.85394 13.0906 8.21199 13.1024 8.55113 13.0126C8.86797 12.9142 9.15715 12.7425 9.39518 12.5114C9.63322 12.2804 9.81342 11.9964 9.92114 11.6826C10.256 10.8455 10.3763 9.93802 10.2711 9.0426C10.2531 8.99795 10.2247 8.95828 10.1881 8.92695C10.1516 8.89563 10.108 8.87356 10.0611 8.86261C9.49114 8.86261 8.91114 8.80261 8.34114 8.86261C7.92281 8.90607 7.50009 8.85192 7.10621 8.70444C6.71234 8.55695 6.35803 8.32014 6.07113 8.0126V8.0526ZM11.3411 15.7326C11.213 15.5152 11.1027 15.2878 11.0111 15.0526C10.995 14.9543 10.9574 14.8607 10.9011 14.7785C10.8447 14.6963 10.7711 14.6275 10.6852 14.5769C10.5993 14.5264 10.5034 14.4953 10.4042 14.4859C10.305 14.4765 10.2049 14.489 10.1111 14.5226C10.0089 14.5389 9.91636 14.5923 9.85113 14.6726C9.28113 15.6726 8.71113 16.6726 8.16113 17.6126C8.11072 17.6967 8.09293 17.7963 8.11113 17.8926C8.21113 18.2326 8.33114 18.5626 8.45114 18.8926C8.49858 18.865 8.54226 18.8314 8.58113 18.7926C9.08113 18.3726 9.58113 17.9426 10.0811 17.5426C10.1751 17.4844 10.248 17.3977 10.2894 17.2952C10.3307 17.1927 10.3384 17.0797 10.3111 16.9726C10.3009 16.7761 10.3009 16.5791 10.3111 16.3826C10.2968 16.2885 10.3203 16.1925 10.3765 16.1157C10.4327 16.0388 10.5171 15.9874 10.6111 15.9726C10.8211 15.9026 11.0711 15.7826 11.3411 15.6926V15.7326ZM4.26114 15.7326L5.11113 15.9926C5.19791 16.0074 5.27559 16.0552 5.32782 16.1261C5.38004 16.197 5.40274 16.2853 5.39114 16.3726C5.3762 16.6257 5.3762 16.8795 5.39114 17.1326C5.39234 17.1896 5.4054 17.2457 5.4295 17.2974C5.45361 17.349 5.48823 17.3951 5.53114 17.4326L7.30113 18.7026L7.74113 19.0126H7.80113C7.78323 18.8399 7.74977 18.6692 7.70114 18.5026C7.25114 17.4426 6.79113 16.3826 6.33113 15.3326C6.22113 15.0726 6.16113 14.7226 5.96113 14.6026C5.76113 14.4826 5.40113 14.6026 5.11113 14.5426C5.04113 14.5426 4.93113 14.6126 4.90113 14.6726C4.64113 14.9526 4.46113 15.3126 4.26114 15.6926V15.7326ZM6.78114 13.3026C6.70114 13.6126 6.62113 13.8926 6.55113 14.1826C6.53664 14.235 6.53664 14.2903 6.55113 14.3426C6.93113 15.2326 7.32114 16.1226 7.70114 17.0226C7.70114 17.0226 7.76113 17.0226 7.77113 16.9626C8.26113 16.1226 8.77114 15.2726 9.23114 14.4226C9.25449 14.3649 9.25449 14.3003 9.23114 14.2426C9.16114 14.0026 9.08113 13.7626 8.99113 13.5026C8.63088 13.6446 8.24151 13.697 7.85653 13.6552C7.47156 13.6134 7.10251 13.4787 6.78114 13.2626V13.3026Z"
      fill="currentColor"
    />
    <path
      d="M20.9012 10.6827C21.0512 11.0527 21.1812 11.4027 21.3412 11.7527C21.3412 11.8127 21.4612 11.8527 21.5312 11.8727C21.7052 11.9244 21.8651 12.0153 21.9985 12.1384C22.132 12.2614 22.2355 12.4134 22.3012 12.5826C22.7612 13.6726 23.2112 14.7626 23.6412 15.8526C23.7045 15.9994 23.7376 16.1574 23.7386 16.3172C23.7395 16.477 23.7082 16.6353 23.6466 16.7828C23.585 16.9302 23.4944 17.0638 23.3801 17.1754C23.2657 17.2871 23.1301 17.3746 22.9812 17.4327L22.1612 17.7726C22.0136 17.8324 21.8556 17.8623 21.6964 17.8604C21.5371 17.8585 21.3799 17.8249 21.2338 17.7616C21.0876 17.6983 20.9556 17.6066 20.8452 17.4917C20.7349 17.3769 20.6486 17.2412 20.5912 17.0927C20.1412 16.0227 19.7012 14.9527 19.2612 13.8727C19.1851 13.6994 19.1501 13.5109 19.1588 13.3218C19.1674 13.1328 19.2196 12.9483 19.3112 12.7827C19.3411 12.702 19.3411 12.6133 19.3112 12.5327C19.1812 12.2127 19.0412 11.9027 18.8812 11.5927C18.8591 11.5558 18.8285 11.5248 18.792 11.5022C18.7554 11.4796 18.7141 11.4661 18.6712 11.4627C17.6104 11.5937 16.5345 11.4069 15.5798 10.926C14.6252 10.445 13.8348 9.69161 13.3087 8.76111C12.7826 7.83062 12.5445 6.76492 12.6245 5.69899C12.7046 4.63307 13.0992 3.61488 13.7583 2.7734C14.4175 1.93191 15.3116 1.30498 16.3273 0.97202C17.3431 0.639061 18.4348 0.615053 19.4642 0.903035C20.4936 1.19102 21.4144 1.77803 22.1099 2.58973C22.8054 3.40142 23.2444 4.40128 23.3712 5.46265C23.5086 6.41327 23.3805 7.38335 23.001 8.26572C22.6216 9.14809 22.0057 9.9084 21.2212 10.4627L21.0612 10.5727L20.9012 10.6827ZM18.0712 10.9027C19.0146 10.9027 19.9369 10.6229 20.7213 10.0988C21.5057 9.57462 22.1171 8.82966 22.4781 7.95806C22.8392 7.08645 22.9336 6.12737 22.7496 5.20208C22.5655 4.27679 22.1112 3.42685 21.4441 2.75975C20.777 2.09266 19.9271 1.63836 19.0018 1.4543C18.0765 1.27025 17.1174 1.36471 16.2458 1.72574C15.3742 2.08677 14.6292 2.69816 14.1051 3.48258C13.581 4.267 13.3012 5.18923 13.3012 6.13265C13.3039 7.39692 13.8073 8.60865 14.7012 9.50263C15.5952 10.3966 16.807 10.9 18.0712 10.9027ZM19.8212 13.4327C19.8212 13.4827 19.8212 13.5726 19.8812 13.6626L21.2012 16.8526C21.2231 16.9335 21.2629 17.0085 21.3176 17.072C21.3723 17.1354 21.4406 17.1858 21.5173 17.2194C21.5941 17.253 21.6774 17.2689 21.7612 17.266C21.8449 17.2631 21.927 17.2415 22.0012 17.2027L22.7612 16.8927C22.84 16.8647 22.9119 16.8203 22.9722 16.7624C23.0324 16.7045 23.0798 16.6345 23.111 16.557C23.1422 16.4795 23.1565 16.3962 23.1531 16.3127C23.1497 16.2292 23.1286 16.1474 23.0912 16.0727L21.7312 12.9027C21.7071 12.8267 21.6672 12.7567 21.6143 12.6971C21.5614 12.6375 21.4965 12.5896 21.4239 12.5567C21.3514 12.5237 21.2727 12.5063 21.193 12.5056C21.1133 12.5049 21.0343 12.5209 20.9612 12.5526C20.6812 12.6526 20.4012 12.7827 20.1212 12.9027C20.0254 12.9484 19.9434 13.0188 19.8836 13.1067C19.8239 13.1945 19.7886 13.2967 19.7812 13.4027L19.8212 13.4327ZM20.3812 11.0226L19.5112 11.3526L19.9412 12.2927L20.7612 11.9426L20.3812 11.0226Z"
      fill="currentColor"
    />
    <path
      d="M17.1306 22.1826C16.4106 22.0126 16.3906 21.9826 16.3906 21.2526V20.6526C16.3906 20.2126 16.5506 20.0526 17.0006 20.0126H17.1006C16.7306 19.4326 16.7306 19.3926 17.2306 18.8926L17.6706 18.4526C17.7206 18.3825 17.7865 18.3252 17.8631 18.2858C17.9396 18.2463 18.0245 18.2257 18.1106 18.2257C18.1967 18.2257 18.2816 18.2463 18.3582 18.2858C18.4347 18.3252 18.5007 18.3825 18.5506 18.4526L18.6406 18.5126C18.7106 17.9326 18.8306 17.8326 19.4206 17.8226H20.1306C20.5906 17.8226 20.7506 17.9726 20.7806 18.4326C20.786 18.4791 20.786 18.5261 20.7806 18.5726L20.9806 18.4126C21.0753 18.3233 21.2005 18.2736 21.3306 18.2736C21.4607 18.2736 21.586 18.3233 21.6806 18.4126C21.9606 18.6726 22.2206 18.9426 22.4806 19.2226C22.5269 19.2674 22.5637 19.321 22.5888 19.3803C22.6139 19.4395 22.6269 19.5033 22.6269 19.5676C22.6269 19.632 22.6139 19.6957 22.5888 19.755C22.5637 19.8143 22.5269 19.8679 22.4806 19.9126L22.3506 20.0726C23.0206 20.2226 23.0406 20.2526 23.0406 20.9626V21.6026C23.0406 22.0226 22.8806 22.1926 22.4506 22.2226H22.3106L22.4106 22.3726C22.4729 22.4213 22.5234 22.4835 22.5581 22.5545C22.5927 22.6256 22.6108 22.7036 22.6108 22.7826C22.6108 22.8617 22.5927 22.9397 22.5581 23.0108C22.5234 23.0818 22.4729 23.144 22.4106 23.1926C22.2006 23.4126 21.9906 23.6126 21.7806 23.8226C21.5706 24.0326 21.2506 24.1426 20.9006 23.8226L20.7806 23.7426C20.7806 23.8526 20.7806 23.9226 20.7806 23.9926C20.782 24.0574 20.7702 24.1217 20.7461 24.1817C20.722 24.2418 20.6859 24.2964 20.6401 24.3421C20.5944 24.3879 20.5398 24.424 20.4797 24.4481C20.4197 24.4722 20.3553 24.484 20.2906 24.4826C19.9142 24.5026 19.537 24.5026 19.1606 24.4826C19.0347 24.483 18.9132 24.4359 18.8205 24.3506C18.7278 24.2653 18.6707 24.1482 18.6606 24.0226C18.6606 23.9426 18.6606 23.8626 18.6606 23.7826H18.5906C18.1006 24.1526 18.0006 24.1426 17.5906 23.7026L17.0906 23.1926C17.0225 23.1435 16.967 23.0789 16.9287 23.0042C16.8904 22.9294 16.8705 22.8466 16.8705 22.7626C16.8705 22.6786 16.8904 22.5958 16.9287 22.5211C16.967 22.4463 17.0225 22.3817 17.0906 22.3326L17.1306 22.1826ZM20.1906 23.8126C20.1906 23.5726 20.1906 23.3326 20.4406 23.2326C20.6906 23.1326 21.1306 22.8326 21.3706 23.2326L22.0006 22.6326C21.7006 22.4426 21.7106 22.2026 21.8406 21.8726C21.8741 21.7303 21.9618 21.6068 22.0851 21.5283C22.2084 21.4498 22.3575 21.4227 22.5006 21.4526V20.6226C22.0006 20.5626 22.0106 20.5626 21.7606 19.9826C21.6406 19.6926 21.8706 19.5726 21.9906 19.4226L21.3806 18.8126C21.1906 19.1026 20.9506 19.0926 20.6206 18.9626C20.5484 18.9473 20.4801 18.9177 20.4196 18.8754C20.3591 18.8332 20.3077 18.7792 20.2686 18.7167C20.2294 18.6542 20.2032 18.5844 20.1915 18.5116C20.1798 18.4387 20.1829 18.3643 20.2006 18.2926H19.3406C19.4006 18.6526 19.2506 18.8226 18.9206 18.9726C18.5906 19.1226 18.3506 19.0726 18.1706 18.8326L17.5206 19.4026C17.8406 19.6126 17.8406 19.8526 17.7106 20.1826C17.5806 20.5126 17.3906 20.6626 17.0406 20.5926V21.4626C17.1851 21.4297 17.3368 21.4554 17.4624 21.5342C17.588 21.6129 17.6773 21.7382 17.7106 21.8826C17.7901 22.009 17.8168 22.1615 17.785 22.3074C17.7532 22.4532 17.6654 22.5808 17.5406 22.6626L18.1606 23.2826C18.4106 22.8126 18.7306 23.0426 19.0706 23.1626C19.4106 23.2826 19.3406 23.5226 19.3506 23.7626L20.1906 23.8126Z"
      fill="currentColor"
    />
    <path
      d="M18.0113 2.02257C18.821 2.01663 19.6143 2.2512 20.2906 2.69655C20.9669 3.1419 21.4958 3.77798 21.8103 4.5242C22.1247 5.27043 22.2106 6.09321 22.057 6.88828C21.9034 7.68335 21.5172 8.41492 20.9475 8.99031C20.3777 9.56571 19.6499 9.95902 18.8564 10.1204C18.0629 10.2818 17.2393 10.204 16.49 9.89688C15.7408 9.58976 15.0995 9.06712 14.6476 8.39521C14.1956 7.72329 13.9533 6.93235 13.9513 6.12258C13.9473 5.58642 14.0493 5.05476 14.2515 4.55815C14.4536 4.06154 14.7519 3.60976 15.1292 3.22878C15.5064 2.84779 15.9553 2.54511 16.4499 2.33811C16.9445 2.13111 17.4751 2.02387 18.0113 2.02257ZM20.9613 7.87258C21.3892 7.18553 21.5588 6.36868 21.4397 5.56807C21.3206 4.76747 20.9206 4.03534 20.3113 3.50258C19.6314 2.91295 18.7496 2.60959 17.8509 2.65621C16.9522 2.70282 16.1065 3.09578 15.4913 3.75258C14.9472 4.33671 14.626 5.09364 14.584 5.89078C14.5419 6.68792 14.7817 7.47445 15.2613 8.11258C15.4571 7.76566 15.7521 7.48513 16.1085 7.30696C16.4648 7.1288 16.8662 7.06111 17.2613 7.11258C17.9713 7.11258 18.6813 7.11258 19.3913 7.11258C19.8833 7.09668 20.3616 7.27648 20.7213 7.61258C20.8071 7.70425 20.8873 7.80109 20.9613 7.90258V7.87258ZM20.5613 8.43257C20.455 8.22019 20.2903 8.04249 20.0866 7.92029C19.883 7.79809 19.6487 7.73644 19.4113 7.74257H16.9413C16.7439 7.74184 16.5493 7.78983 16.3749 7.88228C16.2004 7.97473 16.0515 8.10879 15.9413 8.27257C15.6913 8.61257 15.7013 8.67257 16.0413 8.91257L16.2513 9.05257C16.9304 9.46411 17.7313 9.62728 18.5173 9.51421C19.3033 9.40114 20.0257 9.01885 20.5613 8.43257Z"
      fill="currentColor"
    />
    <path
      d="M18.2507 21.0726C18.3203 20.7261 18.5078 20.4144 18.7813 20.1904C19.0547 19.9665 19.3973 19.8441 19.7507 19.8441C20.1041 19.8441 20.4467 19.9665 20.7201 20.1904C20.9936 20.4144 21.1811 20.7261 21.2507 21.0726C21.2953 21.2947 21.2901 21.5239 21.2354 21.7436C21.1807 21.9634 21.0779 22.1683 20.9344 22.3436C20.7909 22.5188 20.6103 22.66 20.4056 22.7569C20.2009 22.8538 19.9772 22.9041 19.7507 22.9041C19.5242 22.9041 19.3005 22.8538 19.0959 22.7569C18.8912 22.66 18.7105 22.5188 18.567 22.3436C18.4235 22.1683 18.3207 21.9634 18.266 21.7436C18.2113 21.5239 18.2061 21.2947 18.2507 21.0726ZM18.8807 21.0726C18.878 21.3096 18.9689 21.5381 19.1337 21.7085C19.2984 21.8789 19.5238 21.9774 19.7607 21.9826C19.9979 21.9802 20.2249 21.8862 20.3945 21.7204C20.564 21.5546 20.663 21.3297 20.6707 21.0926C20.6473 20.8716 20.5429 20.667 20.3777 20.5183C20.2124 20.3696 19.998 20.2874 19.7757 20.2874C19.5534 20.2874 19.339 20.3696 19.1737 20.5183C19.0085 20.667 18.9041 20.8716 18.8807 21.0926V21.0726Z"
      fill="currentColor"
    />
    <path
      d="M19.7411 5.32258C19.7411 5.63196 19.6491 5.93435 19.4767 6.19131C19.3044 6.44826 19.0596 6.64818 18.7734 6.76565C18.4871 6.88311 18.1725 6.91281 17.8693 6.85097C17.5662 6.78914 17.2883 6.63858 17.0709 6.4184C16.8536 6.19823 16.7066 5.91839 16.6487 5.61448C16.5908 5.31056 16.6245 4.99628 16.7457 4.7116C16.8668 4.42692 17.0699 4.18469 17.329 4.01571C17.5882 3.84672 17.8917 3.75861 18.2011 3.76258C18.4047 3.76389 18.6059 3.80528 18.7935 3.88439C18.981 3.9635 19.1512 4.07876 19.2942 4.22362C19.4372 4.36848 19.5503 4.5401 19.6269 4.72866C19.7036 4.91722 19.7424 5.11903 19.7411 5.32258ZM19.1011 5.32258C19.1037 5.20093 19.0822 5.07996 19.0378 4.96668C18.9934 4.8534 18.927 4.75004 18.8423 4.66262C18.7577 4.57519 18.6565 4.50543 18.5448 4.45736C18.433 4.40929 18.3128 4.38387 18.1911 4.38257C18.0068 4.37862 17.8255 4.42953 17.6701 4.52885C17.5148 4.62816 17.3925 4.7714 17.3188 4.94036C17.2451 5.10932 17.2232 5.29638 17.256 5.47779C17.2888 5.6592 17.3747 5.82677 17.503 5.95922C17.6312 6.09167 17.7959 6.18303 17.9761 6.22169C18.1564 6.26034 18.3441 6.24456 18.5153 6.17634C18.6866 6.10811 18.8337 5.99052 18.938 5.83851C19.0423 5.6865 19.0991 5.50692 19.1011 5.32258Z"
      fill="currentColor"
    />
  </svg>
);

export const SidebarDashboardRoutes: ISidebarDashboardRoutes = {
  title: "MAIN",
  routes: [
    {
      id: 1,
      path: "/",
      title: "Dashboard",
      icon: DashboardIcon,
      name: "Dashboard",
    },
    {
      id: 2,
      path: "/booking-management",
      title: "Booking Management",
      icon: BookingsIcon,
      name: "Booking Management",
    },
    {
      id: 3,
      path: "/room-management",
      title: "Room Management",
      icon: BookingsIcon,
      name: "Room Management",
    },
    {
      id: 4,
      path: "/guest-management",
      title: "Guest Management",
      icon: GuestIcon,
      name: "Guest Management",
    },
    {
      id: 5,
      path: "/availability-calendar",
      title: "Availability Calendar",
      icon: AvailabilityIcon,
      name: "Availability Calendar",
    },
    {
      id: 6,
      path: "/wakeup-module",
      title: "Wake Up Module ",
      icon: WakeupIcon,
      name: "Wakeup",
    },
  ],
};

export const SidebarHousekeepingRoute: ISidebarDashboardRoutes = {
  title: "Housekeeping",
  routes: [
    {
      id: 7,
      path: "#",
      title: "House Keeping",
      icon: HouseKeepingIcon,
      name: "HouseKeeping",
      children: [
        {
          path: "/service-ticketing",
          title: "Service Ticketing ",
          id: 80000,
          name: "Service Ticketing",
        },
        {
          path: "/room-cleaning",
          title: "Room Cleaning ",
          id: 800001,
          name: "Room Cleaning",
        },
        {
          path: "/room-inspection",
          title: "Room Inspection",
          id: 81111,
          name: "Room Inspection",
        },
        {
          path: "/lost-found",
          title: "Lost & Found",
          id: 83111,
          name: "Lost & Found",
        },
        {
          path: "/maintenance",
          title: "Maintenance",
          id: 85111,
          name: "Maintenance",
        },
      ],
    },
    {
      id: 8,
      path: "#",
      title: "Laundry Management",
      name: "Laundry Management",
      icon: LaundaryIcon,
      children: [
        {
          path: "/laundry",
          title: "Guest Laundry",
          id: 900001,
          name: "Guest Laundry",
        },
        {
          path: "/laundry-category",
          title: "Laundary Category",
          id: 900002,
          name: "Laundary Category",
        },
        {
          path: "/laundry-services",
          title: "Laundary Services",
          id: 900003,
          name: "Laundary Services",
        },
        {
          path: "/laundry-inventory",
          title: "Laundry Inventory",
          id: 900004,
          name: "Laundry Inventory",
        },
      ],
    },
  ],
};

export const SidebarFinanceSection: ISidebarDashboardRoutes = {
  title: "Finance",
  routes: [
    {
      id: 9,
      path: "#",
      title: "Expenses Management",
      name: "Expenses Management",
      icon: ExpenseIcon,
      children: [
        {
          path: "/expense-management/expenses",
          title: "Expenses",
          id: 70000,
          name: "Expenses",
        },
        {
          path: "/expense-management/category",
          title: "Expenses Category",
          id: 71111,
          name: "Expense Category",
        },
      ],
    },
    // {
    //   id: 18,
    //   path: "#",
    //   title: "Financial Management",
    //   icon: ExpenseIcon,
    //   children: [{ path: "/Bank", title: "Bank ", id: 98000 }],
    // },

    {
      id: 999,
      path: "#",
      title: "Financial Management",
      name: "Financial Management",
      icon: ExpenseIcon,
      children: [
        { path: "/Bank", title: "Bank ", id: 98000, name: "Bank" },
        {
          path: "/transaction-adjustment",
          title: "Transaction Adjustment",
          id: 700022,
          name: "Transaction Adjustment",
        },
        // { path: "/finance-category", title: "Finance Category", id: 700023 },
      ],
    },
    // Income Management section removed
    // {
    //   id: 12,
    //   path: "#",
    //   title: "Income Management",
    //   icon: InventoryIcon,
    //   name:"Income Management",
    //   children: [
    //     { path: "/income-list",name:"Income list", title: "Income List", id: 100011 },
    //     { path: "/income-category", name:"Income Category",title: "Income Category", id: 100022 },
    //     { path: "/income-type-list",name:"Income Type List", title: "Income Type List", id: 100023 },
    //   ],
    // },
  ],
};

export const SidebarDailyScheduleRoutes: ISidebarDashboardRoutes = {
  title: "Operation",
  routes: [
    {
      id: 10,
      path: "#",
      title: "Store Management",
      name: "Store Management",
      icon: CabIcon,
      children: [
        {
          path: "/store/dashboard",
          title: "Store Dashboard",
          id: 89999,
          name: "Store Dashboard",
        },
        {
          path: "/store/sub-store",
          name: "Sub Store",
          title: "Sub Store",
          id: 91111,
        },
      ],
    },
    {
      id: 11,
      path: "#",
      title: "Restaurant Management",
      name: "Restaurant Management",
      icon: FoodOrderingIcon,
      children: [
        {
          path: "/restaurant/dashboard",
          title: "Restaurant Dashboard",
          id: 110001,
          name: "Restaurant Dashboard",
        },
        {
          path: "/restaurant/food-ordering",
          title: "Food Ordering",
          id: 110002,
          name: "Food Ordering",
        },
        {
          path: "/restaurant/inventory",
          title: "Restaurant Inventory",
          id: 110003,
          name: "Restaurant Inventory",
        },
      ],
    },

    {
      id: 12,
      path: "#",
      title: "Inventory Management",
      icon: InventoryIcon,
      name: "Inventory Management",
      children: [
        {
          path: "/inventory-management/inventory-list",
          title: "Inventory List",
          name: "Inventory List",
          id: 100002,
        },
        {
          path: "/inventory-management/product-category",
          title: "Product Category",
          name: "Product Category",
          id: 100003,
        },
        {
          path: "/inventory-management/product-list",
          title: "Product List",
          name: "Product List",
          id: 100004,
        },
        {
          path: "/inventory-management/purchase",
          title: "Purchase",
          name: "Purchase",
          id: 100005,
        },
      ],
    },

    {
      id: 13,
      path: "#",
      title: "Activity",
      name: "Activity",
      icon: InventoryIcon,
      children: [
        {
          path: "/activity/activity-list",
          title: "Activity List",
          id: 100031,
          name: "Activity List",
        },
        {
          path: "/activity/activity-booking",
          title: "Activity Booking",
          name: "Activity Booking",
          id: 100032,
        },
      ],
    },

    {
      id: 14,
      path: "#",
      title: "Cab Facility",
      name: "Cab Facility",
      icon: CabIcon,
      children: [
        { path: "/cab-list", title: "Cab List", id: 90000, name: "Cab List" },
        {
          path: "/cab-booking",
          name: "Cab Booking",
          title: "Cab Booking",
          id: 91111,
        },
      ],
    },
    // {
    //   id: 15,
    //   path: "#",
    //   title: "Channel Manager",
    //   icon: ChannelIcon,
    //   children: [
    //     { path: "/channel", title: "Channel", id: 111000 },
    //     { path: "/channel-category", title: "Channel Category", id: 122222000 },
    //   ],
    // },
    {
      id: 16,
      path: "#",
      title: "Reports",
      name: "Report",
      icon: ReportsIcon,
      children: [
        {
          path: "/reports/guest-reports",
          title: "Guest",
          id: 1000050,
          name: "Guest",
        },
        {
          path: "/reports/expenses-reports",
          title: "Expenses",
          id: 1000052,
          name: "Expenses",
        },
      ],
    },
  ],
};

export const SidebarServiceDepartmentsRoutes: ISidebarDashboardRoutes = {
  // title: 'Service Departments',
  routes: [
    {
      id: 17,
      // path: "/channel-manager",
      title: "Channel Manager",
      icon: ChannelIcon,
      children: [
        { path: "/channel", title: "Channel", id: 111000 },
        { path: "/channel-category", title: "Channel Category", id: 122222000 },
      ],
    },
  ],
};

export const HrmsRoutes: ISidebarDashboardRoutes = {
  title: "HRMS",
  routes: [
    // {
    //   id: 18,
    //   path: "/employee-configuration",
    //   title: "Employee Configuration",
    //   icon: HrIcon,
    // },
    {
      id: 19,
      path: "#",
      title: "HR Management",
      name: "HR Management",

      icon: HrIcon,
      children: [
        {
          path: "/hr-management/employee-management",
          title: "All Employees",
          name: "All Employees",
          id: 1110,
        },
        {
          path: "/hr-management/employee-schedule",
          title: "Employee Schedule",
          name: "Employee Schedule",
          id: 111011,
        },
        {
          path: "/hr-management/employee-attendance",
          title: "Employee Attendance",
          name: "employee attendance",
          id: 122222,
        },
        {
          path: "/hr-management/employee-payroll",
          title: "Employee Payroll",
          name: "employee payroll",
          id: 133333,
        },
        {
          path: "/hr-management/employee-configuration",
          title: "Employee Configuration",
          name: "employee config",
          id: 1444,
        },
        {
          path: "/hr-management/leave-tracking",
          title: "Leave Tracking",
          name: "leave tracking",
          id: 15555,
        },
        {
          path: "/user-logs",
          name: "user logs",
          title: "User Logs",
          id: 15855,
        },
      ],
    },
    {
      icon: HrIcon,
      id: 1010,
      path: "/customer-complain-box",
      title: "Customer Complaint Box",
      name: "Customer Complaint Box",
    },
    // {
    //   id: 20,
    //   path: "/employee-duty-assign",
    //   title: "Employee Duty Assign",
    //   icon: HrIcon,
    //   children: [
    //     { path: "/assigned-list", title: "Assigned List", id: 1213 },
    //     { path: "/shift-list", title: "Shift List", id: 13134 },
    //     { path: "/roster-list", title: "Roster List", id: 14134 },
    //   ],
    // },
  ],
};
export const SettingRoutes: ISidebarDashboardRoutes = {
  title: "SETTINGS",
  routes: [
    {
      id: 21,
      path: "#",
      title: "Hotel Configurations",
      icon: HrIcon,
      name: "Hotel config",
      children: [
        {
          id: 711000,
          path: "/hotel-config/bed-list",
          title: "Bed List",
          name: "Bed List",
        },
        {
          id: 722222000,
          path: "/hotel-config/floor-plan",
          title: "Floor Plan List",
          name: "floor plan list",
        },
        {
          id: 74444000,
          path: "/hotel-config/room-facilities",
          title: "Room Amenities",
          name: "room amenity",
        },
        {
          id: 733333000,
          path: "/hotel-config/room-list",
          title: "Room List",
          name: "room list",
        },

        {
          id: 7444400777,
          path: "/hotel-config/room-services",
          title: "Room Services",
        },
        {
          id: 74444002,
          path: "/hotel-config/room-package",
          title: "Room Package",
        },

        {
          id: 74444001,
          path: "/hotel-config/membership-tiers",
          title: "Membership Tiers",
          name: "Membership Tiers",
        },

        // { id: 74444003, path: "/hotel-config/room-type", title: "Room Type" },

        {
          id: 74444005,
          path: "/hotel-config/price-manager",
          title: "Price Manager",
        },
      ],
    },
    {
      id: 22,
      path: "/permissions",
      title: "Permissions",
      icon: PermissionIcon,
      name: "Permission",
    },
  ],
};
