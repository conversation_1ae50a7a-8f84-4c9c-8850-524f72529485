import { useQuery } from '@tanstack/react-query';
import { checkSetupStatus, SetupStatus } from '../utils/setupUtils';

export const useSetupStatus = () => {
  return useQuery<SetupStatus>({
    queryKey: ['hotel-setup-status'],
    queryFn: checkSetupStatus,
    refetchInterval: 5000, // Refetch every 5 seconds
    staleTime: 0, // Always consider data stale to force fresh fetches
    cacheTime: 0, // Don't cache the data
    refetchOnMount: true, // Always refetch when component mounts
    refetchOnWindowFocus: true, // Refetch when window gains focus
  });
};

export default useSetupStatus;
