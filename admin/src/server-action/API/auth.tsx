import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { apiClient } from "../utils/ApiGateway";
import { toast } from "react-toastify";

type TLogin = {
  email: string;
  password: string;
};

export const useLogin = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationKey: ["login"],
    mutationFn: async (userData: TLogin) => {
      const response = await apiClient.post("/login", userData, {
        headers: { "Content-Type": "application/json" },
      });
      return response.data;
    },

    onSuccess: (data) => {
      if (
        data?.data?.user?.role === "superAdmin" ||
        data?.data?.user?.role === "admin"
      ) {
        localStorage.setItem("_UPLFMMATRIX", data?.data?.token);
        localStorage.setItem("user", JSON.stringify(data?.data?.user));
        localStorage.setItem(
          "_GBLFMATRIX",
          "F1L22S.eLA000c2Vy2N.lIjoic3VwZXJhZG-iJTaG-HVkaW9uZXBhbC5jb20iL"
        );
        localStorage.setItem("_TFDSFUMATRIX", "HVkaW9uZXBhbC5jb20iL");
        toast.success("Login Successful");
      } else {
        console.log(data);
        toast.error("Access Denied!!!");
        return;
      }

      queryClient.setQueryData(["auth"], {
        token: data.token,
        user: data.user,
      });
      queryClient.invalidateQueries({ queryKey: ["login"] });
    },
  });
};

export const GetAllUser = () => {
  return useQuery<any>({
    queryKey: ["user"],
    queryFn: async () => {
      const response = await apiClient.get("user");
      return response?.data?.data;
    },
  });
};

export const useCreateuser = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (userData: FormData) =>
      apiClient.post("login", userData, {
        headers: {
          "Content-Type": "multipart/form-data",
        },
      }),
    onError: (error) => {
      toast.error(error as unknown as string);
    },

    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["user"] });
    },
  });
};

export const useDeleteUser = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => apiClient.delete(`user/${id}`),
    onError: (error: any) => {
      toast.error(error || "Failed to delete user");
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["user"] });
      toast.success("User deleted successfully");
    },
  });
};

export const useUpdateUser = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ userData }: { userData: any }) =>
      apiClient.patch(`profile/update/`, userData),
    onError: (error: any) => {
      toast.error(error || "Failed to update user");
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["user"] });
      toast.success("User updated successfully");
    },
  });
};

export const useUpdateOtherUser = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ userData, userId }: { userData: any; userId: string }) =>
      apiClient.patch(`user/${userId}`, userData, {
        headers: {
          "Content-Type": "multipart/form-data",
        },
      }),
    onError: (error: any) => {
      toast.error(error || "Failed to update user");
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["user"] });
      toast.success("User updated successfully");
    },
  });
};

export const useRegisterUser = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationKey: ["register"],
    mutationFn: async (userData: any) => {
      const res = await apiClient.post("/register", userData, {
        headers: {
          "Content-Type": "multipart/form-data",
        },
      });
      return res.data;
    },
    onError: (error: any) => {
      toast.error(error || "Failed to register user");
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["user"] });
      toast.success("User registered successfully");
    },
  });
};
