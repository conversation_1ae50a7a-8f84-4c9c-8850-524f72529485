import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { apiClient } from "../../utils/ApiGateway";
import { toast } from "react-toastify";

export const useGetAmenityCategories = () => {
  return useQuery({
    queryKey: ["amenity-categories"],
    queryFn: async () => {
      const response = await apiClient.get("/amenities/category");
      return response?.data?.data;
    },
  });
};

export const useCreateAmenityCategory = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (categoryData: any) => {
      const response = await apiClient.post(
        "/amenities/category",
        categoryData
      );
      return response?.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["amenity-categories"] });
      toast.success("Amenity category created successfully");
    },
    onError: () => {
      toast.error("Error creating amenity category");
    },
  });
};

export const useUpdateAmenityCategory = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async ({
      categoryData,
      _id,
    }: {
      categoryData: any;
      _id: string;
    }) => {
      if (!_id) throw new Error("Amenity category ID is required");
      return apiClient.patch(`/amenities/category/${_id}`, categoryData);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["amenity-categories"] });
      toast.success("Amenity category updated successfully");
    },
    onError: () => {
      toast.error("Error updating amenity category");
    },
  });
};

export const useDeleteAmenityCategory = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (_id: string) => {
      if (!_id) throw new Error("Amenity category ID is required");
      return apiClient.delete(`/amenities/category/${_id}`);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["amenity-categories"] });
      toast.success("Amenity category deleted successfully");
    },
    onError: () => {
      toast.error("Error deleting amenity category");
    },
  });
};

export const useGetAmenities = () => {
  return useQuery({
    queryKey: ["amenities"],
    queryFn: async () => {
      const response = await apiClient.get("/amenities");
      return response?.data?.data;
    },
  });
};

export const useCreateAmenities = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (amenities: any) => {
      const response = await apiClient.post("/amenities", amenities);
      return response?.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["amenities"] });
      toast.success("Amenities created successfully");
    },
    onError: (error: any) => {
      toast.error(`${error || "Error Creating Amenities"}`);
    },
  });
};

export const useUpdateAmenity = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async ({
      aminityData,
      _id,
    }: {
      aminityData: any;
      _id: string;
    }) => {
      if (!_id) throw new Error("Amenity ID is required");
      return apiClient.patch(`amenities/${_id}`, aminityData);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["amenities"] });
      toast.success("Amenity Updated Successfully");
    },
    onError: () => {
      toast.error("Error Updating Amenity");
    },
  });
};

export const useDeleteAmenity = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (_id: string) => {
      if (!_id) throw new Error("Amenity ID is required");
      return apiClient.delete(`amenities/${_id}`);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["amenities"] });
      toast.success("Amenity Deleted Successfully");
    },
    onError: () => {
      toast.error("Error Deleting Amenity");
    },
  });
};
