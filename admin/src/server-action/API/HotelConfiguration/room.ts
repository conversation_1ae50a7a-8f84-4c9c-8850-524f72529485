import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { apiClient } from "../../utils/ApiGateway";
import { toast } from "react-toastify";
import { IRoom, IRoomType } from "../../../Interface/room.interface";

export const useGetAllRoomTypes = () => {
  return useQuery<IRoomType[]>({
    queryKey: ["roomType"],
    queryFn: async () => {
      const response = await apiClient.get("roomtype");
      return response.data?.data;
    },
  });
};

export const useGetRoomTypeById = (id: string) => {
  return useQuery<IRoomType | null>({
    queryKey: ["roomType", id],
    queryFn: async () => {
      if (!id) return null;
      const response = await apiClient.get(`roomtype/${id}`);
      return response.data?.data;
    },
    enabled: !!id,
  });
};

export const useCreateRoomType = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (roomTypeData: IRoomType) => {
      return apiClient.post("roomtype", roomTypeData);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["roomType"] });
      toast.success("Room Type Created Successfully");
    },
    onError: () => {
      toast.error("Error Creating Room Type");
    },
  });
};

export const useUpdateRoomType = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async ({
      roomTypeData,
      _id,
    }: {
      roomTypeData: IRoomType;
      _id: string;
    }) => {
      if (!_id) throw new Error("Room Type ID is required");
      return apiClient.patch(`roomtype/${_id}`, roomTypeData);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["roomType"] });
      toast.success("Room Type Updated Successfully");
    },
    onError: () => {
      toast.error("Error Updating Room Type");
    },
  });
};

export const useDeleteRoomType = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (_id: string) => {
      if (!_id) throw new Error("Room Type ID is required");
      return apiClient.delete(`roomtype/${_id}`);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["roomType"] });
      toast.success("Room Type Deleted Successfully");
    },
    onError: () => {
      toast.error("Error Deleting Room Type");
    },
  });
};

export const useGetAllRooms = () => {
  return useQuery<IRoom[]>({
    queryKey: ["room"],
    queryFn: async () => {
      const response = await apiClient.get("room");

      // Process the response to ensure roomNo is available
      const rooms = response.data?.data || [];

      // Map through the rooms to ensure each has a roomNo property
      const processedRooms = rooms.map((room: any) => {
        // If room is already properly formatted, return it as is
        if (room && room.roomNo) {
          return room;
        }

        // If room has nested structure with roomNo
        if (room && room.room && room.room.roomNo) {
          return {
            ...room,
            roomNo: room.room.roomNo,
            _id: room._id || room.room._id,
          };
        }

        // Default case - return the room as is
        return room;
      });

      return processedRooms;
    },
  });
};

export const useGetRoomById = (id: string) => {
  return useQuery<IRoom>({
    queryKey: ["room", id],
    queryFn: async () => {
      if (!id) return null;
      const response = await apiClient.get(`room/${id}`);
      return response.data?.data;
    },
    enabled: !!id,
  });
};

export const useCreateRoom = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (roomData: any) => {
      let config = {};
      if (roomData instanceof FormData) {
        config = {
          headers: {
            "Content-Type": "multipart/form-data",
          },
        };
      }
      const res = await apiClient.post("room", roomData, config);
      return res?.data?.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["room"] });
      toast.success("Room Created Successfully");
    },
    onError: (error: any) => {
      toast.error(`${error || "Error Creating Room"}`);
    },
  });
};

export const useUpdateRoom = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async ({ roomData, _id }: { roomData: any; _id: string }) => {
      const config =
        roomData instanceof FormData
          ? { headers: { "Content-Type": "multipart/form-data" } }
          : { headers: { "Content-Type": "application/json" } };

      if (!_id) throw new Error("Room ID is required");
      const res = await apiClient.patch(`room/${_id}`, roomData, config);
      return res?.data?.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["room"] });
      toast.success("Room Updated Successfully");
    },
    onError: (error: any) => {
      toast.error(error || "Error Updating Room");
    },
  });
};

export const useDeleteRoom = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (_id: string) => {
      if (!_id) throw new Error("Room ID is required");
      return apiClient.delete(`room/${_id}`);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["room"] });
      toast.success("Room Deleted Successfully");
    },
    onError: () => {
      toast.error("Error Deleting Room");
    },
  });
};
