import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { apiClient } from "../utils/ApiGateway";
import { toast } from "react-toastify";

export const useGetAllVendor = () => {
  return useQuery({
    queryKey: ["vendor"],
    queryFn: async () => {
      const res = await apiClient.get("vendor");
      return res.data.data;
    },
  });
};

export const useCreateVendorMutation = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationKey: ["vendor"],
    mutationFn: async (data: any) => {
      const response = await apiClient.post("vendor", data);
      return response.data;
    },

    onSuccess: () => {
      toast.success("Vendor Created Successfully");
      queryClient.invalidateQueries({ queryKey: ["vendor"] });
    },
    onError: (error: any) => {
      toast.error(`${error || "Error creating vendor"} `);
    },
  });
};

export const useUpdateVendorMutation = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationKey: ["vendor"],
    mutationFn: async ({ id, body }: { id: string; body: any }) => {
      const response = await apiClient.patch(`vendor/${id}`, body);
      return response.data;
    },

    onSuccess: () => {
      toast.success("Vendor Updated Successfully");
      queryClient.invalidateQueries({ queryKey: ["vendor"] });
    },
    onError: () => {
      toast.error("Error updating vendor");
    },
  });
};

export const useDeleteVendor = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationKey: ["vendor"],
    mutationFn: async (id: string) => {
      const response = await apiClient.delete(`vendor/${id}`);
      return response.data;
    },
    onSuccess: () => {
      toast.success("Vendor Deleted Successfully");
      queryClient.invalidateQueries({ queryKey: ["vendor"] });
    },
    onError: () => {
      toast.error("Error deleting vendor");
    },
  });
};
