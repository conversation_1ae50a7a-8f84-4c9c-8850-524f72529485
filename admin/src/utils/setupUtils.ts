import { apiClient } from "../server-action/utils/ApiGateway";

// Helper functions to check if configurations exist
const checkHotelConfiguration = async (): Promise<boolean> => {
  try {
    // Check if bed types exist
    const bedResponse = await apiClient.get("/bed");
    const beds = bedResponse.data?.data || [];

    // Check if floor plans exist
    const floorResponse = await apiClient.get("/floor");
    const floors = floorResponse.data?.data || [];

    console.log("Hotel configuration check:", {
      beds: beds.length,
      floors: floors.length
    });

    return beds.length > 0 && floors.length > 0;
  } catch (error) {
    console.error("Error checking hotel configuration:", error);
    return false;
  }
};

const checkRoomConfiguration = async (): Promise<boolean> => {
  try {
    // Check if room types exist
    const roomTypeResponse = await apiClient.get("/room-type");
    const roomTypes = roomTypeResponse.data?.data || [];

    // Check if rooms exist
    const roomResponse = await apiClient.get("/room");
    const rooms = roomResponse.data?.data || [];

    console.log("Room configuration check:", {
      roomTypes: roomTypes.length,
      rooms: rooms.length
    });

    return roomTypes.length > 0 && rooms.length > 0;
  } catch (error) {
    console.error("Error checking room configuration:", error);
    return false;
  }
};

// Interface for setup status based on the actual API implementation
export interface SetupStatus {
  hotelConfigured: boolean;
  roomsConfigured: boolean;
  usersConfigured: boolean;
  servicesConfigured: boolean;
  amenitiesConfigured: boolean;
  lastUpdated: string;
  overallCompletion?: number; // Calculated field for UI display
}

// Check if hotel setup is complete
export const checkSetupStatus = async (): Promise<SetupStatus> => {
  try {
    console.log("Attempting to fetch setup status from /setup-status");
    // Add cache busting parameter to ensure fresh data
    const response = await apiClient.get("/setup-status", {
      params: { _t: Date.now() }
    });
    const setupData = response.data.data;
    console.log("Setup status API response:", response.data);
    console.log("Setup data:", setupData);

    // Validate that we have the expected data structure
    if (!setupData) {
      console.warn("No setup data received from API");
      throw new Error("No setup data received");
    }

    const configuredItems = [
      setupData.hotelConfigured,
      setupData.roomsConfigured,
      setupData.usersConfigured,
      setupData.servicesConfigured,
      setupData.amenitiesConfigured,
    ];

    const completedItems = configuredItems.filter(
      (item) => item === true
    ).length;
    const overallCompletion = Math.round(
      (completedItems / configuredItems.length) * 100
    );

    console.log("Processed setup status:", {
      ...setupData,
      overallCompletion,
    });

    return {
      ...setupData,
      overallCompletion,
    };
  } catch (error) {
    console.error("Error checking setup status:", error);
    console.log("API call failed, checking if we can determine setup status from existing data...");

    // Try to determine setup status by checking if we have actual data
    try {
      const hasHotelData = await checkHotelConfiguration();
      const hasRoomData = await checkRoomConfiguration();

      console.log("Fallback setup check results:", {
        hasHotelData,
        hasRoomData
      });

      return {
        hotelConfigured: hasHotelData,
        roomsConfigured: hasRoomData,
        usersConfigured: false,
        servicesConfigured: false,
        amenitiesConfigured: false,
        lastUpdated: new Date().toISOString(),
        overallCompletion: hasHotelData && hasRoomData ? 40 : 0,
      };
    } catch (fallbackError) {
      console.error("Fallback setup check also failed:", fallbackError);
      return {
        hotelConfigured: false,
        roomsConfigured: false,
        usersConfigured: false,
        servicesConfigured: false,
        amenitiesConfigured: false,
        lastUpdated: new Date().toISOString(),
        overallCompletion: 0,
      };
    }
  }
};

// Check if hotel setup is complete enough to use the system
export const isSetupComplete = async (): Promise<boolean> => {
  try {
    console.log("=== SETUP COMPLETION CHECK STARTED ===");

    // Get setup status from API
    const setupStatus = await checkSetupStatus();
    console.log("Raw setup status from API:", setupStatus);

    // TEMPORARY FIX: For development/testing, you can force the setup to be considered complete
    // by uncommenting the next line
    // return true;

    // Define the minimum requirements for setup to be considered complete
    // We require hotel and rooms to be configured at minimum
    const requiredComponents = [
      setupStatus.hotelConfigured,
      setupStatus.roomsConfigured,
    ];

    console.log("Required components check:", {
      hotelConfigured: setupStatus.hotelConfigured,
      roomsConfigured: setupStatus.roomsConfigured,
      requiredComponents,
    });

    // For more thorough validation, we could also check:
    // 1. If there are actual bed types defined
    // 2. If there are actual floor plans defined
    // 3. If there are actual room types defined
    // 4. If there are actual rooms defined
    // This would require additional API calls to check counts

    // For now, we'll rely on the API's setup status flags
    // Check if all required components are complete
    const isComplete = requiredComponents.every(
      (component) => component === true
    );

    console.log("=== SETUP COMPLETION CHECK RESULT ===", {
      hotelConfigured: setupStatus.hotelConfigured,
      roomsConfigured: setupStatus.roomsConfigured,
      requiredComponents,
      isComplete,
      timestamp: new Date().toISOString(),
    });

    if (isComplete) {
      console.log("✅ Setup is COMPLETE - should allow access to main app");
    } else {
      console.log("❌ Setup is INCOMPLETE - will redirect to setup page");
    }

    return isComplete;
  } catch (error) {
    console.error("❌ Error checking if setup is complete:", error);
    // For development/testing, you can return true here to bypass the setup check
    // when there's an error
    // return true;
    console.log("❌ Returning false due to error");
    return false;
  }
};

// Refresh setup status (admin only)
export const refreshSetupStatus = async (): Promise<SetupStatus> => {
  try {
    console.log("🔄 Refreshing setup status...");
    const response = await apiClient.post("/setup-status/refresh");
    console.log("🔄 Setup status refresh response:", response.data);
    return response.data.data;
  } catch (error) {
    console.error("❌ Error refreshing setup status:", error);
    throw error;
  }
};

// Clear all caches and force fresh setup check
export const clearSetupCache = () => {
  console.log("🧹 Clearing setup cache...");
  // Clear localStorage cache if any
  localStorage.removeItem('hotel-setup-status');
  // Force a page reload to clear all React Query caches
  window.location.reload();
};

// Update a specific part of the setup status
export const updateSetupStatus = async (
  updates: Partial<SetupStatus>
): Promise<SetupStatus> => {
  try {
    // In a real implementation, this would call the API to update the status
    const response = await apiClient.patch("/setup-status", updates);
    return response.data.data;
  } catch (error) {
    console.error("Error updating setup status:", error);
    // For development, we'll simulate a successful update
    console.log("Simulating successful update with:", updates);
    // Return a mock response
    return {
      hotelConfigured: updates.hotelConfigured ?? false,
      roomsConfigured: updates.roomsConfigured ?? false,
      usersConfigured: updates.usersConfigured ?? false,
      servicesConfigured: updates.servicesConfigured ?? false,
      amenitiesConfigured: updates.amenitiesConfigured ?? false,
      lastUpdated: new Date().toISOString(),
      overallCompletion: 0, // This will be calculated by the API
    };
  }
};
