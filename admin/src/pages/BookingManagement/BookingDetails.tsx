import moment from "moment";
import { useState } from "react";
import { useParams } from "react-router-dom";
import { CustomTabs } from "../../components/CustomTab";
import { useGetBookingById } from "../../server-action/API/BookingManagement/BookingManagement";
import Details from "./components/Details";
import Folio from "./components/Folio";
import Payment from "./components/Payment";

const tabs = ["Folio", "Details", "Payments"];

export function getStatus(checkIn: string, checkOut: string): string {
  const today = moment().startOf("day");
  const checkInDate = moment(checkIn).startOf("day");
  const checkOutDate = moment(checkOut).startOf("day");

  if (today.isSameOrAfter(checkOutDate)) {
    return "checkout";
  } else if (today.isSameOrAfter(checkInDate) && today.isBefore(checkOutDate)) {
    return "checkin";
  } else {
    return "upcoming";
  }
}

const BookingDetails = () => {
  const { id } = useParams();
  const [selectedTab, setSelectedTab] = useState("Folio");
  const { data: bookingData } = useGetBookingById(id ?? "");

  const renderTabContent = () => {
    switch (selectedTab) {
      case "Folio":
        return <Folio booking={bookingData?.data} />;
      case "Details":
        return <Details booking={bookingData?.data} />;
      case "Payments":
        return <Payment booking={bookingData?.data} />;
      default:
        return null;
    }
  };
  const status = getStatus(
    bookingData?.data?.checkIn,
    bookingData?.data?.checkOut
  );
  return (
    <div className="flex flex-col w-full h-full gap-4">
      <div className="w-full p-4 bg-white shadow-sm rounded-xl">
        <CustomTabs
          tabs={tabs}
          defaultTab="Folio"
          onTabChange={(tab: any) => setSelectedTab(tab)}
        />
      </div>
      <section>
        <div className="flex items-center justify-center h-full gap-4 w-fit">
          <h1 className="text-sm font-semibold">Status</h1>
          <button
            className={`w-full h-fit ${
              status === "checkin" ? "bg-[#28A745]" : "bg-red"
            } py-1 px-2 text-[12px] rounded-md tracking-widest text-white`}
          >
            {status}
          </button>
        </div>
      </section>

      <div className="flex-1">{renderTabContent()}</div>
    </div>
  );
};

export default BookingDetails;
