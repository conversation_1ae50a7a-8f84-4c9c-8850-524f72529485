import * as Yup from "yup";

// Create a function that returns the validation schema based on whether we're in edit mode
export const getBookingValidationSchema = (isEditMode = false) => {
  return Yup.object().shape({
    // Custom validation to ensure either checkin or reservationDate is provided
    _checkinOrReservation: Yup.mixed().test(
      "checkin-or-reservation",
      "Either Check-in date or Reservation date is required",
      function (_, context) {
        const { checkin, reservationDate } = context.parent;
        return !!checkin || !!reservationDate;
      }
    ),
    // Reservation Details Section
    checkin: Yup.date().nullable(),
    checkout: Yup.date()
      .nullable()
      .test(
        "checkout-after-checkin",
        "Check-out date must be after check-in date",
        function (checkout, context) {
          const { checkin } = context.parent;
          // If either checkout or checkin is not provided, skip validation
          if (!checkout || !checkin) return true;

          // Parse dates to ensure proper comparison
          const checkoutDate = new Date(checkout);
          const checkinDate = new Date(checkin);

          // Check if checkout is after or equal to checkin
          return checkoutDate >= checkinDate;
        }
      ),
    expectedCheckout: Yup.date()
      .required("Expected checkout date is required")
      .test(
        "expected-checkout-after-checkin",
        "Expected check-out date must be after check-in date",
        function (expectedCheckout, context) {
          const { checkin } = context.parent;
          // If checkin is not provided, skip this validation
          if (!checkin) return true;

          // Parse dates to ensure proper comparison
          const expectedCheckoutDate = new Date(expectedCheckout);
          const checkinDate = new Date(checkin);

          // Check if expected checkout is after or equal to checkin
          return expectedCheckoutDate >= checkinDate;
        }
      ),
    reservationDate: Yup.date().nullable(),
    roomCategory: Yup.string().required("Room category is required"),
    roomNo: Yup.string().required("Room number is required"),

    // Make these fields optional as per user's requirements
    packageType: Yup.string().nullable(),
    ac: Yup.mixed().nullable(),
    bedCategory: Yup.string().nullable(),

    // Customer Details Section with conditional validation
    isExistingGuest: Yup.boolean(),
    existingGuestId: Yup.string().when("isExistingGuest", {
      is: true,
      then: (schema) => schema.required("Please select an existing guest"),
      otherwise: (schema) => schema.notRequired(),
    }),

    // For new guest, these fields are required - skip validation in edit mode
    name: isEditMode
      ? Yup.string().notRequired()
      : Yup.string().when("isExistingGuest", {
          is: true,
          then: (schema) => schema.notRequired(),
          otherwise: (schema) => schema.required("Guest name is required"),
        }),
    // Keep guestName for backward compatibility
    guestName: Yup.string().notRequired(),
    mobileNo: isEditMode
      ? Yup.string().notRequired()
      : Yup.string().when("isExistingGuest", {
          is: true,
          then: (schema) => schema.notRequired(),
          otherwise: (schema) =>
            schema
              .required("Mobile number is required")
              .matches(/^\d{10,15}$/, "Invalid mobile number"),
        }),
    dob: isEditMode
      ? Yup.date().notRequired()
      : Yup.date().when("isExistingGuest", {
          is: true,
          then: (schema) => schema.notRequired(),
          otherwise: (schema) => schema.required("Date of birth is required"),
        }),
    gender: isEditMode
      ? Yup.string().notRequired()
      : Yup.string().when("isExistingGuest", {
          is: true,
          then: (schema) => schema.notRequired(),
          otherwise: (schema) => schema.required("Gender is required"),
        }),
    email: Yup.string().email("Invalid email").nullable(),
    country: isEditMode
      ? Yup.string().notRequired()
      : Yup.string().when("isExistingGuest", {
          is: true,
          then: (schema) => schema.notRequired(),
          otherwise: (schema) => schema.required("Country is required"),
        }),
    address: isEditMode
      ? Yup.string().notRequired()
      : Yup.string().when("isExistingGuest", {
          is: true,
          then: (schema) => schema.notRequired(),
          otherwise: (schema) => schema.required("Address is required"),
        }),

    // Guest count validation
    numberOfGuest: Yup.number()
      .transform((value) => (isNaN(value) ? undefined : value))
      .required("Total number of guests is required")
      .min(1, "At least one guest is required")
      .max(10, "Maximum 10 guests allowed")
      .test(
        "equals-sum",
        "Number of guests must equal sum of adults and children",
        function (value, context) {
          const { adults, children } = context.parent;
          const adultsNum = Number(adults) || 0;
          const childrenNum = Number(children) || 0;
          return value === adultsNum + childrenNum;
        }
      ),

    // Adults validation
    adults: Yup.number()
      .transform((value) => (isNaN(value) ? undefined : value))
      .required("Number of adults is required")
      .min(1, "At least one adult is required")
      .max(10, "Maximum 10 adults allowed")
      .test(
        "total-guests-limit",
        "Total guests (adults + children) cannot exceed 10",
        function (value, context) {
          const { children } = context.parent;
          const childrenNum = Number(children) || 0;
          return value + childrenNum <= 10;
        }
      ),

    // Children validation
    children: Yup.number()
      .transform((value) => (isNaN(value) ? undefined : value))
      .required("Number of children is required")
      .min(0, "Number of children cannot be negative")
      .max(9, "Maximum 9 children allowed")
      .test(
        "total-guests-limit",
        "Total guests (adults + children) cannot exceed 10",
        function (value, context) {
          const { adults } = context.parent;
          const adultsNum = Number(adults) || 0;
          return value + adultsNum <= 10;
        }
      ),

    // Identity Details Section - optional for all guests
    documents: Yup.array().notRequired().nullable().default([]),

    // Payment Details Section
    paymentMethod: Yup.string().nullable(), // Made optional as per requirement
    totalAmount: Yup.number()
      .required("Total amount is required")
      .min(0, "Amount must be non-negative"),
    paidAmount: Yup.number()
      .required("Paid amount is required")
      .min(0, "Paid amount must be non-negative")
      .test(
        "paid-amount-not-greater-than-total",
        "Paid amount cannot exceed total amount",
        function (paidAmount, context) {
          const { totalAmount } = context.parent;
          if (!paidAmount || !totalAmount) return true;
          return paidAmount <= totalAmount;
        }
      ),
  });
};

// Default export for backward compatibility
export const BookingValidationSchema = getBookingValidationSchema(false);
