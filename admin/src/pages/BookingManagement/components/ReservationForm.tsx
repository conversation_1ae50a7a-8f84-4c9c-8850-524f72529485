import { useEffect, useState } from "react";
import { FormikProvider, Form, useFormik, FieldArray } from "formik";
import { GlobalForm } from "../../../components/GlobalForm/GlobalFormComponent";
import { ActionButton } from "../../../components/ActionButton";
import {
  Reserve,
  CustomerDetails,
  IdentityDetails,
  PaymentDetails,
} from "./ReservationFormData";
import {
  useCreateBooking,
  useUpdateBooking,
  useGetBookingById,
} from "../../../server-action/API/BookingManagement/BookingManagement";
import { useParams, useNavigate, Link } from "react-router-dom";
import { useStore } from "@tanstack/react-store";
import { reservationStore, setBookingData } from "../../../store/reservation";
import { get } from "lodash";
import moment from "moment";
import { BookingStatus } from "../../../Interface/booking.interface";
import { getBookingValidationSchema } from "./ReservationValidationSchema";
import { toast } from "react-toastify";
import CustomerTabs from "./CustomerTabs";
import { IUser } from "../../../Interface/user.interface";
import { useGetUserById } from "../../../server-action/API/user";
import { calculateBookingPrice } from "../../../server-action/utils/calculateBookingPrice";
import { FrontendRoutes } from "../../../routes";
import { useGetRoomById } from "../../../server-action/API/HotelConfiguration/room";
import { Icon } from "@iconify/react/dist/iconify.js";
/* eslint-disable @typescript-eslint/no-explicit-any */

// Custom component to display booking conflict errors
interface BookingConflictErrorProps {
  message: string;
  conflictingBookingId?: string | null;
  onSelectDifferentRoom: () => void;
  onSelectDifferentDates: () => void;
}

const BookingConflictError: React.FC<BookingConflictErrorProps> = ({
  message,
  conflictingBookingId,
  onSelectDifferentRoom,
  onSelectDifferentDates,
}) => {
  return (
    <div className="p-4 mb-6 border-l-4 border-red-500 bg-red-50">
      <div className="flex items-start">
        <div className="flex-shrink-0 mt-0.5">
          <Icon icon="mdi:alert-circle" className="w-5 h-5 text-red" />
        </div>
        <div className="ml-3">
          <h3 className="text-sm font-medium text-red">
            Room Booking Conflict
          </h3>
          <div className="mt-2 text-sm text-red">
            <p className="text-red">{message}</p>
            {conflictingBookingId && (
              <p className="mt-1 text-red">
                Conflicting booking reference:{" "}
                <span className="font-medium text-red">
                  {conflictingBookingId}
                </span>
              </p>
            )}
          </div>
          <div className="mt-4 flex space-x-3">
            <button
              type="button"
              onClick={onSelectDifferentDates}
              className="px-3 py-1.5 text-sm font-medium text-red bg-red-100 hover:bg-red-200 rounded-md"
            >
              Select Different Dates
            </button>
            <button
              type="button"
              onClick={onSelectDifferentRoom}
              className="px-3 py-1.5 text-sm font-medium text-red bg-red-100 hover:bg-red-200 rounded-md"
            >
              Select Different Room
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

const getInitialValues = (sections: any) => {
  return sections.reduce((values: Record<string, any>, section: any) => {
    section.fields.forEach((field: any) => {
      if (field.type === "files") {
        values[field.field] = [];
      } else if (field.type === "file") {
        values[field.field] = "";
      } else if (field.field === "permanentAddress.country") {
        // Initialize nested permanentAddress object
        values.permanentAddress = { country: "" };
      } else if (field.field === "tempAddress.district") {
        // Initialize nested tempAddress object
        values.tempAddress = { district: "" };
      } else {
        values[field.field] = "";
      }
    });
    return values;
  }, {} as Record<string, any>);
};

const ReservationForm: React.FC = () => {
  const { id, bookingId: urlBookingId } = useParams();
  const navigate = useNavigate();
  const editData = useStore(reservationStore, (state) => state.bookingData);
  const [roomQuery, setRoomQuerry] = useState({
    selectedRoomCategory: "",
    ac: "",
    bedCategory: "",
  });
  const [selectedUserId, setSelectedUserId] = useState<string>("");
  const [selectedRoomId, setSelectedRoomId] = useState<string>("");
  const [membershipDiscount, setMembershipDiscount] = useState<any>(null);
  const [isCalculatingPrice, setIsCalculatingPrice] = useState(false);
  const [submissionError, setSubmissionError] = useState<string | null>(null);
  const [bookingConflictError, setBookingConflictError] = useState<{
    message: string;
    conflictingBookingId: string | null;
  } | null>(null);
  const [bookingId, setBookingId] = useState<string | null>(null);
  const [isLoadingBookingData, setIsLoadingBookingData] =
    useState<boolean>(false);

  // Fetch booking data if in edit mode and not available in store
  const { data: fetchedBookingData, isLoading: isLoadingBooking } =
    useGetBookingById(id === "edit" && !editData && bookingId ? bookingId : "");

  // Fetch user details when an existing guest is selected
  const { data: selectedUserData = {} as IUser } =
    useGetUserById(selectedUserId);

  // Fetch room details when a room is selected
  const { data: selectedRoomData } = useGetRoomById(selectedRoomId);

  const {
    mutateAsync: createBooking,
    isPending: isCreating,
    isSuccess: isCreateSuccess,
  } = useCreateBooking();

  const {
    mutateAsync: updateBooking,
    isPending: isUpdating,
    isSuccess: isUpdateSuccess,
  } = useUpdateBooking();

  const isPending = isCreating || isUpdating;
  const isSuccess = isCreateSuccess || isUpdateSuccess;

  const { ReservationDetails } = Reserve(
    roomQuery.selectedRoomCategory,
    roomQuery.bedCategory,
    roomQuery.ac
  );

  // Include Identity Details section for new bookings (now optional)
  const formSections = [
    { id: 1, title: "Reservation Details", fields: ReservationDetails },
    { id: 2, title: "Customer Details", fields: CustomerDetails },
    ...(id !== "edit"
      ? [
          {
            id: 3,
            title: "Identity Details (Optional)",
            fields: IdentityDetails,
          },
        ]
      : []),
    {
      id: id !== "edit" ? 4 : 3,
      title: "Payment Details",
      fields: PaymentDetails,
    },
  ];

  const formik = useFormik({
    initialValues:
      id === "add"
        ? {
            ...getInitialValues(formSections),
            isExistingGuest: false,
            existingGuestId: "",
            name: "", // Add name field explicitly
            guestName: "", // Keep guestName for backward compatibility
            expectedCheckout: "",
            reservationDate: "",
            totalAmount: 0,
            paidAmount: 0,
            adults: 1,
            children: 0,
            status: BookingStatus.CONFIRMED, // Default status for new bookings
            documents: [
              {
                IdentityType: "",
                IdNo: "",
                images: [],
              },
            ],
          }
        : {
            checkin: moment(get(editData, "checkIn")).format("YYYY-MM-DD"),
            checkout: get(editData, "checkOut")
              ? moment(get(editData, "checkOut")).format("YYYY-MM-DD")
              : "",
            expectedCheckout: get(editData, "expectedCheckOut")
              ? moment(get(editData, "expectedCheckOut")).format("YYYY-MM-DD")
              : "",
            reservationDate: get(editData, "reservationDate")
              ? moment(get(editData, "reservationDate")).format("YYYY-MM-DD")
              : "",
            isExistingGuest: false,
            existingGuestId: get(editData, "guest._id") || "",
            packageType: get(editData, "package.package") || "",
            ac: "",
            bedCategory: "",
            roomCategory: get(editData, "roomType._id") || "",
            roomNo: get(editData, "room._id"),
            name: get(editData, "guest.name"),
            guestName: get(editData, "guest.name"), // Add guestName for backward compatibility
            mobileNo: get(editData, "guest.phoneNumber"),
            dob: moment(get(editData, "guest.DOB")).format("YYYY-MM-DD"),
            gender: get(editData, "guest.gender"),
            email: get(editData, "guest.email"),
            permanentAddress: {
              country: get(editData, "guest.permanentAddress.country"),
            },
            tempAddress: {
              district: get(editData, "guest.tempAddress.district"),
            },
            numberOfGuest:
              get(editData, "pax.adults") + get(editData, "pax.children", 0),
            adults: get(editData, "pax.adults", 1),
            children: get(editData, "pax.children", 0),
            documents: get(editData, "guest.documents", []),
            paymentType: get(editData, "paymentType"),
            totalAmount: get(editData, "amount"),
            paidAmount: get(editData, "amountPaid", 0),
            paymentMethod: get(editData, "paymentMethod"),
            status: get(editData, "status", BookingStatus.CONFIRMED), // Get existing status or default to confirmed
          },
    enableReinitialize: true,
    validationSchema: getBookingValidationSchema(id === "edit"),
    onSubmit: async (values) => {
      try {
        setSubmissionError(null);
        setBookingConflictError(null);
        console.log("Form values:", values);
        // Use the existing formData variable

        const formatDate = (date: any) => {
          if (!date) return "";
          return moment(date).format("YYYY-MM-DD");
        };

        // Ensure permanentAddress and tempAddress have default values when using existing guest
        if (values.isExistingGuest && values.existingGuestId) {
          // If these fields are empty but we're using an existing guest, use default values
          if (!get(values, "permanentAddress.country")) {
            values.permanentAddress = { country: "Nepal" };
          }
          if (!get(values, "tempAddress.district")) {
            values.tempAddress = { district: "Not specified" };
          }
        }

        // Log the address values for debugging
        console.log("Address values before submission:", {
          permanentAddress: values.permanentAddress,
          tempAddress: values.tempAddress,
        });

        console.log("Form values:", values);

        // Check if a booking already exists for this room and date range
        try {
          // Define or import the checkBookingExists function
          const checkBookingExists = async (
            roomNo: string,
            checkin: string,
            checkout: string
          ) => {
            // Replace this with the actual implementation or API call
            console.log("Checking booking existence for:", {
              roomNo,
              checkin,
              checkout,
            });
            return false; // Example: return false to indicate no booking exists
          };

          const bookingExists = await checkBookingExists(
            values.roomNo,
            formatDate(values.checkin),
            formatDate(values.checkout)
          );

          if (bookingExists) {
            const errorMsg =
              "This room is already booked for the selected dates. Please choose different dates or a different room.";
            toast.error(errorMsg);
            setSubmissionError(errorMsg);
            return; // Stop form submission
          }
        } catch (checkError: any) {
          console.error("Error checking for existing bookings:", checkError);

          // Extract error message
          let errorMessage = "Failed to check room availability";

          if (checkError?.response?.data?.message) {
            errorMessage = checkError.response.data.message;
          } else if (checkError?.message) {
            errorMessage = checkError.message;
          }

          toast.error(`Availability check error: ${errorMessage}`);
          // Continue with submission even if check fails, but warn the user
        }

        const formData = new FormData();

        // The validation schema will handle these validations, but we'll double-check here
        if (!values.checkin && !values.reservationDate) {
          const errorMsg =
            "Either Check-in date or Reservation date is required.";
          setSubmissionError(errorMsg);
          toast.error(errorMsg);
          return;
        }

        if (!values.expectedCheckout) {
          const errorMsg = "Expected checkout date is required.";
          setSubmissionError(errorMsg);
          toast.error(errorMsg);
          return;
        }

        // Check if checkout date is after checkin date
        if (values.checkin && values.checkout) {
          const checkinDate = new Date(values.checkin);
          const checkoutDate = new Date(values.checkout);

          if (checkoutDate < checkinDate) {
            const errorMsg = "Check-out date must be after check-in date";
            setSubmissionError(errorMsg);
            formik.setFieldError("checkout", errorMsg);
            formik.setFieldTouched("checkout", true, false);
            return;
          }
        }

        // Check if expected checkout date is after checkin date
        if (values.checkin && values.expectedCheckout) {
          const checkinDate = new Date(values.checkin);
          const expectedCheckoutDate = new Date(values.expectedCheckout);

          if (expectedCheckoutDate < checkinDate) {
            const errorMsg =
              "Expected check-out date must be after check-in date";
            setSubmissionError(errorMsg);
            formik.setFieldError("expectedCheckout", errorMsg);
            formik.setFieldTouched("expectedCheckout", true, false);
            return;
          }
        }

        // Only append fields with values and avoid sending 'all'
        if (values.checkin) {
          formData.append("checkIn", formatDate(values.checkin));
        }

        if (values.checkout) {
          formData.append("checkOut", formatDate(values.checkout));
        }

        // Always append expectedCheckout as it's required
        formData.append(
          "expectedCheckOut",
          formatDate(values.expectedCheckout)
        );

        if (values.reservationDate) {
          formData.append(
            "reservationDate",
            formatDate(values.reservationDate)
          );
        }

        if (
          values.packageType &&
          values.packageType !== "all" &&
          values.packageType !== ""
        ) {
          formData.append("package[package]", values.packageType);
        }

        if (
          values.roomCategory &&
          values.roomCategory !== "all" &&
          values.roomCategory !== ""
        ) {
          formData.append("roomType", values.roomCategory);
        }

        // Ensure room field is always present as required by the backend API
        if (!values.roomNo) {
          console.warn(
            "Room number is missing! This is required by the backend API."
          );
          const errorMsg = "Room number is required. Please select a room.";
          setSubmissionError(errorMsg);
          toast.error(errorMsg);
          return;
        }
        formData.append("room", values.roomNo);

        // For editing existing bookings, only send the guest ID
        if (id === "edit" && editData?.guest?._id) {
          formData.append("guest", editData.guest._id);
          console.log(
            "Using existing guest ID for booking update:",
            editData.guest._id
          );
        } else if (values.isExistingGuest && values.existingGuestId) {
          // For new bookings with existing guest
          formData.append("guest", values.existingGuestId);
          console.log("Using existing guest with ID:", values.existingGuestId);
        } else {
          // For new bookings with new guest
          // Validate required guest fields
          if (!values.name) {
            const errorMsg = "Guest name is required.";
            setSubmissionError(errorMsg);
            formik.setFieldError("name", errorMsg);
            formik.setFieldTouched("name", true, false);
            return;
          }
          if (!values.mobileNo) {
            const errorMsg = "Mobile number is required.";
            setSubmissionError(errorMsg);
            formik.setFieldError("mobileNo", errorMsg);
            formik.setFieldTouched("mobileNo", true, false);
            return;
          }

          const guestData = {
            name: values.name || "",
            gender: values.gender || "",
            DOB: formatDate(values.dob),
            phoneNumber: values.mobileNo || "",
            email: values.email || "",
            tempAddress: {
              district: get(values, "tempAddress.district") || "Not specified",
              municipality: "",
              tole: "",
              country: "",
            },
            permanentAddress: {
              district: "",
              municipality: "",
              tole: "",
              country: get(values, "permanentAddress.country") || "Nepal",
            },
          };

          console.log("Creating new guest with data:", guestData);
          console.log("Form values for address fields:", {
            tempAddressDistrict: get(values, "tempAddress.district"),
            permanentAddressCountry: get(values, "permanentAddress.country"),
            rawTempAddress: values.tempAddress,
            rawPermanentAddress: values.permanentAddress,
          });

          // Convert the nested objects to JSON strings
          const tempAddressJson = JSON.stringify(guestData.tempAddress);
          const permanentAddressJson = JSON.stringify(
            guestData.permanentAddress
          );

          // Remove the nested objects from guestData
          const { tempAddress, permanentAddress, ...restGuestData } = guestData;

          // Append the flattened guest data
          Object.entries(restGuestData).forEach(([key, value]) => {
            formData.append(`guest[${key}]`, String(value));
          });

          // Append the address objects as JSON strings
          formData.append("guest[tempAddress]", tempAddressJson);
          formData.append("guest[permanentAddress]", permanentAddressJson);
        }

        // Use the values from the form for adults and children
        formData.append("pax[adults]", String(values.adults || 1)); // Default to 1 if not specified
        formData.append("pax[children]", String(values.children || 0)); // Default to 0 if not specified

        // Make payment method optional, only send if it has a value
        if (values.paymentMethod && values.paymentMethod !== "") {
          formData.append("paymentMethod", values.paymentMethod);
        }

        // Always send amount and amountPaid values, defaulting to 0 if not provided
        // This ensures the API always receives these fields
        formData.append("amount", String(values.totalAmount || 0));
        formData.append("amountPaid", String(values.paidAmount || 0));

        console.log("Sending payment details to API:", {
          totalAmount: values.totalAmount || 0,
          paidAmount: values.paidAmount || 0,
        });

        // Determine the status based on check-in and check-out dates
        let bookingStatus = values.status || BookingStatus.CONFIRMED;

        // If check-in date is entered, set status to checked-in
        if (values.checkin) {
          bookingStatus = BookingStatus.CHECKED_IN;

          // If check-out date is also entered, set status to checked-out
          if (values.checkout) {
            bookingStatus = BookingStatus.CHECKED_OUT;
          }
        }

        // Ensure the status is one of the valid enum values
        if (
          !Object.values(BookingStatus).includes(bookingStatus as BookingStatus)
        ) {
          console.warn(
            `Invalid booking status: ${bookingStatus}, defaulting to CONFIRMED`
          );
          bookingStatus = BookingStatus.CONFIRMED;
        }

        console.log(`Setting final booking status to: ${bookingStatus}`);
        formData.append("status", bookingStatus);

        // Ensure documents array is properly initialized
        if (!values.documents || !Array.isArray(values.documents)) {
          values.documents = [];
        }

        // Include documents for new bookings (not updates) if they exist
        // Documents are now optional for all guests
        if (id !== "edit" && values.documents && values.documents.length > 0) {
          // Filter out empty documents (where both IdentityType and IdNo are empty)
          const validDocuments = values.documents.filter(
            (doc: any) => doc.IdentityType || doc.IdNo
          );

          if (validDocuments.length > 0) {
            // Create a documents array structure with only valid documents
            const documentsData = validDocuments.map((doc: any) => ({
              IdentityType: doc.IdentityType || "",
              IdNo: doc.IdNo?.toString() || "",
              // We'll handle images separately
            }));

            // Append the documents data as JSON to guest[documents]
            formData.append("guest[documents]", JSON.stringify(documentsData));

            // Now handle the file uploads separately
            validDocuments.forEach((doc: any, docIndex: number) => {
              formData.append(
                `guest[documents][${docIndex}][IdentityType]`,
                doc.IdentityType || ""
              );
              formData.append(
                `guest[documents][${docIndex}][IdNo]`,
                doc.IdNo?.toString() || ""
              );

              // Handle images for this document
              if (doc.images && doc.images.length > 0) {
                doc.images.forEach((image: File) => {
                  console.log(
                    `Adding image for document ${docIndex}:`,
                    image.name
                  );
                  formData.append(
                    `guest[documents][${docIndex}][images]`,
                    image
                  );
                });
              }
            });
            console.log("Finished adding all guest documents to FormData");
          } else {
            console.log("No valid documents to add");
          }
        }

        // Log the final FormData for debugging
        console.log("Final FormData entries:");
        for (const pair of (formData as any).entries()) {
          console.log(pair[0], pair[1]);
        }

        // Log the final status for debugging
        console.log(
          `Final booking status being sent to API: ${formData.get("status")}`
        );

        // Double-check that status is set correctly based on check-in and check-out dates
        const statusValue = formData.get("status")?.toString() || "";

        if (
          values.checkin &&
          !statusValue.includes("checked-in") &&
          !statusValue.includes("checked-out")
        ) {
          console.warn(
            "Warning: Check-in date is set but status is not checked-in or checked-out"
          );
        }
        if (
          values.checkin &&
          values.checkout &&
          !statusValue.includes("checked-out")
        ) {
          console.warn(
            "Warning: Both check-in and check-out dates are set but status is not checked-out"
          );
        }

        let response;
        try {
          if (id === "edit" && editData?._id) {
            // Update existing booking
            console.log("Updating existing booking with ID:", editData._id);

            // Log all form data entries for debugging
            console.log("Form data entries for update:");
            for (const pair of formData.entries()) {
              console.log(`${pair[0]}: ${pair[1]}`);
            }

            response = await updateBooking({
              _id: editData._id,
              bookingData: formData,
            });
            console.log("Booking update response:", response);
            // Toast message is handled in the mutation's onSuccess callback
          } else {
            // Create new booking
            console.log("Creating new booking...");
            response = await createBooking(formData);
            // Toast message is handled in the mutation's onSuccess callback
          }
          console.log("Booking operation completed successfully:", response);
        } catch (error: any) {
          console.error("Error during booking operation:", error);

          // Check if it's a booking conflict error
          if (error?.isBookingConflict) {
            console.log("Booking conflict detected:", error);

            // Set the booking conflict error state
            setBookingConflictError({
              message:
                error.message ||
                "Room is already booked for the selected dates.",
              conflictingBookingId: error.conflictingBookingId || null,
            });

            // Highlight the problematic fields
            formik.setFieldError(
              "roomNo",
              "This room is already booked for these dates"
            );
            formik.setFieldError(
              "checkin",
              "Date conflict with existing booking"
            );
            formik.setFieldError(
              "checkout",
              "Date conflict with existing booking"
            );

            // Mark fields as touched to show errors
            formik.setFieldTouched("roomNo", true);
            formik.setFieldTouched("checkin", true);
            formik.setFieldTouched("checkout", true);

            return;
          }

          // Extract detailed error message from the API response if available
          let errorMessage = "An error occurred while processing your booking.";

          if (error?.response?.data?.message) {
            errorMessage = error.response.data.message;
          } else if (error?.message) {
            errorMessage = error.message;
          }

          console.error("Error message:", errorMessage);
          setSubmissionError(errorMessage);

          // Display specific error message based on operation type
          if (id === "edit") {
            toast.error(`Failed to update booking: ${errorMessage}`);
          } else {
            toast.error(`Failed to create booking: ${errorMessage}`);
          }
          return;
        }

        console.log("Booking response:", response);

        // Navigate to booking list after successful submission
        setTimeout(() => {
          navigate(FrontendRoutes.BOOKINGMANAGEMENT);
        }, 1500);
      } catch (error: any) {
        console.error("Error submitting reservation:", error);

        // Extract detailed error message from the API response if available
        let errorMessage = "Failed to process booking. Please try again.";

        if (error?.response?.data?.message) {
          errorMessage = error.response.data.message;
        } else if (error?.message) {
          errorMessage = error.message;
        }

        setSubmissionError(errorMessage);

        // Display specific error message based on operation type
        if (id === "edit") {
          toast.error(`Failed to update booking: ${errorMessage}`);
        } else {
          toast.error(`Failed to create booking: ${errorMessage}`);
        }
      }
    },
  });

  // Function to update booking status based on check-in and check-out dates
  const updateBookingStatus = (
    checkinDate: string | null,
    checkoutDate: string | null
  ) => {
    let newStatus = BookingStatus.CONFIRMED;

    // If check-in date is entered, set status to checked-in
    if (checkinDate) {
      newStatus = BookingStatus.CHECKED_IN;

      // If check-out date is also entered, set status to checked-out
      if (checkoutDate) {
        newStatus = BookingStatus.CHECKED_OUT;
      }
    }

    console.log(
      `Updating booking status to: ${newStatus} based on check-in: ${checkinDate}, check-out: ${checkoutDate}`
    );

    // Set the status field value
    formik.setFieldValue("status", newStatus);
  };

  // Function to calculate price with membership discount
  const calculatePrice = async () => {
    try {
      // Check if we have the minimum required fields to calculate price
      if (!formik.values.roomNo) {
        return;
      }

      // Need either checkin or expectedCheckout for calculation
      if (!formik.values.checkin && !formik.values.expectedCheckout) {
        return;
      }

      setIsCalculatingPrice(true);

      // Log all form values for debugging
      console.log("All form values:", formik.values);

      console.log("Calculating price with the following data:", {
        roomId: formik.values.roomNo,
        checkInDate: formik.values.checkin || formik.values.reservationDate, // Use checkin or reservationDate
        checkOutDate: formik.values.checkout || formik.values.expectedCheckout, // Use checkout or expectedCheckout
        userId: formik.values.existingGuestId || undefined,
      });

      // Update form with calculated price
      const priceData = await calculateBookingPrice({
        roomId: formik.values.roomNo,
        checkInDate: formik.values.checkin || formik.values.reservationDate,
        checkOutDate: formik.values.checkout || formik.values.expectedCheckout,
        userId: formik.values.existingGuestId || undefined,
      });
      // Set the total amount
      formik.setFieldValue("totalAmount", priceData.totalPrice);

      // If paidAmount is not set or is 0, initialize it to 0
      if (!formik.values.paidAmount) {
        formik.setFieldValue("paidAmount", 0);
      }

      // Ensure paidAmount doesn't exceed the new total amount
      if (formik.values.paidAmount > priceData.totalPrice) {
        formik.setFieldValue("paidAmount", priceData.totalPrice);
        toast.info("Paid amount adjusted to match the total amount");
      }

      // Store membership discount info if available
      if (priceData.membershipDiscount) {
        setMembershipDiscount(priceData.membershipDiscount);
        toast.success(
          `${priceData.membershipDiscount.name} membership discount of ${priceData.membershipDiscount.discountPercentage}% applied!`
        );
      } else {
        setMembershipDiscount(null);
      }

      setIsCalculatingPrice(false);
    } catch (error: any) {
      console.error("Error calculating price:", error);

      // Extract error message
      let errorMessage = "Failed to calculate price";

      if (error?.response?.data?.message) {
        errorMessage = error.response.data.message;
      } else if (error?.message) {
        errorMessage = error.message;
      }

      // Show error toast with specific message
      toast.error(`Price calculation error: ${errorMessage}`);

      setIsCalculatingPrice(false);

      // Set a default price as fallback
      formik.setFieldValue("totalAmount", 0);
    }
  };

  // Improved synchronization between guest count fields
  useEffect(() => {
    // Get current values with proper type conversion and defaults
    const adults = Math.max(0, Number(formik.values.adults) || 0);
    const children = Math.max(0, Number(formik.values.children) || 0);
    const currentTotal = Number(formik.values.numberOfGuest) || 0;
    const calculatedTotal = adults + children;

    // Prevent infinite loops by checking if values actually changed
    const needsUpdate = calculatedTotal !== currentTotal;

    if (needsUpdate) {
      // Update the total guest count
      formik.setFieldValue("numberOfGuest", calculatedTotal, false);

      // Validate the fields to show any errors immediately
      formik.validateField("adults");
      formik.validateField("children");
      formik.validateField("numberOfGuest");

      // Log for debugging
      console.log(
        `Guest count updated: ${adults} adults + ${children} children = ${calculatedTotal} total`
      );
    }
  }, [formik.values.adults, formik.values.children]);

  // Handle edge cases and constraints
  useEffect(() => {
    // Ensure adults is at least 1
    if (Number(formik.values.adults) < 1 && formik.values.adults !== "") {
      formik.setFieldValue("adults", 1, false);
    }

    // Ensure children is non-negative
    if (Number(formik.values.children) < 0 && formik.values.children !== "") {
      formik.setFieldValue("children", 0, false);
    }

    // Ensure total doesn't exceed 10
    const adults = Number(formik.values.adults) || 0;
    const children = Number(formik.values.children) || 0;
    if (adults + children > 10) {
      // If total exceeds 10, adjust children first
      const newChildren = Math.max(0, 10 - adults);
      if (newChildren !== children) {
        formik.setFieldValue("children", newChildren, false);
        // Total will be updated by the other useEffect
      }
    }
  }, [formik.values.adults, formik.values.children]);

  // Watch for changes in room selection or dates to recalculate price and check availability
  useEffect(() => {
    // Calculate price if we have room and either checkin/reservationDate and checkout/expectedCheckout
    if (
      formik.values.roomNo &&
      (formik.values.checkin || formik.values.reservationDate) &&
      (formik.values.checkout || formik.values.expectedCheckout)
    ) {
      calculatePrice();
    }

    // Check date validation when dates change
    if (formik.values.checkin && formik.values.checkout) {
      const checkinDate = new Date(formik.values.checkin);
      const checkoutDate = new Date(formik.values.checkout);

      if (checkoutDate < checkinDate) {
        // Set field error to display below the checkout field
        formik.setFieldError(
          "checkout",
          "Check-out date must be after check-in date"
        );
        // Mark the field as touched to ensure error is displayed
        formik.setFieldTouched("checkout", true, false);
      } else {
        // Clear the error if dates are valid
        formik.setFieldError("checkout", undefined);
      }
    }

    if (formik.values.checkin && formik.values.expectedCheckout) {
      const checkinDate = new Date(formik.values.checkin);
      const expectedCheckoutDate = new Date(formik.values.expectedCheckout);

      if (expectedCheckoutDate < checkinDate) {
        // Set field error to display below the expectedCheckout field
        formik.setFieldError(
          "expectedCheckout",
          "Expected check-out date must be after check-in date"
        );
        // Mark the field as touched to ensure error is displayed
        formik.setFieldTouched("expectedCheckout", true, false);
      } else {
        // Clear the error if dates are valid
        formik.setFieldError("expectedCheckout", undefined);
      }
    }

    // Update booking status when dates change
    updateBookingStatus(
      formik.values.checkin || null,
      formik.values.checkout || null
    );
  }, [
    formik.values.roomNo,
    formik.values.checkin,
    formik.values.checkout,
    formik.values.reservationDate,
    formik.values.expectedCheckout,
    formik.values.existingGuestId,
  ]);

  // Handle existing guest selection
  useEffect(() => {
    if (
      formik.values.existingGuest &&
      formik.values.existingGuest !== selectedUserId
    ) {
      setSelectedUserId(formik.values.existingGuest);
    }
  }, [formik.values.existingGuest]);

  // Auto-fill guest details when an existing guest is selected
  useEffect(() => {
    // Don't auto-fill if we're editing an existing booking
    if (id === "edit") return;

    if (selectedUserData && Object.keys(selectedUserData).length > 0) {
      console.log("Selected user data:", selectedUserData);
      const userData = selectedUserData as IUser;

      // Set guest name
      formik.setFieldValue("guestName", userData.name || "");

      // Set phone number
      formik.setFieldValue("mobileNo", userData.phoneNumber || "");

      // Set date of birth with proper formatting
      formik.setFieldValue(
        "dob",
        userData.DOB ? moment(userData.DOB).format("YYYY-MM-DD") : ""
      );

      // Set gender
      formik.setFieldValue("gender", userData.gender || "");

      // Set email
      formik.setFieldValue("email", userData.email || "");

      // Set country - check both direct property and nested address
      const country =
        userData.country || userData.permanentAddress?.country || "";
      formik.setFieldValue("country", country);

      // Set address - check for address property or use district from tempAddress
      const address = userData.address || userData.tempAddress?.district || "";
      formik.setFieldValue("address", address);

      console.log("Guest data populated in form");
    }
  }, [selectedUserData, id]);

  // Update room query when filter values change
  useEffect(() => {
    // Only update if the values have actually changed
    if (
      formik.values.ac !== roomQuery.ac ||
      formik.values.bedCategory !== roomQuery.bedCategory ||
      formik.values.roomCategory !== roomQuery.selectedRoomCategory
    ) {
      console.log("Updating room filters:", {
        ac: formik.values.ac,
        bedCategory: formik.values.bedCategory,
        selectedRoomCategory: formik.values.roomCategory,
      });

      setRoomQuerry({
        ac: formik.values.ac,
        bedCategory: formik.values.bedCategory,
        selectedRoomCategory: formik.values.roomCategory,
      });
    }
  }, [formik.values.ac, formik.values.bedCategory, formik.values.roomCategory]);

  // Handle room selection and auto-fill room details
  useEffect(() => {
    if (formik.values.roomNo && formik.values.roomNo !== selectedRoomId) {
      setSelectedRoomId(formik.values.roomNo);
    }
  }, [formik.values.roomNo]);

  // Auto-fill room type, AC type, and bed type when room is selected
  useEffect(() => {
    if (selectedRoomData) {
      console.log("Selected room data:", selectedRoomData);

      // Set room category/type
      if (selectedRoomData.roomType?._id) {
        formik.setFieldValue("roomCategory", selectedRoomData.roomType._id);
      }

      // Set AC type
      if (selectedRoomData.features?.acType) {
        formik.setFieldValue("ac", selectedRoomData.features.acType);
      }

      // Set bed type if available
      if (
        selectedRoomData.beds?.types &&
        selectedRoomData.beds.types.length > 0
      ) {
        formik.setFieldValue("bedCategory", selectedRoomData.beds.types[0]._id);
      }

      console.log("Room details auto-filled");
    }
  }, [selectedRoomData]);

  // Update booking status when check-in or check-out dates change
  useEffect(() => {
    // Always call updateBookingStatus to ensure status is properly set
    // even if both values are null/empty
    updateBookingStatus(
      formik.values.checkin || null,
      formik.values.checkout || null
    );

    // Log the current status for debugging
    console.log(
      `Current booking status after date change: ${formik.values.status}`
    );
  }, [formik.values.checkin, formik.values.checkout]);

  // Initialize booking status based on initial check-in and check-out dates
  useEffect(() => {
    // Only run once when component mounts
    updateBookingStatus(
      formik.values.checkin || null,
      formik.values.checkout || null
    );
    console.log(`Initial booking status set to: ${formik.values.status}`);
  }, []); // Empty dependency array means this runs once on mount

  // Set booking ID from URL parameter
  useEffect(() => {
    // If we're in edit mode but don't have booking data in the store
    if (id === "edit" && !editData) {
      if (urlBookingId) {
        setBookingId(urlBookingId);
        console.log("Found booking ID in URL params:", urlBookingId);
      } else {
        console.warn("Edit mode activated but no booking data or ID found");
        // Optionally redirect back to booking list if no data is available
        // navigate(FrontendRoutes.BOOKINGMANAGEMENT);
      }
    }
  }, [id, editData, urlBookingId]);

  // Use fetched booking data if available
  useEffect(() => {
    // Update loading state based on the API loading status
    setIsLoadingBookingData(isLoadingBooking);

    if (id === "edit" && !editData && fetchedBookingData?.data) {
      console.log("Using fetched booking data:", fetchedBookingData.data);
      // Store the fetched data in the reservation store
      setBookingData(fetchedBookingData.data);
    }
  }, [id, editData, fetchedBookingData, isLoadingBooking]);

  useEffect(() => {
    if (isSuccess) {
      formik.resetForm();
      // Navigate to booking list after successful submission
      setTimeout(() => {
        navigate(FrontendRoutes.BOOKINGMANAGEMENT);
      }, 1500);
    }
  }, [isSuccess, navigate]);

  const { handleSubmit, getFieldProps } = formik;

  return (
    <div className="w-full p-6 mx-auto bg-white rounded-lg shadow-md">
      {/* Show loading indicator when fetching booking data */}
      {isLoadingBookingData && (
        <div className="flex items-center justify-center p-4 mb-4 bg-blue-50 border border-blue-200 rounded-md">
          <div className="w-6 h-6 mr-3 border-t-2 border-b-2 border-blue-500 rounded-full animate-spin"></div>
          <p className="text-blue-700">Loading booking data...</p>
        </div>
      )}

      <FormikProvider value={formik}>
        <Form onSubmit={handleSubmit}>
          {/* Display booking conflict error if present */}
          {bookingConflictError && (
            <BookingConflictError
              message={bookingConflictError.message}
              conflictingBookingId={bookingConflictError.conflictingBookingId}
              onSelectDifferentRoom={() => {
                // Clear the room selection
                formik.setFieldValue("roomNo", "");
                // Clear the error
                setBookingConflictError(null);
                // Focus on the room field
                const roomField = document.querySelector('[name="roomNo"]');
                if (roomField) {
                  (roomField as HTMLElement).focus();
                }
              }}
              onSelectDifferentDates={() => {
                // Clear the date fields
                formik.setFieldValue("checkin", "");
                formik.setFieldValue("checkout", "");
                // Clear the error
                setBookingConflictError(null);
                // Focus on the checkin field
                const checkinField = document.querySelector('[name="checkin"]');
                if (checkinField) {
                  (checkinField as HTMLElement).focus();
                }
              }}
            />
          )}

          <div className="max-h-[calc(100vh-200px)] overflow-y-auto p-2">
            {formSections.map(({ title, fields, id: sectionId }) => (
              <div key={title} className="p-4 mt-5 mb-6 border rounded-md">
                <h2 className="pb-2 mb-3 text-lg font-semibold border-b">
                  {title}
                </h2>

                {sectionId === 2 ? (
                  <div>
                    {/* Make guest fields read-only when editing */}
                    {id === "add" ? (
                      <>
                        <CustomerTabs
                          isExistingGuest={formik.values.isExistingGuest}
                          onTabChange={(isExisting: boolean) => {
                            formik.setFieldValue("isExistingGuest", isExisting);
                            if (!isExisting) {
                              formik.setFieldValue("existingGuestId", "");
                              const currentValues = { ...formik.values };
                              const fieldsToReset = [
                                "name",
                                "mobileNo",
                                "dob",
                                "gender",
                                "email",
                                "permanentAddress.country",
                                "tempAddress.district",
                              ];
                              if (currentValues.existingGuestId) {
                                fieldsToReset.forEach((field) => {
                                  formik.setFieldValue(field, "");
                                });
                              }
                            }
                            formik.setErrors({});
                          }}
                          onSelectExistingGuest={(guest: IUser) => {
                            if (guest) {
                              formik.setValues({
                                ...formik.values,
                                existingGuestId: guest._id,
                                name: guest.name || "",
                                guestName: guest.name || "", // Set both name and guestName for compatibility
                                mobileNo: guest.phoneNumber || "",
                                dob: guest.DOB
                                  ? moment(guest.DOB).format("YYYY-MM-DD")
                                  : "",
                                gender: guest.gender || "",
                                email: guest.email || "",
                                "permanentAddress.country":
                                  guest.permanentAddress?.country || "",
                                "tempAddress.district":
                                  guest.tempAddress?.district || "",
                              });
                              formik.setFieldTouched("existingGuestId", true);
                              formik.setFieldTouched("name", true);
                            }
                          }}
                          formik={formik}
                          isEditMode={id !== "add"}
                        />

                        {formik.values.isExistingGuest ? (
                          <div>
                            <div className="mt-4 p-4 border rounded-md bg-blue-50">
                              <h3 className="font-medium text-blue-800 mb-3">
                                Guest Count Information
                              </h3>
                              <p className="text-sm text-blue-700 mb-3">
                                Please specify the number of adults and children
                                staying in this room. The total number of guests
                                will be calculated automatically.
                              </p>
                              <div className="grid grid-cols-3 gap-4 mt-2">
                                {CustomerDetails.filter(
                                  (field) =>
                                    field.field === "numberOfGuest" ||
                                    field.field === "adults" ||
                                    field.field === "children"
                                ).map((field, index) => (
                                  <div key={index} className="col-span-1">
                                    <GlobalForm
                                      formDatails={[field] as any}
                                      getFieldProps={getFieldProps}
                                      formik={formik}
                                    />
                                  </div>
                                ))}
                              </div>
                            </div>
                            {formik.values.existingGuestId && (
                              <div className="mt-4 p-4 border rounded bg-gray-50">
                                <h3 className="font-medium text-gray-800 mb-2">
                                  Selected Existing Customer
                                </h3>
                                <div className="grid grid-cols-2 gap-4">
                                  <div>
                                    <p className="text-sm text-gray-600">
                                      Name:{" "}
                                      <span className="font-medium">
                                        {formik.values.name}
                                      </span>
                                    </p>
                                    <p className="text-sm text-gray-600">
                                      Phone:{" "}
                                      <span className="font-medium">
                                        {formik.values.mobileNo}
                                      </span>
                                    </p>
                                    <p className="text-sm text-gray-600">
                                      Email:{" "}
                                      <span className="font-medium">
                                        {formik.values.email || "-"}
                                      </span>
                                    </p>
                                  </div>
                                  <div>
                                    <p className="text-sm text-gray-600">
                                      Gender:{" "}
                                      <span className="font-medium">
                                        {formik.values.gender}
                                      </span>
                                    </p>
                                    <p className="text-sm text-gray-600">
                                      Country:{" "}
                                      <span className="font-medium">
                                        {get(
                                          formik.values,
                                          "permanentAddress.country",
                                          ""
                                        )}
                                      </span>
                                    </p>
                                  </div>
                                </div>
                              </div>
                            )}
                          </div>
                        ) : (
                          <div className="grid grid-cols-3 gap-4 mt-4">
                            <GlobalForm
                              formDatails={fields as any}
                              getFieldProps={getFieldProps}
                              formik={formik}
                            />
                          </div>
                        )}
                      </>
                    ) : (
                      <>
                        <CustomerTabs
                          isExistingGuest={true}
                          onTabChange={() => {}}
                          onSelectExistingGuest={() => {}}
                          formik={formik}
                          isEditMode={true}
                        />
                        <div className="mt-4 p-4 border rounded-md bg-blue-50">
                          <h3 className="font-medium text-blue-800 mb-3">
                            Guest Count Information
                          </h3>
                          <p className="text-sm text-blue-700 mb-3">
                            Please specify the number of adults and children
                            staying in this room. The total number of guests
                            will be calculated automatically.
                          </p>
                          <div className="grid grid-cols-3 gap-4 mt-2">
                            {CustomerDetails.filter(
                              (field) =>
                                field.field === "numberOfGuest" ||
                                field.field === "adults" ||
                                field.field === "children"
                            ).map((field, index) => (
                              <div key={index} className="col-span-1">
                                <GlobalForm
                                  formDatails={
                                    [
                                      {
                                        ...field,
                                        disabled:
                                          field.field === "numberOfGuest", // Only make numberOfGuest read-only
                                      },
                                    ] as any
                                  }
                                  getFieldProps={getFieldProps}
                                  formik={formik}
                                />
                              </div>
                            ))}
                          </div>
                        </div>
                      </>
                    )}
                  </div>
                ) : sectionId === 3 && id !== "edit" ? (
                  <>
                    <div className="mb-4 p-3 bg-blue-50 border-l-4 border-blue-500">
                      <p className="text-sm font-medium text-black">
                        Optional: Identity Details
                      </p>
                      <p className="text-xs mt-1 text-black">
                        You can add identity documents if available, but they
                        are not required.
                      </p>
                    </div>

                    <FieldArray name="documents">
                      {({ push, remove }) => (
                        <div className="space-y-4">
                          {formik.values.documents &&
                          formik.values.documents.length > 0 ? (
                            formik.values.documents.map(
                              (_: {}, index: number) => {
                                return (
                                  <div
                                    key={index}
                                    className="p-4 border rounded-lg"
                                  >
                                    <div className="grid grid-cols-3 gap-4 mb-4">
                                      <div className="col-span-1">
                                        <label className="block mb-1 text-sm font-medium">
                                          Identity Type
                                        </label>
                                        <select
                                          {...getFieldProps(
                                            `documents[${index}].IdentityType`
                                          )}
                                          className="w-full p-2 border rounded"
                                        >
                                          <option value="">
                                            Select ID Type
                                          </option>
                                          <option value="passport">
                                            Passport
                                          </option>
                                          <option value="drivingLicense">
                                            Driving License
                                          </option>
                                          <option value="nationalId">
                                            National ID
                                          </option>
                                          <option value="other">Other</option>
                                        </select>
                                      </div>

                                      <div className="col-span-1">
                                        <label className="block mb-1 text-sm font-medium">
                                          ID Number
                                        </label>
                                        <input
                                          type="text"
                                          {...getFieldProps(
                                            `documents[${index}].IdNo`
                                          )}
                                          className="w-full p-2 border rounded"
                                          placeholder="Enter ID number"
                                        />
                                      </div>

                                      <div className="col-span-1">
                                        <label className="block mb-1 text-sm font-medium">
                                          Upload Images
                                        </label>
                                        <input
                                          type="file"
                                          multiple
                                          onChange={(event) => {
                                            const files =
                                              event.currentTarget.files;
                                            if (files) {
                                              const newImages = [
                                                ...(formik.values.documents[
                                                  index
                                                ]?.images || []),
                                              ];
                                              for (
                                                let i = 0;
                                                i < files.length;
                                                i++
                                              ) {
                                                newImages.push(files[i]);
                                              }
                                              formik.setFieldValue(
                                                `documents[${index}].images`,
                                                newImages
                                              );
                                            }
                                          }}
                                          className="w-full p-2 border rounded"
                                        />
                                      </div>
                                    </div>

                                    {formik.values.documents[index]?.images
                                      ?.length > 0 && (
                                      <div className="mt-2">
                                        <p className="mb-2 text-sm font-medium">
                                          Uploaded Files:
                                        </p>
                                        <div className="flex flex-wrap gap-2">
                                          {formik.values.documents[
                                            index
                                          ].images.map(
                                            (image: any, imgIndex: number) => (
                                              <div
                                                key={imgIndex}
                                                className="relative"
                                              >
                                                <div className="p-2 text-xs bg-gray-100 rounded">
                                                  {image.name ||
                                                    `File ${imgIndex + 1}`}
                                                  <button
                                                    type="button"
                                                    className="ml-2 text-red-500"
                                                    onClick={() => {
                                                      const newImages = [
                                                        ...formik.values
                                                          .documents[index]
                                                          .images,
                                                      ];
                                                      newImages.splice(
                                                        imgIndex,
                                                        1
                                                      );
                                                      formik.setFieldValue(
                                                        `documents[${index}].images`,
                                                        newImages
                                                      );
                                                    }}
                                                  >
                                                    ×
                                                  </button>
                                                </div>
                                              </div>
                                            )
                                          )}
                                        </div>
                                      </div>
                                    )}

                                    <button
                                      type="button"
                                      onClick={() => remove(index)}
                                      className="px-3 py-1 mt-2 text-sm text-red-600 border border-red-600 rounded"
                                    >
                                      Remove Document
                                    </button>
                                  </div>
                                );
                              }
                            )
                          ) : (
                            <div className="p-4 text-center bg-gray-50 rounded-lg">
                              <p className="text-gray-600">
                                No identity documents added yet.
                              </p>
                            </div>
                          )}

                          <button
                            type="button"
                            onClick={() =>
                              push({ IdentityType: "", IdNo: "", images: [] })
                            }
                            className="px-4 py-2 mt-4 bg-gray-200 rounded"
                          >
                            + Add Identity Document
                          </button>
                        </div>
                      )}
                    </FieldArray>
                  </>
                ) : (
                  <div className="grid grid-cols-3 gap-4">
                    <GlobalForm
                      formDatails={fields as any}
                      getFieldProps={getFieldProps}
                      formik={formik}
                    />
                  </div>
                )}
              </div>
            ))}
          </div>

          {submissionError && (
            <div className="p-3 mb-4 text-sm text-red bg-red-100 rounded-md font-semibold">
              <strong className="text-red">Error:</strong>{" "}
              <span className="text-red">{submissionError}</span>
            </div>
          )}

          <div className="flex justify-between mt-4">
            <button
              type="button"
              onClick={() => navigate(FrontendRoutes.BOOKINGMANAGEMENT)}
              className="px-4 py-2 text-gray-700 bg-gray-200 rounded hover:bg-gray-300"
            >
              Cancel
            </button>

            <ActionButton
              loading={isPending}
              isSubmitting={formik.isSubmitting}
              submitText={id === "edit" ? "Update Booking" : "Create Booking"}
              onCancel={() => navigate(FrontendRoutes.BOOKINGMANAGEMENT)}
              onSubmit={async () => {
                try {
                  // For edit mode, we don't need to validate guest fields
                  if (id === "edit") {
                    // Skip guest field validation for edit mode
                    const errors = await formik.validateForm();
                    const filteredErrors = Object.keys(errors).reduce(
                      (acc: Record<string, any>, key) => {
                        // Only keep errors that are not related to guest fields
                        if (
                          ![
                            "guestName",
                            "mobileNo",
                            "dob",
                            "gender",
                            "email",
                            "country",
                            "address",
                            "documents",
                          ].includes(key)
                        ) {
                          acc[key] = errors[key as string];
                        }
                        return acc;
                      },
                      {} as Record<string, any>
                    );

                    // Set the filtered errors
                    formik.setErrors(filteredErrors);
                  }

                  // Ensure permanentAddress and tempAddress have default values when using existing guest
                  if (
                    formik.values.isExistingGuest &&
                    formik.values.existingGuestId
                  ) {
                    // If these fields are empty but we're using an existing guest, set default values
                    if (!get(formik.values, "permanentAddress.country")) {
                      formik.setFieldValue("permanentAddress.country", "Nepal");
                    }
                    if (!get(formik.values, "tempAddress.district")) {
                      formik.setFieldValue(
                        "tempAddress.district",
                        "Not specified"
                      );
                    }
                  }

                  // Check for validation errors before submitting
                  await formik.validateForm().then((errors) => {
                    // For edit mode, filter out guest-related errors
                    if (id === "edit") {
                      const guestFields = [
                        "guestName",
                        "mobileNo",
                        "dob",
                        "gender",
                        "email",
                        "country",
                        "address",
                        "documents", // Documents are now optional for all guests
                      ];
                      Object.keys(errors).forEach((key) => {
                        if (guestFields.includes(key)) {
                          delete errors[key];
                        }
                      });
                    }
                    console.log(
                      "Form values before submission:",
                      formik.values
                    );

                    if (Object.keys(errors).length === 0) {
                      console.log("Form is valid, submitting...");
                      formik.submitForm();
                    } else {
                      console.error("Form validation failed:", errors);

                      // Get the first error message to display in toast
                      const firstErrorKey = Object.keys(errors)[0];
                      const firstErrorValue = errors[firstErrorKey];

                      // Create a more specific error message
                      let errorMessage =
                        "Please fix the form errors before submitting";

                      // Handle date validation errors by setting field errors
                      // instead of showing toast messages
                      if (errors.checkout) {
                        const checkoutError =
                          typeof errors.checkout === "string"
                            ? errors.checkout
                            : "Check-out date must be after check-in date";

                        // Set field error to display below the checkout field
                        formik.setFieldError("checkout", checkoutError);
                        formik.setFieldTouched("checkout", true, false);

                        // Don't show toast for date validation errors
                        errorMessage =
                          "Please fix the form errors before submitting";
                      } else if (errors.expectedCheckout) {
                        const expectedCheckoutError =
                          typeof errors.expectedCheckout === "string"
                            ? errors.expectedCheckout
                            : "Expected check-out date must be after check-in date";

                        // Set field error to display below the expectedCheckout field
                        formik.setFieldError(
                          "expectedCheckout",
                          expectedCheckoutError
                        );
                        formik.setFieldTouched("expectedCheckout", true, false);

                        // Don't show toast for date validation errors
                        errorMessage =
                          "Please fix the form errors before submitting";
                      }
                      // For other errors, create a generic message
                      else if (typeof firstErrorValue === "string") {
                        errorMessage = `Please fix the form errors before submitting`;
                      } else if (firstErrorKey) {
                        errorMessage = `Please fix the form errors before submitting`;
                      }

                      // Only show toast for non-date validation errors
                      if (!errors.checkout && !errors.expectedCheckout) {
                        toast.error(errorMessage);
                      }

                      // Touch all fields to show validation errors
                      Object.keys(formik.values).forEach((field) => {
                        formik.setFieldTouched(field, true);
                      });

                      // Specifically touch the document fields including images
                      if (
                        formik.values.documents &&
                        Array.isArray(formik.values.documents)
                      ) {
                        formik.values.documents.forEach(
                          (_: any, index: number) => {
                            formik.setFieldTouched(
                              `documents[${index}].IdentityType`,
                              true
                            );
                            formik.setFieldTouched(
                              `documents[${index}].IdNo`,
                              true
                            );
                            formik.setFieldTouched(
                              `documents[${index}].images`,
                              true
                            );
                          }
                        );
                      }
                    }
                  });
                } catch (error) {
                  console.error("Error during form submission:", error);
                  toast.error("An error occurred while submitting the form");
                }
              }}
            />
          </div>
        </Form>
      </FormikProvider>
    </div>
  );
};

export default ReservationForm;
