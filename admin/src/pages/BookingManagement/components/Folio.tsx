import get from "lodash/get";
import moment from "moment";
import { useState } from "react";
import { IBooking } from "../../../Interface/booking.interface";
import RoomBill from "./RoomBill";
import OrdersSection from "./OrdersSection";
import print from "../../../assets/Svg/Print.svg";
import { PopupModal } from "../../../components";

interface propTypes {
  booking: IBooking;
}

interface mapperTypes {
  [key: string]: {
    _id: string;
    label: string;
    value:
      | string
      | number
      | {
          district: string;
          municipality: string;
          tole: string;
          country: string;
        };
  }[];
}

const Folio = ({ booking }: propTypes) => {
  const [showPrintModal, setShowPrintModal] = useState(false);

  const totalGuests =
    get(booking, "pax.adults", 0) +
    get(booking, "pax.children", 0) +
    (get(booking, "pax.infants", 0) ?? 0);

  const guestData = [
    {
      _id: "1",
      label: "Guest Full Name",
      value: get(booking, "guest.name", ""),
    },
    { _id: "2", label: "Email", value: get(booking, "guest.email", "") },
    {
      _id: "3",
      label: "Country",
      value: get(booking, "guest.permanentAddress.country", "Nepal"),
    },
    {
      _id: "4",
      label: "Phone Number",
      value: get(booking, "guest.phoneNumber", ""),
    },
    {
      _id: "8",
      label: "Address",
      value:
        typeof get(booking, "guest.permanentAddress", "") === "object"
          ? `${get(booking, "guest.permanentAddress.tole", "")}, ${get(
              booking,
              "guest.permanentAddress.municipality",
              ""
            )}, ${get(booking, "guest.permanentAddress.district", "")}, ${get(
              booking,
              "guest.permanentAddress.country",
              ""
            )}`
          : get(booking, "guest.permanentAddress", "") ||
            get(booking, "guest.address", ""),
    },
    {
      _id: "9",
      label: "Purpose",
      value: get(booking, "guest.purpose", "Holiday"),
    },
  ];

  const roomData = [
    { _id: "1", label: "Number of Guest", value: totalGuests },
    { _id: "2", label: "Adults", value: get(booking, "pax.adults", 0) },
    { _id: "3", label: "Kids", value: get(booking, "pax.children", 0) },
    {
      _id: "4",
      label: "Booking Number",
      value: `B-${get(booking, "_id", "").slice(-5)}`,
    },
    {
      _id: "5",
      label: "Check In",
      value: moment(get(booking, "checkIn", moment())).format("MMM-DD-YYYY"),
    },
    {
      _id: "6",
      label: "Room Type",
      value: get(booking, "room.roomType.name", ""),
    },
    {
      _id: "7",
      label: "Nights",
      value: moment(get(booking, "checkOut")).diff(
        moment(get(booking, "checkIn")),
        "days"
      ),
    },
    {
      _id: "8",
      label: "Check Out",
      value: moment(get(booking, "checkOut", moment())).format("MMM-DD-YYYY"),
    },
    {
      _id: "9",
      label: "Facilities",
      value: Object.entries({
        Balcony: get(booking, "room.features.balcony", false),
        "A/C": get(booking, "room.features.acType", false),
        Smoking: get(booking, "room.features.smoking", false),
        "Beautiful View": get(booking, "room.features.viewType", false),
      })
        .filter(([_, value]) => value === true)
        .map(([key]) => key)
        .join(", "),
    },
    {
      _id: "10",
      label: "OTA Platform",
      value: get(booking, "otaPlatform", "Booking.com"),
    },
    {
      _id: "11",
      label: "Package Type",
      value: get(booking, "package.package", "Standard, Basic"),
    },
  ];

  const handlePrint = () => {
    setShowPrintModal(true);
  };

  return (
    <div className="flex flex-col w-full h-full gap-4">
      {showPrintModal && (
        <PopupModal onClose={() => setShowPrintModal(false)}>
          <PrintFolio
            booking={booking}
            onClose={() => setShowPrintModal(false)}
          />
        </PopupModal>
      )}

      <div className="flex justify-end mb-2">
        <button
          onClick={handlePrint}
          className="bg-[#EBFEF4] px-4 py-2 text-sm rounded-lg text-black font-semibold flex justify-center items-center gap-2"
        >
          <img src={print} alt="Print" />
          Print Folio
        </button>
      </div>

      <div className="flex flex-col gap-4 md:flex-row">
        <div className="w-full md:w-1/2 h-fit">
          <Guest guestData={guestData} />
        </div>
        <div className="w-full md:w-1/2 h-fit">
          <Rooms roomData={roomData} />
        </div>
      </div>
      <section className="w-full h-full gap-6">
        <div>
          <RoomBill booking={booking} />
        </div>
        <div className="mt-6">
          <OrdersSection booking={booking} />
        </div>
      </section>
    </div>
  );
};

export default Folio;

export const Guest = ({ guestData }: mapperTypes) => {
  return (
    <div className="w-full flex flex-col p-5 gap-3 bg-white min-h-[25rem] h-full shadow-sm rounded-md">
      <h1 className="font-semibold text-black">Guest Information</h1>
      <div className="grid lg:grid-cols-3 md:grid-cols-2 gap-y-8">
        {guestData?.map((item) => (
          <div key={item._id} className="flex p-4 flex-col">
            <h3 className="font-medium text-black text-medium">{item.label}</h3>
            <p className="text-sm font-light text-black">
              {typeof item.value === "object" && item.value !== null
                ? `${(item.value as any).tole || ""}, ${
                    (item.value as any).municipality || ""
                  }, ${(item.value as any).district || ""}, ${
                    (item.value as any).country || ""
                  }`
                : String(item.value)}
            </p>
          </div>
        ))}
      </div>
    </div>
  );
};

export const Rooms = ({ roomData }: mapperTypes) => {
  return (
    <div className="w-full flex flex-col p-5 gap-3 bg-white min-h-[25rem] h-full shadow-sm rounded-md">
      <h1 className="font-bold text-black">Room Information</h1>
      <div className="grid lg:grid-cols-3 md:grid-cols-2 gap-y-8">
        {roomData.map((item) => (
          <div key={item._id} className="flex flex-col">
            <h3 className="font-medium text-black text-medium">{item.label}</h3>
            <p className="text-sm font-light text-black">
              {typeof item.value === "object" && item.value !== null
                ? String(JSON.stringify(item.value))
                : String(item.value)}
            </p>
          </div>
        ))}
      </div>
    </div>
  );
};

// Print Folio Component
const PrintFolio = ({
  booking,
  onClose,
}: {
  booking: IBooking;
  onClose: () => void;
}) => {
  return (
    <div className="w-full max-w-4xl bg-white p-8">
      <div className="flex justify-between items-center mb-6 border-b pb-4">
        <h1 className="text-2xl font-bold">Hotel Folio</h1>
        <button
          onClick={onClose}
          className="bg-gray-200 px-4 py-2 rounded-md text-sm"
        >
          Close
        </button>
      </div>

      <div className="mb-6">
        <h2 className="text-xl font-semibold mb-2">Booking Information</h2>
        <div className="grid grid-cols-2 gap-4">
          <div>
            <p>
              <span className="font-medium">Booking ID:</span> B-
              {get(booking, "_id", "").slice(-5)}
            </p>
            <p>
              <span className="font-medium">Guest Name:</span>{" "}
              {get(booking, "guest.name", "")}
            </p>
            <p>
              <span className="font-medium">Check-in:</span>{" "}
              {moment(get(booking, "checkIn")).format("MMM DD, YYYY")}
            </p>
            <p>
              <span className="font-medium">Check-out:</span>{" "}
              {moment(get(booking, "checkOut")).format("MMM DD, YYYY")}
            </p>
          </div>
          <div>
            <p>
              <span className="font-medium">Room:</span>{" "}
              {get(booking, "room.roomNo", "")} /{" "}
              {get(booking, "room.roomType.name", "")}
            </p>
            <p>
              <span className="font-medium">Nights:</span>{" "}
              {moment(get(booking, "checkOut")).diff(
                moment(get(booking, "checkIn")),
                "days"
              )}
            </p>
            <p>
              <span className="font-medium">Guests:</span>{" "}
              {get(booking, "pax.adults", 0)} Adults,{" "}
              {get(booking, "pax.children", 0)} Children
            </p>
            <p>
              <span className="font-medium">Address:</span>{" "}
              {typeof get(booking, "guest.permanentAddress", "") === "object"
                ? `${get(booking, "guest.permanentAddress.tole", "")}, ${get(
                    booking,
                    "guest.permanentAddress.municipality",
                    ""
                  )}, ${get(
                    booking,
                    "guest.permanentAddress.district",
                    ""
                  )}, ${get(booking, "guest.permanentAddress.country", "")}`
                : String(
                    get(booking, "guest.permanentAddress", "") ||
                      get(booking, "guest.address", "")
                  )}
            </p>
            <p>
              <span className="font-medium">Status:</span>{" "}
              {get(booking, "status", "")}
            </p>
          </div>
        </div>
      </div>

      <div className="mb-6">
        <h2 className="text-xl font-semibold mb-2">Charges</h2>
        <table className="w-full border-collapse">
          <thead>
            <tr className="bg-gray-100">
              <th className="border p-2 text-left">Description</th>
              <th className="border p-2 text-right">Amount</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td className="border p-2">
                Room Charge (
                {moment(get(booking, "checkOut")).diff(
                  moment(get(booking, "checkIn")),
                  "days"
                )}{" "}
                nights)
              </td>
              <td className="border p-2 text-right">
                Rs. {get(booking, "amount", 0)}
              </td>
            </tr>
            <tr>
              <td className="border p-2 font-semibold">Total</td>
              <td className="border p-2 text-right font-semibold">
                Rs. {get(booking, "amount", 0)}
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <div className="mb-6">
        <h2 className="text-xl font-semibold mb-2">Payments</h2>
        <table className="w-full border-collapse">
          <thead>
            <tr className="bg-gray-100">
              <th className="border p-2 text-left">Date</th>
              <th className="border p-2 text-left">Method</th>
              <th className="border p-2 text-right">Amount</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td className="border p-2">
                {moment(get(booking, "createdAt")).format("MMM DD, YYYY")}
              </td>
              <td className="border p-2">
                {get(booking, "paymentMethod", "Cash")}
              </td>
              <td className="border p-2 text-right">
                Rs. {get(booking, "amountPaid", 0)}
              </td>
            </tr>
            <tr>
              <td className="border p-2 font-semibold" colSpan={2}>
                Balance Due
              </td>
              <td className="border p-2 text-right font-semibold">
                Rs. {get(booking, "amount", 0) - get(booking, "amountPaid", 0)}
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <div className="text-center mt-8 pt-4 border-t">
        <p className="text-sm text-gray-600">Thank you for staying with us!</p>
        <p className="text-sm text-gray-600">
          For any inquiries, please contact <NAME_EMAIL>
        </p>
      </div>
    </div>
  );
};
