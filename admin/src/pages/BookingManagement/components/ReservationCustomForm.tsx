import React, { useState } from "react";
import { Field, FieldProps, useFormik, FormikProps } from "formik";
import * as Yup from "yup";
/*eslint-disable*/
interface Option {
  value: string;
  label: string;
}
import { galleryIcon } from "../../../components/svgExports";
import { customerDetails, reservationDetails } from "./reservationObj";
import { CustomDatePicker } from "../../../components/DatePickerFormFields";
import { CustomSelect } from "../../../components/SelectFormFields";
// import { FiChevronLeft, FiChevronRight, FiUpload, FiPlus } from 'react-icons/fi';

const FormCard = ({
  title,
  children,
  title1,
  reset,
  formik,
}: {
  title: string;
  reset?: boolean;
  formik?: any;
  title1?: string;
  children: React.ReactNode;
}) => {
  return (
    <div className="p-6 pb-6 mx-auto mb-6 bg-white rounded-lg shadow-md">
      {title1 && (
        <div className="mb-6">
          <h1 className="text-lg font-bold">{title1}</h1>
        </div>
      )}
      <div className="flex items-center justify-between mb-3">
        <h2 className="text-xl font-semibold">{title}</h2>
        {reset && (
          <button
            type="button"
            className="p-2 text-sm border border-gray-200 rounded-md"
            onClick={() => formik.resetForm()}
          >
            Reset
          </button>
        )}
      </div>

      <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
        {children}
      </div>
    </div>
  );
};

export interface FormFieldProps {
  label?: string;
  required?: boolean;
  name: string;
  rows?: number;
  type?: string;
  placeholder?: string;
  options?: Option[];
  formik: FormikProps<any>;
  value?: string | any;
  min?: number | string;
  max?: number | string;
  readonly?: boolean;
  disabled?: boolean;
  onChange?: (e: React.ChangeEvent<any>) => void;
  multiSelect?: boolean;
  [key: string]: any;
}

export const FormField: React.FC<FormFieldProps> = ({
  label,
  required = false,
  name,
  rows,
  type = "text",
  placeholder,
  options = [],
  formik,
  value,
  min,
  max,
  readonly,
  disabled,
  onChange,
  multiSelect = false,
  ...rest
}) => {
  // Combined onChange handler
  const handleChange = (e: React.ChangeEvent<any>) => {
    formik.handleChange(e);
    if (onChange) {
      onChange(e);
    }
  };

  // Return the CustomDatePicker for date type
  if (type === "date") {
    return (
      <CustomDatePicker
        label={label || name}
        required={required}
        name={name}
        formik={formik}
        value={value ?? formik.values[name]}
        disabled={disabled}
        onChange={onChange}
        {...rest}
      />
    );
  }

  return (
    <div className="flex flex-col w-full">
      <label htmlFor={name} className="mb-1 text-sm">
        {label} {required && <p className="text-red">*</p>}
      </label>

      {type === "dropdown" && multiSelect ? (
        <Field name={name}>
          {({ field: formikField, form }: FieldProps) => (
            <div>
              <div
                className={`flex flex-wrap gap-2 p-2 border ${
                  formik.touched[name] && formik.errors[name]
                    ? "border-red-500"
                    : "border-gray-300"
                } rounded-lg bg-gray-50 min-h-12`}
              >
                {(formikField.value || []).map(
                  (value: string, index: number) => {
                    const option = options.find((opt) => opt.value === value);
                    return (
                      <div
                        key={index}
                        className="flex items-center gap-1 px-2 py-1 bg-blue-100 text-blue-800 rounded-md"
                      >
                        {option?.label || value}
                        <button
                          type="button"
                          className="text-blue-600 hover:text-blue-800"
                          onClick={() => {
                            const newValues = (formikField.value || []).filter(
                              (_: any, i: number) => i !== index
                            );
                            form.setFieldValue(name, newValues);
                          }}
                          disabled={disabled}
                        >
                          ×
                        </button>
                      </div>
                    );
                  }
                )}
                <select
                  className="flex-1 min-w-[150px] bg-transparent border-none focus:outline-none text-sm"
                  value=""
                  onChange={(e: React.ChangeEvent<HTMLSelectElement>) => {
                    const selectedValue = e.target.value;
                    if (
                      selectedValue &&
                      !formikField.value.includes(selectedValue)
                    ) {
                      const newValues = [
                        ...(formikField.value || []),
                        selectedValue,
                      ];
                      form.setFieldValue(name, newValues);
                      e.target.value = ""; // Reset select to allow multiple selections
                    }
                  }}
                  disabled={disabled}
                >
                  <option value="" disabled>
                    {placeholder || "Select an option"}
                  </option>
                  {options.map((option) => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
              </div>
              <select
                multiple
                className="hidden"
                name={name}
                value={formikField.value || []}
                onChange={() => {}}
              >
                {(formikField.value || []).map(
                  (value: string, index: number) => (
                    <option key={index} value={value}>
                      {options.find((opt) => opt.value === value)?.label ||
                        value}
                    </option>
                  )
                )}
              </select>
            </div>
          )}
        </Field>
      ) : type === "dropdown" ? (
        <CustomSelect
          label={label || name}
          required={required}
          name={name}
          placeholder={placeholder}
          options={options}
          formik={formik}
          value={value ?? formik.values[name]}
          disabled={disabled}
          onChange={onChange}
          multiSelect={multiSelect}
          {...rest}
        />
      ) : type === "file" ? (
        <div className="flex items-center space-x-2">
          <label
            htmlFor={name}
            className="flex items-center p-2 text-white bg-indigo-500 cursor-pointer rounded-l-md"
          >
            Choose File
          </label>
          <input
            id={name}
            name={name}
            type="file"
            onChange={(e) => {
              formik.setFieldValue(name, e.currentTarget.files?.[0]);
              if (onChange) {
                onChange(e);
              }
            }}
            onBlur={formik.handleBlur}
            className="hidden"
            {...rest}
          />
          <span className="text-sm text-gray-500">
            {formik.values[name] ? formik.values[name].name : "No File Chosen"}
          </span>
        </div>
      ) : type === "textarea" ? (
        <textarea
          id={name}
          name={name}
          placeholder={placeholder}
          value={value ?? formik.values[name] ?? ""}
          onChange={handleChange}
          onBlur={formik.handleBlur}
          className={`p-2 border rounded-md h-24 resize-none  focus:outline-none focus:border-gray-400 ${
            formik.touched[name] && formik.errors[name]
              ? "border-red-500"
              : "border-gray-300  "
          }`}
          rows={rows}
          {...rest}
        />
      ) : (
        <input
          id={name}
          name={name}
          type={type}
          placeholder={placeholder}
          value={value ?? formik.values[name] ?? ""}
          min={min}
          max={max}
          readOnly={readonly}
          disabled={disabled}
          onChange={handleChange}
          onBlur={formik.handleBlur}
          className={`p-2 border rounded-md focus:outline-none focus:border-gray-400  ${
            formik.touched[name] && formik.errors[name]
              ? "border-red-500"
              : "border-gray-300"
          }`}
          {...rest}
        />
      )}

      {formik.touched[name] && formik.errors[name] && (
        <div className="mt-1 text-xs text-red">{`${formik.errors[name]}`}</div>
      )}
    </div>
  );
};

const ReservationCustomForm = () => {
  const [identityFields, setIdentityFields] = useState([{ id: 1 }]);

  const reservationSchema = Yup.object({
    // Reservation Details
    checkIn: Yup.string().required("Check-in date is required"),
    checkOut: Yup.string().required("Check-out date is required"),
    roomCategory: Yup.string(),
    acNon: Yup.string(),
    bedCategory: Yup.string(),
    roomNo: Yup.string(),

    // Customer Details
    guestName: Yup.string().required("Guest name is required"),
    gender: Yup.string().required("Gender is required"),
    dateOfBirth: Yup.string(),
    mobileNo: Yup.string().required("Mobile number is required"),
    email: Yup.string()
      .email("Invalid email format")
      .required("Email is required"),
    country: Yup.string().required("Country is required"),
    address: Yup.string(),
    nationality: Yup.string().required("Nationality is required"),
    purposeOfVisit: Yup.string(),

    // Identity fields are handled dynamically
  });

  const formik = useFormik({
    initialValues: {
      // Reservation Details
      checkIn: "",
      checkOut: "",
      packageType: "",
      roomCategory: "",
      acNon: "",
      bedCategory: "",
      roomNo: "",

      // Customer Details
      guestName: "",
      gender: "",
      dateOfBirth: "",
      mobileNo: "",
      email: "",
      country: "",
      address: "",
      nationality: "",
      purposeOfVisit: "",

      // Identity Details (initial field)
      identityType1: "",
      idNo1: "",
      photo1: null,
    },
    validationSchema: reservationSchema,
    onSubmit: (values) => {
      console.log("Form Values:", values);
      // Handle form submission
    },
  });

  const addIdentityField = () => {
    const newId = identityFields.length + 1;
    setIdentityFields([...identityFields, { id: newId }]);

    // Add dynamic fields to formik
    formik.setFieldValue(`identityType${newId}`, "");
    formik.setFieldValue(`idNo${newId}`, "");
    formik.setFieldValue(`photo${newId}`, null);
  };

  const handleFileChange = (
    event: React.ChangeEvent<HTMLInputElement>,
    fieldId: number
  ) => {
    if (event.currentTarget.files && event.currentTarget.files.length > 0) {
      formik.setFieldValue(`photo${fieldId}`, event.currentTarget.files[0]);
    }
  };

  const genderOptions = [
    { value: "male", label: "Male" },
    { value: "female", label: "Female" },
    { value: "other", label: "Other" },
  ];

  const roomCategoryOptions = [
    { value: "standard", label: "Standard" },
    { value: "deluxe", label: "Deluxe" },
    { value: "suite", label: "Suite" },
  ];

  const packageTypeOptions = [
    { value: "standard", label: "Standard" },
    { value: "deluxe", label: "Deluxe" },
    { value: "suite", label: "Suite" },
  ];

  const bedCategoryOptions = [
    { value: "single", label: "Single" },
    { value: "double", label: "Double" },
    { value: "twin", label: "Twin" },
  ];

  const acOptions = [
    { value: "ac", label: "AC" },
    { value: "non-ac", label: "Non-AC" },
  ];

  const countryOptions = [
    { value: "us", label: "United States" },
    { value: "uk", label: "United Kingdom" },
    { value: "ca", label: "Canada" },
    { value: "au", label: "Australia" },
  ];

  const identityTypeOptions = [
    { value: "passport", label: "Passport" },
    { value: "drivingLicense", label: "Driving License" },
    { value: "idCard", label: "ID Card" },
  ];
  const reservationObj = reservationDetails({
    bedCategory: bedCategoryOptions,
    roomCategory: roomCategoryOptions,
    packageType: packageTypeOptions,
    roomNo: roomCategoryOptions,
    acNon: acOptions,
  });
  const clientObj = customerDetails({
    gender: genderOptions,
    country: countryOptions,
  });

  return (
    <div className="w-full">
      <form onSubmit={formik.handleSubmit}>
        <FormCard
          title1="Booking Reference No #202"
          title="Reservations Details"
          formik={formik}
        >
          {reservationObj.map(({ label, options, type, placeholder, name }) => (
            <FormField
              key={name}
              label={label}
              options={options}
              type={type}
              placeholder={placeholder}
              name={name}
              formik={formik}
            />
          ))}
        </FormCard>

        {/* Customer Details Card */}
        <FormCard title="Customer Details" reset={true} formik={formik}>
          {clientObj?.map(({ label, type, options, name, placeholder }) => (
            <FormField
              key={name}
              label={label}
              options={options}
              type={type}
              placeholder={placeholder}
              name={name}
              formik={formik}
            />
          ))}
        </FormCard>

        <FormCard title="Identity Details" formik={formik}>
          {identityFields.map((field, index) => (
            <div
              key={field.id}
              className="grid grid-cols-1 col-span-3 gap-4 md:grid-cols-3"
            >
              <FormField
                label="Identity Type"
                name={`identityType${field.id}`}
                type="select"
                placeholder="Select Identity Type"
                options={identityTypeOptions}
                formik={formik}
              />

              <FormField
                label="ID No."
                name={`idNo${field.id}`}
                placeholder="Id No."
                type="text"
                formik={formik}
              />

              <div className="flex flex-col">
                <label htmlFor={`photo${field.id}`} className="mb-1 text-sm">
                  Upload Photo
                </label>
                <div className="flex items-center w-full space-x-2 border-2 rounded-md">
                  <label
                    htmlFor={`photo${field.id}`}
                    className="flex items-center gap-1 p-2 text-white cursor-pointer bg-light-secondary rounded-l-md"
                  >
                    {galleryIcon({ color: "white", size: "18" })}
                    Choose File
                  </label>
                  <input
                    id={`photo${field.id}`}
                    name={`photo${field.id}`}
                    type="file"
                    onChange={(e) => handleFileChange(e, field.id)}
                    className="hidden"
                  />
                  <span className="text-sm text-gray-500">
                    {formik.values[
                      `photo${field.id}` as keyof typeof formik.values
                    ]
                      ? (
                          formik?.values[
                            `photo${field.id}` as keyof typeof formik.values
                          ] as any
                        ).name
                      : "No File Chosen"}
                  </span>
                </div>
              </div>

              {index === identityFields.length - 1 && (
                <div className="col-span-3 mt-4">
                  <button
                    type="button"
                    onClick={addIdentityField}
                    className="flex gap-2 py-1 px-2 items-center font-medium border-[#6047E4] border-2 rounded-lg"
                  >
                    Add More
                    {/* {PlusIcon({className="black size-4"})} */}
                  </button>
                </div>
              )}
            </div>
          ))}
        </FormCard>

        <div className="flex items-center justify-end mt-6">
          <button
            type="submit"
            className="px-4 py-2 text-white rounded-md bg-light-secondary hover:bg-dark-secondary"
          >
            Save Details
          </button>
        </div>
      </form>
    </div>
  );
};

export default ReservationCustomForm;
