import { Form, FormikProvider, useFormik } from "formik";
import { useEffect } from "react";
import { useParams } from "react-router-dom";
import { useMakeBookingPayment, useGetBookingBalance } from "../../../server-action/API/BookingManagement/BookingManagement";
import HeadingPopup from "../../GuestManagement/components/HeadingPopup";
import { FormField } from "./ReservationCustomForm";
import * as Yup from "yup";
import { get } from "lodash";

// Validation schema
const PaymentSchema = Yup.object().shape({
  amount: Yup.number()
    .required("Amount is required")
    .positive("Amount must be positive")
    .max(999999, "Amount too large"),
  paymentMethod: Yup.string()
    .required("Payment method is required")
    .oneOf(["cash", "online"], "Invalid payment method"),
  notes: Yup.string().optional(),
});

const EditPayment = ({ onclose }: { onclose: () => void }) => {
  const { mutate: makePayment, isPending, isSuccess } = useMakeBookingPayment();
  const { id } = useParams();

  // Get booking balance information
  const { data: balanceData, isLoading: isLoadingBalance } = useGetBookingBalance(id || "");

  useEffect(() => {
    if (isSuccess) {
      onclose();
    }
  }, [isSuccess, onclose]);

  const formik = useFormik({
    initialValues: {
      amount: "",
      paymentMethod: "",
      notes: "",
    },
    validationSchema: PaymentSchema,
    onSubmit: (values) => {
      if (!id) return;

      makePayment({
        bookingId: id,
        amount: Number(values.amount),
        paymentMethod: values.paymentMethod as "cash" | "online",
        notes: values.notes || undefined,
      });
    },
  });

  const paymentFields = [
    {
      type: "dropdown",
      name: "paymentMethod",
      label: "Payment Method",
      options: [
        { label: "Cash", value: "cash" },
        { label: "Online", value: "online" },
      ],
      placeholder: "Select Payment Method",
    },
    {
      type: "number",
      name: "amount",
      label: "Amount",
      placeholder: "Enter Amount",
    },
    {
      type: "textarea",
      name: "notes",
      label: "Notes (Optional)",
      placeholder: "Enter payment notes",
    },
  ];
  return (
    <HeadingPopup
      heading="Add Payment"
      onClose={onclose}
      className="w-full max-w-screen-md"
    >
      <FormikProvider value={formik}>
        <Form onSubmit={formik.handleSubmit} className="p-4">
          {/* Booking Balance Information */}
          {balanceData?.data && !isLoadingBalance && (
            <div className="mb-6 p-4 bg-gray-50 rounded-lg">
              <h3 className="text-sm font-semibold text-gray-700 mb-2">Booking Summary</h3>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="text-gray-600">Total Amount:</span>
                  <span className="ml-2 font-semibold">Rs {get(balanceData, "data.totalAmount", 0).toLocaleString()}</span>
                </div>
                <div>
                  <span className="text-gray-600">Paid Amount:</span>
                  <span className="ml-2 font-semibold text-green-600">Rs {get(balanceData, "data.amountPaid", 0).toLocaleString()}</span>
                </div>
                <div>
                  <span className="text-gray-600">Remaining Balance:</span>
                  <span className="ml-2 font-semibold text-red-600">Rs {get(balanceData, "data.remainingBalance", 0).toLocaleString()}</span>
                </div>
                <div>
                  <span className="text-gray-600">Payment Status:</span>
                  <span className={`ml-2 font-semibold ${
                    get(balanceData, "data.paymentStatus") === "fully-paid"
                      ? "text-green-600"
                      : get(balanceData, "data.paymentStatus") === "partially-paid"
                      ? "text-yellow-600"
                      : "text-red-600"
                  }`}>
                    {get(balanceData, "data.paymentStatus", "").replace("-", " ").toUpperCase()}
                  </span>
                </div>
              </div>
            </div>
          )}

          <div className="grid grid-cols-1 gap-4">
            {paymentFields.map((field) => (
              <FormField key={field.name} formik={formik} {...field} />
            ))}
          </div>

          <div className="flex items-end justify-end w-full h-full mt-6">
            <div className="flex gap-2">
              <button
                type="button"
                className="flex items-center justify-center gap-2 px-4 py-2 text-sm font-semibold text-black border-2 rounded-lg hover:bg-gray-50"
                onClick={onclose}
              >
                Cancel
              </button>

              <button
                disabled={isPending || isLoadingBalance}
                type="submit"
                className="bg-[#6047E4] px-6 py-2 text-sm rounded-lg text-white font-semibold flex justify-center items-center gap-2 hover:bg-[#5038d3] disabled:opacity-50"
              >
                {isPending ? "Processing..." : "Process Payment"}
              </button>
            </div>
          </div>
        </Form>
      </FormikProvider>
    </HeadingPopup>
  );
};

export default EditPayment;
