import MasterTable from "../../../layouts/Table/MasterTable";
import { TableAction } from "../../../layouts/Table/TableAction";
import { PopupModal } from "../../../components";
import { useState } from "react";
import cross from "../../../assets/Svg/Cross.svg";
import { PrintLayout } from "./PrintLayout";
import EditPayment from "./EditPayment";
import { IBooking } from "../../../Interface/booking.interface";
import { get } from "lodash";
import moment from "moment";
import { useGetBookingTransactions } from "../../../server-action/API/Guest/transaction";
import { useGetBookingPaymentHistory, useGetBookingBalance } from "../../../server-action/API/BookingManagement/BookingManagement";
import { Icon } from "@iconify/react/dist/iconify.js";

interface propTypes {
  booking: IBooking;
}
const Payment = ({ booking }: propTypes) => {
  const [showPopup, setShowPopup] = useState(false);
  const [showPrint, setshowPrint] = useState(false);
  const [showEdit, setshowEdit] = useState(false);
  const [selectedTransaction, setSelectedTransaction] = useState<any>(null);

  // Fetch booking balance and payment history
  const { data: balanceData, isLoading: isLoadingBalance } = useGetBookingBalance(booking?._id);
  const { data: paymentHistory, isLoading: isLoadingPaymentHistory } = useGetBookingPaymentHistory(booking?._id);

  // Fetch transactions for this booking (for transaction history)
  const { data: transactions, isLoading: isLoadingTransactions } =
    useGetBookingTransactions(booking?._id);

  // Early return if booking is not available
  if (!booking) {
    return (
      <div className="space-y-6">
        <div className="bg-white p-4 rounded-md shadow-sm">
          <h2 className="text-lg font-semibold mb-4">Payment Summary</h2>
          <div className="text-center py-8 text-gray-500">
            Loading booking information...
          </div>
        </div>
      </div>
    );
  }

  // Summary table for booking payment status
  const summaryTableData = {
    columns: [
      { title: "Payment Method", key: "paymentMethod" },
      { title: "Total Amount", key: "amount" },
      { title: "Paid Amount", key: "paidAmount" },
      { title: "Remaining", key: "remaining" },
      { title: "Payment Status", key: "paymentStatus" },
      { title: "Action", key: "action" },
    ],
    rows: [
      {
        amount: `Rs ${get(balanceData, "data.totalAmount", get(booking, "amount", 0)).toLocaleString()}`,
        paymentMethod: get(balanceData, "data.paymentMethod", get(booking, "paymentMethod", "")).toUpperCase(),
        paidAmount: `Rs ${get(balanceData, "data.amountPaid", get(booking, "amountPaid", 0)).toLocaleString()}`,
        remaining: `Rs ${get(balanceData, "data.remainingBalance",
          get(booking, "amount", 0) - get(booking, "amountPaid", 0)
        ).toLocaleString()}`,
        paymentStatus: (
          <span className={`px-2 py-1 rounded-full text-xs font-semibold ${
            get(balanceData, "data.paymentStatus", get(booking, "paymentStatus")) === "fully-paid"
              ? "bg-green-100 text-green-800"
              : get(balanceData, "data.paymentStatus", get(booking, "paymentStatus")) === "partially-paid"
              ? "bg-yellow-100 text-yellow-800"
              : "bg-red-100 text-red-800"
          }`}>
            {get(balanceData, "data.paymentStatus", get(booking, "paymentStatus", "")).replace("-", " ").toUpperCase()}
          </span>
        ),
        action: (
          <TableAction
            onEdit={() => setshowEdit(true)}
            onPrint={() => setshowPrint(true)}
          />
        ),
      },
    ],
  };

  // Transactions table data
  const transactionsTableData = {
    columns: [
      { title: "Date", key: "date" },
      { title: "Reference No", key: "referenceNo" },
      { title: "Description", key: "description" },
      { title: "Debit", key: "debit" },
      { title: "Credit", key: "credit" },
      { title: "Balance", key: "balance" },
      { title: "Action", key: "action" },
    ],
    rows: transactions
      ? transactions.map((transaction: any) => ({
          date: moment(transaction.date).format("MMM DD, YYYY"),
          referenceNo: transaction.referenceNo,
          description: transaction.description,
          debit:
            transaction.debitAmount > 0
              ? `Rs ${transaction.debitAmount.toFixed(2)}`
              : "-",
          credit:
            transaction.creditAmount > 0
              ? `Rs ${transaction.creditAmount.toFixed(2)}`
              : "-",
          balance: `Rs ${transaction.balance.toFixed(2)}`,
          action: (
            <button
              onClick={() => {
                setSelectedTransaction(transaction);
                setShowPopup(true);
              }}
              className="text-blue-600 hover:text-blue-800"
            >
              <Icon icon="mdi:eye" width="20" height="20" />
            </button>
          ),
        }))
      : [],
  };

  return (
    <div className="space-y-6">
      {showPopup && selectedTransaction && (
        <PopupModal onClose={() => setShowPopup(false)}>
          <TransactionDetails
            transaction={selectedTransaction}
            onClose={() => setShowPopup(false)}
          />
        </PopupModal>
      )}

      {showPrint && (
        <PopupModal onClose={() => setshowPrint(false)}>
          <PrintLayout onClose={() => setshowPrint(false)} />
        </PopupModal>
      )}

      {showEdit && (
        <PopupModal onClose={() => setshowEdit(false)}>
          <EditPayment onclose={() => setshowEdit(false)} />
        </PopupModal>
      )}

      {/* Booking Payment Summary */}
      <div className="bg-white p-4 rounded-md shadow-sm">
        <h2 className="text-lg font-semibold mb-4">Payment Summary</h2>
        <MasterTable
          columns={summaryTableData?.columns}
          rows={summaryTableData?.rows}
          loading={isLoadingBalance}
          sortBy="createdAt"
          sortOrder="desc"
        />
      </div>

      {/* Guest Transactions for this Booking */}
      <div className="bg-white p-4 rounded-md shadow-sm">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-lg font-semibold">Transaction History</h2>
          <button
            onClick={() => setshowEdit(true)}
            className="bg-blue-600 px-4 py-2 text-sm rounded-lg text-white font-semibold flex justify-center items-center gap-2"
          >
            <Icon icon="mdi:plus" width="20" height="20" />
            Add Payment
          </button>
        </div>

        <MasterTable
          columns={transactionsTableData?.columns}
          rows={transactionsTableData?.rows}
          loading={isLoadingTransactions}
          sortBy="date"
          sortOrder="desc"
        />
      </div>
    </div>
  );
};

// Transaction Details Component
const TransactionDetails = ({
  transaction,
  onClose,
}: {
  transaction: any;
  onClose: () => void;
}) => {
  const isPayment = transaction.debitAmount > 0;

  return (
    <div className="w-full max-w-screen-sm">
      <div className="relative flex items-center justify-between bg-[#EBFEF4]">
        <h1 className="w-full p-4 text-center text-semibold">
          {isPayment ? "Payment Details" : "Transaction Details"}
        </h1>
        <button
          className="absolute flex items-center justify-center p-1 bg-black rounded-full top-4 right-4"
          onClick={onClose}
        >
          <Icon
            icon="fluent-mdl2:calculator-multiply"
            width="14"
            height="14"
            className="text-white"
          />
        </button>
      </div>
      <div className="p-6">
        <div className="grid grid-cols-2 gap-4">
          <div className="col-span-2 p-4 bg-gray-50 rounded-md">
            <h2 className="mb-2 text-lg font-semibold">
              {isPayment ? "Payment Information" : "Transaction Information"}
            </h2>
            <div className="grid grid-cols-2 gap-2">
              <div>
                <p className="text-sm text-gray-500">Reference Number</p>
                <p className="font-medium">{transaction.referenceNo}</p>
              </div>
              <div>
                <p className="text-sm text-gray-500">Date</p>
                <p className="font-medium">
                  {moment(transaction.date).format("MMM DD, YYYY")}
                </p>
              </div>
              <div className="col-span-2">
                <p className="text-sm text-gray-500">Description</p>
                <p className="font-medium">{transaction.description}</p>
              </div>
              {isPayment ? (
                <div>
                  <p className="text-sm text-gray-500">Amount Paid</p>
                  <p className="font-medium text-green-600">
                    Rs {transaction.debitAmount.toFixed(2)}
                  </p>
                </div>
              ) : (
                <>
                  <div>
                    <p className="text-sm text-gray-500">Debit Amount</p>
                    <p className="font-medium text-red-600">
                      Rs {transaction.debitAmount.toFixed(2)}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">Credit Amount</p>
                    <p className="font-medium text-green-600">
                      Rs {transaction.creditAmount.toFixed(2)}
                    </p>
                  </div>
                </>
              )}
              <div>
                <p className="text-sm text-gray-500">Balance</p>
                <p className="font-medium">
                  Rs {transaction.balance.toFixed(2)}
                </p>
              </div>
            </div>
          </div>

          <div className="col-span-2 p-4 bg-gray-50 rounded-md">
            <h2 className="mb-2 text-lg font-semibold">Booking Information</h2>
            <div className="grid grid-cols-2 gap-2">
              <div>
                <p className="text-sm text-gray-500">Booking ID</p>
                <p className="font-medium">
                  B-{transaction.booking.toString().slice(-5)}
                </p>
              </div>
              <div>
                <p className="text-sm text-gray-500">Guest</p>
                <p className="font-medium">
                  {transaction.guest ? transaction.guest.name : "N/A"}
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Payment;

export const ViewPayment = ({ onClose }: { onClose: () => void }) => {
  const data = [
    {
      label: "Date:",
      value: "19/07/2024",
    },
    {
      label: "Payment Method:",
      value: "Credit Card",
    },
    {
      label: "Transaction ID:",
      value: "TXN-123456",
    },
    {
      label: "Booking Number:",
      value: "20",
    },
    {
      label: "Invoice Number:",
      value: "35",
    },
    {
      label: "Amount:",
      value: "$400",
    },
    {
      label: "Package Type:",
      value: "Standard",
    },
    {
      label: "Payment Status:",
      value: "Pending",
    },
    {
      label: "Details:",
      value: "Room Charges (2 Nights), Breakfast, Spa services, GYM",
    },
  ];

  return (
    <div className="w-full min-w-[50rem] h-full min-h-[20rem]">
      <div className="w-full h-12 flex justify-between bg-[#EBFEF4] items-center px-4">
        <div className="flex items-center justify-center flex-1 w-full">
          <h1 className="text-lg font-semibold text-black">View Payment</h1>
        </div>
        <div>
          <img src={cross} alt="" onClick={onClose} />
        </div>
      </div>

      <div className="flex flex-col gap-6 p-4">
        <div className="w-full p-4 border rounded-lg">
          <div className="grid grid-cols-3 gap-4">
            {data.slice(0, 3).map((item, index) => (
              <div key={index}>
                <section className="flex items-center gap-2">
                  <h1 className="font-semibold">{item.label}</h1>
                  <span>{item.value}</span>
                </section>
              </div>
            ))}
          </div>
        </div>

        <div className="w-full p-4 border rounded-lg">
          <div className="grid grid-cols-3 gap-4">
            {data.slice(3, 6).map((item, index) => (
              <div key={index}>
                <section className="flex items-center gap-2">
                  <h1 className="font-semibold">{item.label}</h1>
                  <span>{item.value}</span>
                </section>
              </div>
            ))}
          </div>
        </div>

        <div className="w-full p-4 border rounded-lg">
          <div className="grid grid-cols-3 gap-4">
            {data.slice(6, 9).map((item, index) => (
              <div key={index}>
                <section className="flex items-end h-full gap-2">
                  <h1 className="font-semibold">{item.label}</h1>
                  <span className="text-sm">{item.value}</span>
                </section>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};
