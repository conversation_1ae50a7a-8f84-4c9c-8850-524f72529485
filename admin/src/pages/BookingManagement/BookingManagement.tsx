import { get } from "lodash";
import moment from "moment";
import { useNavigate } from "react-router-dom";
import Header from "../../components/Header";
import MasterTable from "../../layouts/Table/MasterTable";
import { TableAction } from "../../layouts/Table/TableAction";
import { FrontendRoutes } from "../../routes";
import {
  useDeleteBooking,
  useGetBookings,
} from "../../server-action/API/BookingManagement/BookingManagement";
import { setBookingData } from "../../store/reservation";
import { useEffect, useMemo, useState } from "react";
import BookingFilter from "./components/BookingFilter";
import { DateForamter } from "../../components/DateFormater";

const BookingManagement = () => {
  const nav = useNavigate();
  const [searchTerm, setSearchTerm] = useState("");
  const [filters, setFilters] = useState({
    status: "all",
    reservationDate: "",
    checkIn: "",
    checkOut: "",
    minAmount: "",
    maxAmount: "",
  });

  const { data: bookingData, isLoading, isSuccess, refetch } = useGetBookings();
  const { mutate: deleteBooking } = useDeleteBooking();

  useEffect(() => {
    refetch();
  }, [refetch]);

  const filteredBookings = useMemo(() => {
    if (!bookingData?.data) return [];

    return bookingData.data.filter((item: any) => {
      const guestName = get(item, "guest.name", "").toLowerCase();
      const roomNo = get(item, "room.roomNo", "").toLowerCase();
      const bookingStatus = get(item, "status", "").toLowerCase();
      const reservationDate = get(item, "reservationDate", "");
      const checkInDate = get(item, "checkIn", "");
      const checkOutDate = get(item, "checkOut", "");

      const matchesSearch =
        searchTerm === "" ||
        guestName.includes(searchTerm.toLowerCase()) ||
        roomNo.includes(searchTerm.toLowerCase()) ||
        bookingStatus.includes(searchTerm.toLowerCase());

      const matchesStatus =
        filters.status === "all" ||
        bookingStatus === filters.status.toLowerCase();

      let matchesReservationDate = true;
      if (filters.reservationDate && reservationDate) {
        const selectedDate = moment(filters.reservationDate, "YYYY-MM-DD");
        const bookingDate = moment(reservationDate);
        matchesReservationDate =
          bookingDate.isValid() && bookingDate.isSame(selectedDate, "day");
      }

      let matchesCheckIn = true;
      if (filters.checkIn && checkInDate) {
        const selectedDate = moment(filters.checkIn, "YYYY-MM-DD");
        const bookingDate = moment(checkInDate);
        matchesCheckIn =
          bookingDate.isValid() && bookingDate.isSame(selectedDate, "day");
      }

      let matchesCheckOut = true;
      if (filters.checkOut && checkOutDate) {
        const selectedDate = moment(filters.checkOut, "YYYY-MM-DD");
        const bookingDate = moment(checkOutDate);
        matchesCheckOut =
          bookingDate.isValid() && bookingDate.isSame(selectedDate, "day");
      }

      return (
        matchesSearch &&
        matchesStatus &&
        matchesReservationDate &&
        matchesCheckIn &&
        matchesCheckOut
      );
    });
  }, [bookingData, searchTerm, filters]);

  const tableData = {
    columns: [
      { title: "ID No.", key: "tokenid" },
      { title: "Guest Name", key: "GuestName" },
      { title: "Room", key: "Room" },
      { title: "Booking Status", key: "status" },
      { title: "Reservation Date", key: "reservationDate" },
      { title: "Check In", key: "checkin" },
      { title: "Check Out", key: "checkout" },
      { title: "Expected Check Out", key: "expectedCheckout" },
      { title: "Total Amount", key: "amount" },
      { title: "Paid Amount", key: "paidAmount" },
      { title: "Actions", key: "action" },
    ],
    rows: filteredBookings.map((item: any) => ({
      key: item?._id,
      tokenid: `B-${get(item, "_id", "").slice(-5)}`,
      GuestName: get(item, "guest.name", ""),
      Room: `${get(item, "room.roomNo", "")} / ${get(
        item,
        "room.roomType.name",
        ""
      )}`,
      status: get(item, "status", ""),
      reservationDate: get(item, "reservationDate")
        ? DateForamter(item?.reservationDate)
        : "-",
      checkin: DateForamter(item?.checkIn),
      checkout: get(item, "checkOut") ? DateForamter(item?.checkOut) : "-",
      expectedCheckout: get(item, "expectedCheckOut")
        ? DateForamter(item?.expectedCheckOut)
        : "-",
      amount: `${get(item, "amount", 0).toLocaleString()} NPR`,
      paidAmount: `${get(item, "amountPaid", 0).toLocaleString()} NPR`,
      action: (
        <TableAction
          onShow={() => nav(`${FrontendRoutes.VIEWBOOKING}/${item?._id}`)}
          onEdit={() => {
            setBookingData(item);
            nav(`/booking-management/reservation/edit/${item?._id}`);
          }}
          onDelete={async () => deleteBooking(item?._id)}
        />
      ),
    })),
  };

  return (
    <div>
      <Header title="Reservation" route="/booking-management/reservation/add" />
      <div className="p-4 border bg-white rounded-md">
        <BookingFilter
          onSearch={(value) => setSearchTerm(value)}
          onApplyFilters={(newFilters) => setFilters(newFilters)}
          statusOptions={[
            { label: "Pending", value: "pending" },
            { label: "Confirmed", value: "confirmed" },
            { label: "CHECKED_IN", value: "checked-in" },
            { label: "CHECKED_OUT", value: "checked-out" },
            { label: "CANCELLED", value: "cancelled" },
            { label: "NO_SHOW", value: "no-show" },
          ]}
        />
      </div>
      <div className="my-2">
        <MasterTable
          columns={tableData.columns}
          rows={tableData.rows}
          loading={isLoading}
          canSearch={false}
        />
      </div>
    </div>
  );
};

export default BookingManagement;
