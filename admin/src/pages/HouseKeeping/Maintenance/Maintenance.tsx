import { Icon } from "@iconify/react/dist/iconify.js";
import MasterTable from "../../../layouts/Table/MasterTable";
import { TableAction } from "../../../layouts/Table/TableAction";
import Header from "../../../components/Header";
import { useState } from "react";
import MaintenanceForm from "./Component/MaintenanceForm";
import { PopupModal } from "../../../components";
import {
  useGetAllTickets,
  useDeleteTicket,
} from "../../../server-action/API/Ticket/Ticket";
import get from "lodash/get";
import { Status } from "../../../components/Status";

interface FormState {
  state: boolean;
  edit: boolean;
  editData?: any;
  ticketStatus?: string;
}

interface Ticket {
  _id: string;
  room?: {
    roomNo?: string;
  };
  user?: {
    name?: string;
  };
  ticketStatus?: string;
}

const Maintenance = () => {
  const [showPopup, setShowPopup] = useState("");
  const { data, isLoading, isSuccess } = useGetAllTickets({
    ticketCategory: "maintenance",
  });
  const { mutate: deleteModule } = useDeleteTicket();
  const [imagePreview, setImagePreview] = useState(false);
  const [formState, setFormState] = useState<FormState>({
    state: false,
    edit: true,
    editData: null,
  });

  console.log(data, "mainteancece");

  const tableData = {
    columns: [
      {
        title: "Room Number",
        key: "room",
      },
      {
        title: "Assigned Staff",
        key: "user",
      },
      {
        title: "Reported By",
        key: "user",
      },
      {
        title: "Issue type",
        key: "issueType",
      },
      {
        title: "Description",
        key: "notes",
      },
      {
        title: "Status",
        key: "ticketStatus",
      },
      {
        title: "Actions",
        key: "action",
      },
    ],
    rows: isSuccess
      ? (data as Ticket[])?.map((item) => ({
          id: item._id ?? "",
          room: get(item, "room.roomNo", "-"),
          user: get(item, "user.name", "-"),
          reportedBy: get(item, "reportedBy", "-"),
          issueType: get(item, "issueType", "-"),
          notes: get(item, "notes", "-"),
          attachment: get(item, "attachment", "-"),
          ticketStatus: <Status status={item?.ticketStatus ?? ""} />,

          action: (
            <TableAction
              onShow={() => {
                setFormState({
                  edit: false,
                  state: false,
                  editData: item,
                });
                setShowPopup("view");
              }}
              onEdit={() =>
                setFormState({
                  edit: true,
                  state: true,
                  editData: item,
                })
              }
              onDelete={() => {
                deleteModule(item._id as string);
              }}
            />
          ),
        }))
      : [],
  };

  return (
    <div>
      {showPopup === "view" && formState.editData && (
        <PopupModal onClose={() => setShowPopup("")}>
          <div className="w-full bg-white">
            <div className="relative flex items-center justify-between bg-[#EBFEF4]">
              <h1 className="w-full p-4 text-center font-semibold text-lg">
                Maintenance Details
              </h1>
              <button
                className="absolute flex items-center justify-center p-1 bg-black rounded-full top-4 right-4"
                onClick={() => setShowPopup("")}
              >
                <svg
                  width="14"
                  height="14"
                  viewBox="0 0 14 14"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                  className="text-white"
                >
                  <path
                    d="M1 1L13 13M1 13L13 1"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                  />
                </svg>
              </button>
            </div>{" "}
            <div className="p-8 flex justify-center">
              <div className="border rounded-lg p-8 w-full max-w-4xl shadow-md bg-white">
                <div className="mb-6 pb-5 border-b">
                  <div className="flex items-center space-x-3">
                    <p className="text-base text-gray-500">
                      Reported Date & Time :
                    </p>
                    <p className="font-medium text-base">
                      {`${get(formState.editData, "date", "-")} - ${get(
                        formState.editData,
                        "time",
                        "-"
                      )}`}
                    </p>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-x-40 gap-y-6 mb-6 pb-5 border-b">
                  <div className="flex items-center space-x-2">
                    <p className="text-base text-gray-500">Room No :</p>
                    <p className="font-medium text-base">
                      {get(formState.editData, "room.roomNo", "-")}
                    </p>
                  </div>

                  <div className="flex items-center space-x-2">
                    <p className="text-base text-gray-500">Reported By :</p>
                    <p className="font-medium text-base">
                      {get(formState.editData, "user.name", "-")}
                    </p>
                  </div>

                  <div className="flex items-center space-x-2">
                    <p className="text-base text-gray-500">Assigned Staff :</p>
                    <p className="font-medium text-base">
                      {get(formState.editData, "user.name", "-")}
                    </p>
                  </div>

                  <div className="flex items-center space-x-2">
                    <p className="text-base text-gray-500">Issue Type :</p>
                    <p className="font-medium text-base">
                      {get(formState.editData, "issueType", "-")}
                    </p>
                  </div>

                  <div className="flex items-center space-x-2">
                    <p className="text-base text-gray-500">Status :</p>
                    <span className="inline-flex items-center px-3 py-1.5 text-sm font-semibold text-yellow-800 bg-yellow-100 rounded-full">
                      {get(formState.editData, "ticketStatus", "-")}
                    </span>
                  </div>

                  <div className="flex items-center space-x-2">
                    <p className="text-base text-gray-500">
                      Before/After Image :
                    </p>
                    {formState?.editData?.attachment?.length > 0 ? (
                      <TableAction onShow={() => setImagePreview(true)} />
                    ) : (
                      <p className="font-medium text-base">-</p>
                    )}
                  </div>
                </div>

                <div className="flex items-start space-x-3">
                  <p className="text-base text-gray-500">Notes :</p>
                  <p className="font-medium text-base">
                    {get(formState.editData, "notes", "-")}
                  </p>
                </div>
              </div>
            </div>
            {formState?.editData?.attachment?.length > 0 && imagePreview && (
              <div className="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 z-50">
                <div className="bg-white p-6 rounded-lg shadow-lg max-w-xl w-full relative">
                  <button
                    className="absolute top-2 right-2 text-white bg-black rounded-full p-1"
                    onClick={() => setImagePreview(false)}
                  >
                    <Icon
                      icon="fluent-mdl2:calculator-multiply"
                      width="20"
                      height="20"
                    />
                  </button>
                  <div className="flex flex-wrap justify-center gap-4 mt-8">
                    {get(formState.editData, "attachment", []).map(
                      (img: any, index: number) => (
                        <img
                          key={index}
                          src={`http://************:8085/${img}`}
                          alt="img"
                          className="w-80 h-80 object-cover border rounded-md"
                        />
                      )
                    )}
                  </div>
                </div>
              </div>
            )}
          </div>
        </PopupModal>
      )}

      <div>
        <Header title="Maintenance" showButton={false} />
        <MasterTable
          columns={tableData?.columns}
          rows={tableData.rows}
          loading={isLoading}
        />
        {formState.state && (
          <MaintenanceForm
            close={() =>
              setFormState({ state: false, edit: false, editData: null })
            }
            edit={formState.edit}
            editData={formState.editData}
          />
        )}
      </div>
    </div>
  );
};
export default Maintenance;
