import MasterTable from "../../../layouts/Table/MasterTable";
import { TableAction } from "../../../layouts/Table/TableAction";
import Header from "../../../components/Header";
import { useGetAllTickets } from "../../../server-action/API/Ticket/Ticket";
import get from "lodash/get";
import { PopupModal } from "../../../components";

import { useState } from "react";
import { Status } from "../../../components/Status";

interface Ticket {
  _id: string;
  room?: {
    roomNo?: string;
  };
  ticketCategory?: string;
  user?: {
    name?: string;
  };
}

interface FormState {
  state: boolean;
  edit: boolean;
  editData: any;
}

const RoomInspection = () => {
  const [showPopup, setShowPopup] = useState("");
  const { data, isLoading, isSuccess } = useGetAllTickets({
    ticketCategory: "inspection",
  });

  console.log(data, "inspection");
  const [formState, setFormState] = useState<FormState>({
    state: false,
    edit: true,
    editData: null,
  });

  const tableData = {
    columns: [
      {
        title: "Room Number",
        key: "room",
      },
      {
        title: "Ticket Category",
        key: "ticketCategory",
      },
      {
        title: "Accepted User",
        key: "user",
      },

      {
        title: "Status",
        key: "ticketStatus",
      },
      {
        title: "Actions",
        key: "action",
      },
    ],
    rows: isSuccess
      ? (data as Ticket[])?.map((item) => ({
          id: item._id,
          room: get(item, "room.roomNo", "-"),
          ticketCategory: get(item, "ticketCategory", "-"),
          user: get(item, "user.name", "-"),
          ticketStatus: <Status status={item?.ticketCategory || "-"} />,
          action: (
            <TableAction
              onShow={() => {
                setFormState({
                  edit: false,
                  state: false,
                  editData: item,
                });
                setShowPopup("view");
              }}
            />
          ),
        }))
      : [],
  };

  return (
    <div>
      {showPopup === "view" && formState.editData && (
        <PopupModal onClose={() => setShowPopup("")}>
          <div className="w-full bg-white">
            <div className="relative flex items-center justify-between bg-[#F1F6FD]">
              <h1 className="w-full p-4 text-center font-semibold text-lg">
                Inspection Details
              </h1>
              <button
                className="absolute flex items-center justify-center p-1 bg-black rounded-full top-4 right-4"
                onClick={() => setShowPopup("")}
              >
                <svg
                  width="14"
                  height="14"
                  viewBox="0 0 14 14"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                  className="text-white"
                >
                  <path
                    d="M1 1L13 13M1 13L13 1"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                  />
                </svg>
              </button>
            </div>

            <div className="p-8 flex justify-center">
              <div className="border rounded-lg p-8 w-full max-w-4xl shadow-md bg-white">
                <div className="mb-6 pb-5 border-b">
                  <div className="flex items-center space-x-3">
                    <p className="text-base text-gray-500">Date & Time :</p>
                    <p className="font-medium text-base">
                      {`${get(formState.editData, "date", "-")} - ${get(
                        formState.editData,
                        "time",
                        "-"
                      )}`}
                    </p>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-x-40 gap-y-6 mb-6 pb-5 border-b">
                  <div className="flex items-center space-x-2">
                    <p className="text-base text-gray-500">Room No :</p>
                    <p className="font-medium text-base">
                      {get(formState.editData, "room.roomNo", "-")}
                    </p>
                  </div>

                  <div className="flex items-center space-x-2">
                    <p className="text-base text-gray-500">Ticket Category :</p>
                    <p className="font-medium text-base capitalize">
                      {get(formState.editData, "ticketCategory", "-")}
                    </p>
                  </div>

                  <div className="flex items-center space-x-2">
                    <p className="text-base text-gray-500 whitespace-nowrap">
                      Reported By :
                    </p>
                    <p className="font-medium text-base whitespace-nowrap capitalize">
                      {get(formState.editData, "user.name", "-")}
                    </p>
                  </div>

                  <div className="flex items-center space-x-2">
                    <p className="text-base text-gray-500">Status :</p>
                    <span className="inline-flex capitalize items-center px-3 py-1.5 text-sm font-semibold text-yellow-800 bg-yellow-100 rounded-full">
                      {get(formState.editData, "ticketStatus", "-")}
                    </span>
                  </div>
                </div>

                {/* Description */}
                <div className="flex items-start space-x-3">
                  <p className="text-base text-gray-500">Description :</p>
                  <p className="font-medium text-base capitalize">
                    {get(formState.editData, "notes", "-")}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </PopupModal>
      )}

      <div>
        <Header showButton={false} title="Room Cleaning" />
        <MasterTable
          columns={tableData?.columns}
          rows={tableData.rows}
          loading={isLoading}
        />
      </div>
    </div>
  );
};

export default RoomInspection;
