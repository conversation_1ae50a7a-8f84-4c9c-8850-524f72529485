import { useState } from "react";
import { Card } from "../../../../components/Card";
import { TableAction } from "../../../../layouts/Table/TableAction";
import MasterTable from "../../../../layouts/Table/MasterTable";
import {
  useDeleteStore,
  useGetAllStore,
} from "../../../../server-action/API/StoreManagement/store";
import { AddSubStore } from "./AddSubStore";
import StoreDetails from "./StoreDetails";

const SubstoreList = () => {
  const { data: store, isLoading } = useGetAllStore();
  const { mutate: deleteStore } = useDeleteStore();
  const subStores = store?.filter((item: any) => item.type !== "main") || [];

  const [editModal, setEditModal] = useState(false);
  const [viewModal, setViewModal] = useState(false);
  const [selectedStore, setSelectedStore] = useState<any>(null);

  const columns = [
    {
      key: "sn",
      title: "S.N",
    },
    {
      key: "name",
      title: "Store Name",
    },
    {
      key: "type",
      title: "Type",
    },
    {
      key: "location",
      title: "Location",
    },
    {
      key: "status",
      title: "Status",
    },
    {
      key: "action",
      title: "Action",
    },
  ];

  const handleEdit = (store: any) => {
    setSelectedStore(store);
    setEditModal(true);
  };

  const handleView = (store: any) => {
    setSelectedStore(store);
    setViewModal(true);
  };

  const handleDelete = (id: string) => {
    deleteStore(id);
  };

  const closeEditModal = () => {
    setEditModal(false);
    setSelectedStore(null);
  };

  const closeViewModal = () => {
    setViewModal(false);
    setSelectedStore(null);
  };

  const rows = subStores.map((store: any, index: number) => ({
    key: store._id || index,
    sn: index + 1,
    name: store.name || "N/A",
    type: store.type === "floor" ? "Floor Store" : store.type || "N/A",
    location: store.location || "N/A",
    status: store.isActive ? (
      <span className="px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs font-medium">
        Active
      </span>
    ) : (
      <span className="px-2 py-1 bg-red-100 text-red-800 rounded-full text-xs font-medium">
        Inactive
      </span>
    ),
    action: (
      <TableAction
        onShow={() => handleView(store)}
        onEdit={() => handleEdit(store)}
        onDelete={() => handleDelete(store._id)}
      />
    ),
  }));

  return (
    <>
      <div>
        {subStores.length > 0 ? (
          <>
            {/* <h2 className="text-lg font-semibold mb-4">Substore List</h2> */}
            <MasterTable
              columns={columns}
              rows={rows}
              loading={isLoading}
              sortBy="createdAt"
              sortOrder="desc"
            />
          </>
        ) : (
          <div className="text-center py-8">
            <p className="text-gray-500">
              No substores found. Click the + button to add a new substore.
            </p>
          </div>
        )}
      </div>

      {editModal && (
        <AddSubStore onClose={closeEditModal} editData={selectedStore} />
      )}
      {viewModal && (
        <StoreDetails store={selectedStore} onClose={closeViewModal} />
      )}
    </>
  );
};

export default SubstoreList;
