import { Form, FormikProvider, useFormik } from "formik";
import { PopupModal } from "../../../../components";
import { Icon } from "@iconify/react/dist/iconify.js";
import moment from "moment";
import { FieldArray } from "formik";
import {
  useRegisterUser,
  useUpdateOtherUser,
} from "../../../../server-action/API/auth";
import {
  useGetDepartment,
  useGetDesignation,
  useGetReligion,
  useGetShiftType,
} from "../../../../server-action/API/EmployeeConfiguration/jobconfig";
import {
  useGetAllowance,
  useGetDeduction,
} from "../../../../server-action/API/EmployeeConfiguration/salaryconfig";
import { Card, CardContent } from "../../../../components/Card";
import { useEffect, useState } from "react";
import { IMAGE_URL } from "../../../../constant/constant";
import { EmployeeValidationSchema } from "./EmployeeValidationSchema";
import Stepper from "../../../../components/Stepper";
import { FormField } from "./EmployeeFormField";

interface AllowanceItem {
  name: string;
}

interface DeductionItem {
  name: string;
}

interface EmployeeFormValues {
  role: "staff" | "housekeeper";
  name: string;
  phoneNumber: string;
  email: string;
  password: string;
  showPassword?: boolean; // Added for password visibility toggle
  DOB: string;
  gender: "male" | "female" | "other";
  tempAddress: {
    district: string;
    municipality: string;
    tole: string;
  };
  permanentAddress: {
    district: string;
    municipality: string;
    tole: string;
  };
  religion: string;
  maritalStatus: "married" | "unmarried";
  staffType: "permanent" | "temporary";
  joinDate: string;
  department: string;
  designation: string;
  shiftType: string;
  applyTDS: boolean;
  basicSalary: number;
  annualSalary: number;
  festivalAllowanceMonth: string;
  overTimePerHr: number;
  paidLeave: number;
  allowance: AllowanceItem[];
  deduction: DeductionItem[];
  photo: (File | string)[];
  citizenship: (File | string)[];
  license: (File | string)[];
  resume: (File | string)[];
}

interface EmployeeFormProps {
  onClose: () => void;
  editData: any | null;
}

const defaultInitialValues: EmployeeFormValues = {
  role: "staff",
  name: "",
  phoneNumber: "",
  email: "",
  password: "",
  showPassword: false, // Initialize password visibility as hidden
  DOB: "",
  gender: "male",
  tempAddress: { district: "", municipality: "", tole: "" },
  permanentAddress: { district: "", municipality: "", tole: "" },
  religion: "",
  maritalStatus: "unmarried",
  staffType: "permanent",
  joinDate: "",
  department: "",
  designation: "",
  shiftType: "",
  applyTDS: false,
  basicSalary: 0,
  annualSalary: 0,
  festivalAllowanceMonth: "",
  overTimePerHr: 0,
  paidLeave: 0,
  allowance: [{ name: "" }],
  deduction: [{ name: "" }],
  photo: [],
  citizenship: [],
  license: [],
  resume: [],
};

const EmployeeForm = ({ onClose, editData }: EmployeeFormProps) => {
  const [currentStep, setCurrentStep] = useState(1);
  const {
    mutateAsync: registerUser,
    reset: resetCreate,
    isPending: isCreating,
  } = useRegisterUser();
  const {
    mutateAsync: updateUser,
    reset: resetUpdate,
    isPending: isUpdating,
  } = useUpdateOtherUser();

  const stepFields = {
    1: [
      "role",
      "name",
      "phoneNumber",
      "email",
      "password",
      "DOB",
      "gender",
      "tempAddress.district",
      "tempAddress.municipality",
      "tempAddress.tole",
      "permanentAddress.district",
      "permanentAddress.municipality",
      "permanentAddress.tole",
      "religion",
      "maritalStatus",
      "staffType",
    ],
    2: ["joinDate", "department", "designation", "shiftType"],
    3: [
      "basicSalary",
      "annualSalary",
      "festivalAllowanceMonth",
      "overTimePerHr",
      "paidLeave",
      "allowance",
      "deduction",
    ],
    4: ["photo", "citizenship", "license", "resume"],
  };

  const formik = useFormik<EmployeeFormValues>({
    enableReinitialize: true,
    initialValues: editData
      ? {
          role: Array.isArray(editData.role)
            ? editData.role.includes("housekeeper")
              ? "housekeeper"
              : "staff"
            : editData.role === "housekeeper" || editData.role === "staff"
            ? editData.role
            : "staff",
          name: editData.name ?? "",
          phoneNumber: editData.phoneNumber ?? "",
          email: editData.email ?? "",
          password: editData.password ?? "",
          showPassword: false, // Initialize password visibility as hidden in edit mode
          DOB: editData.DOB ? moment(editData.DOB).format("YYYY-MM-DD") : "",
          gender: editData.gender?._id ?? "male",
          tempAddress: {
            district: editData.tempAddress?.district ?? "",
            municipality: editData.tempAddress?.municipality ?? "",
            tole: editData.tempAddress?.tole ?? "",
          },
          permanentAddress: {
            district: editData.permanentAddress?.district ?? "",
            municipality: editData.permanentAddress?.municipality ?? "",
            tole: editData.permanentAddress?.tole ?? "",
          },
          religion: editData.religion ?? "",
          maritalStatus: editData.maritalStatus ?? "unmarried",
          staffType: editData.staffType ?? "permanent",
          joinDate: editData.joinDate
            ? moment(editData.joinDate).format("YYYY-MM-DD")
            : "",
          department: editData.department?._id ?? "",
          designation: editData.designation?._id ?? "",
          shiftType: editData.shiftType?._id ?? "",
          applyTDS: editData.applyTDS ?? false,
          basicSalary: editData.basicSalary ?? 0,
          annualSalary: editData.annualSalary ?? 0,
          festivalAllowanceMonth: editData.festivalAllowanceMonth ?? "",
          overTimePerHr: editData.overTimePerHr ?? 0,
          paidLeave: editData.paidLeave ?? 0,
          allowance:
            Array.isArray(editData.allowance) && editData.allowance.length
              ? editData.allowance.map((item: any) => ({
                  name:
                    typeof item === "string"
                      ? item
                      : item._id || item.name || "",
                }))
              : [{ name: "" }],
          deduction:
            Array.isArray(editData.deduction) && editData.deduction.length
              ? editData.deduction.map((item: any) => ({
                  name:
                    typeof item === "string"
                      ? item
                      : item._id || item.name || "",
                }))
              : [{ name: "" }],
          photo: Array.isArray(editData.photo)
            ? editData.photo.filter(
                (item: any) => typeof item === "string" && item.length > 0
              )
            : [],
          citizenship: Array.isArray(editData.citizenship)
            ? editData.citizenship.filter(
                (item: any) => typeof item === "string" && item.length > 0
              )
            : [],
          license: Array.isArray(editData.license)
            ? editData.license.filter(
                (item: any) => typeof item === "string" && item.length > 0
              )
            : [],
          resume: Array.isArray(editData.resume)
            ? editData.resume.filter(
                (item: any) => typeof item === "string" && item.length > 0
              )
            : [],
        }
      : defaultInitialValues,
    validationSchema: EmployeeValidationSchema,
    onSubmit: async (values) => {
      // Validate the entire form
      await formik.validateForm(values);
      const errors = formik.errors;

      // Check if there are any validation errors
      if (Object.keys(errors).length > 0) {
        // Find the first step with errors
        const firstErrorStep = findFirstErrorStep(errors);

        if (firstErrorStep) {
          // Navigate to the step with errors
          setCurrentStep(firstErrorStep);

          // Mark all fields in that step as touched to show the errors
          const touchedFields = stepFields[
            firstErrorStep as keyof typeof stepFields
          ].reduce((acc: Record<string, any>, field) => {
            if (field.includes(".")) {
              const [parent, child] = field.split(".");
              return {
                ...acc,
                [parent]: { ...(acc[parent] || {}), [child]: true },
              };
            }
            return { ...acc, [field]: true };
          }, {});

          formik.setTouched({ ...formik.touched, ...touchedFields }, true);

          // Show validation error message
          const errorContainer = document.getElementById(
            "step-validation-error"
          );
          if (errorContainer) {
            errorContainer.textContent =
              "Please fix all validation errors before submitting the form.";
            // Auto-hide the message after 5 seconds
            setTimeout(() => {
              if (errorContainer) {
                errorContainer.textContent = "";
              }
            }, 5000);
          }

          return;
        }
      }

      const formData = new FormData();
      // Prepare form data fields, excluding UI-only fields like showPassword
      const fields = [
        { key: "role", value: values.role },
        { key: "name", value: values.name },
        { key: "phoneNumber", value: values.phoneNumber },
        { key: "email", value: values.email },
        { key: "password", value: values.password },
        { key: "DOB", value: values.DOB },
        { key: "gender", value: values.gender },
        { key: "religion", value: values.religion },
        { key: "maritalStatus", value: values.maritalStatus },
        { key: "staffType", value: values.staffType },
        { key: "joinDate", value: values.joinDate },
        { key: "department", value: values.department },
        { key: "designation", value: values.designation },
        { key: "shiftType", value: values.shiftType },
        { key: "applyTDS", value: values.applyTDS.toString() },
        { key: "basicSalary", value: values.basicSalary.toString() },
        { key: "annualSalary", value: values.annualSalary.toString() },
        { key: "festivalAllowanceMonth", value: values.festivalAllowanceMonth },
        { key: "overTimePerHr", value: values.overTimePerHr.toString() },
        { key: "paidLeave", value: values.paidLeave.toString() },
      ];

      fields.forEach(({ key, value }) => {
        if (value !== undefined && value !== null) {
          formData.append(key, value.toString());
        }
      });

      formData.append(
        "tempAddress[district]",
        values.tempAddress.district || ""
      );
      formData.append(
        "tempAddress[municipality]",
        values.tempAddress.municipality || ""
      );
      formData.append("tempAddress[tole]", values.tempAddress.tole || "");
      formData.append(
        "permanentAddress[district]",
        values.permanentAddress.district || ""
      );
      formData.append(
        "permanentAddress[municipality]",
        values.permanentAddress.municipality || ""
      );
      formData.append(
        "permanentAddress[tole]",
        values.permanentAddress.tole || ""
      );

      values.allowance.forEach((item, index) => {
        if (item.name) {
          formData.append(`allowance[${index}]`, item.name);
        }
      });

      values.deduction.forEach((item, index) => {
        if (item.name) {
          formData.append(`deduction[${index}]`, item.name);
        }
      });

      ["photo", "citizenship", "license", "resume"].forEach((key) => {
        const files = values[key as keyof EmployeeFormValues] as (
          | File
          | string
        )[];
        if (files?.length > 0) {
          files.forEach((file) => {
            if (file instanceof File) {
              formData.append(key, file, file.name);
            } else if (typeof file === "string" && file.length > 0) {
              formData.append(key, file);
            }
          });
        }
      });

      try {
        if (editData) {
          await updateUser({ userData: formData, userId: editData._id });
          resetUpdate();
        } else {
          await registerUser(formData);
          resetCreate();
        }
        onClose();
      } catch (error) {
        console.error("Error submitting form:", error);
      }
    },
  });

  const findFirstErrorStep = (errors: any) => {
    for (let step = 1; step <= 4; step++) {
      const fields = stepFields[step as keyof typeof stepFields];
      for (const field of fields) {
        if (errors[field]) {
          return step;
        }
        if (field.includes(".")) {
          const [parent, child] = field.split(".");
          if (errors[parent]?.[child]) {
            return step;
          }
        }
        if (Array.isArray((formik.values as any)[field])) {
          if (errors[field]?.some((item: any) => item?.name)) {
            return step;
          }
          if (errors[field] && typeof errors[field] === "string") {
            return step;
          }
        }
      }
    }
    return null;
  };

  const validateCurrentStep = async () => {
    await formik.validateForm(formik.values);
    const errors = formik.errors;
    const fieldsToValidate = stepFields[currentStep as keyof typeof stepFields];

    // Check if any field in the current step has errors
    const hasErrors = fieldsToValidate.some((field) => {
      // Direct field error
      if ((errors as any)[field]) return true;

      // Nested field error (e.g., tempAddress.district)
      if (field.includes(".")) {
        const [parent, child] = field.split(".");
        return (errors as any)[parent]?.[child];
      }

      // Array field error (e.g., allowance, deduction)
      if (Array.isArray(formik.values[field as keyof EmployeeFormValues])) {
        return (
          // Check for errors in array items
          (errors as any)[field]?.some((item: any) => item?.name) ||
          // Check for error on the array itself
          ((errors as any)[field] && typeof (errors as any)[field] === "string")
        );
      }

      return false;
    });

    // If there are errors, mark all fields in this step as touched to show the errors
    if (hasErrors) {
      const touchedFields = fieldsToValidate.reduce(
        (acc: Record<string, any>, field) => {
          if (field.includes(".")) {
            const [parent, child] = field.split(".");
            return {
              ...acc,
              [parent]: { ...(acc[parent] || {}), [child]: true },
            };
          }
          return { ...acc, [field]: true };
        },
        {}
      );

      formik.setTouched({ ...formik.touched, ...touchedFields }, true);
    }

    return !hasErrors;
  };

  const handleStepNavigation = async (stepId: number) => {
    if (stepId === currentStep) return;

    // When trying to go forward, validate the current step first
    if (stepId > currentStep) {
      const isCurrentStepValid = await validateCurrentStep();

      if (!isCurrentStepValid) {
        // Show validation error message
        const errorContainer = document.getElementById("step-validation-error");
        if (errorContainer) {
          errorContainer.textContent =
            "Please fix the validation errors before proceeding to the next step.";
          // Auto-hide the message after 5 seconds
          setTimeout(() => {
            if (errorContainer) {
              errorContainer.textContent = "";
            }
          }, 5000);
        }
        return;
      }
    }

    // If going backward or if validation passed, proceed to the requested step
    setCurrentStep(stepId);
  };

  const steps = [
    { id: 1, label: "Basic Details" },
    { id: 2, label: "Service Area" },
    { id: 3, label: "Salary Details" },
    { id: 4, label: "Documents" },
  ];

  const renderStep = (stepId: number) => {
    switch (stepId) {
      case 1:
        return <BasicDetails formik={formik} />;
      case 2:
        return <JobDetails formik={formik} />;
      case 3:
        return <SalaryDetails formik={formik} />;
      case 4:
        return <DocumentDetails formik={formik} />;
      default:
        return null;
    }
  };

  return (
    <PopupModal onClose={onClose} classname="w-full max-w-5xl">
      <div className="relative flex items-center justify-between bg-[#F4F6FB] rounded-lg">
        <h1 className="w-full p-4 text-center font-semibold ">
          {editData ? "Edit Employee" : "Add Employee"}
        </h1>
        <button
          className="absolute flex items-center justify-center p-1 bg-black rounded-full top-4 right-4"
          onClick={onClose}
        >
          <Icon
            icon="fluent-mdl2:calculator-multiply"
            width="14"
            height="14"
            className="text-white"
          />
        </button>
      </div>
      <div className="p-4 max-h-[80vh] overflow-y-auto">
        <Stepper
          steps={steps}
          currentStep={currentStep}
          onStepClick={handleStepNavigation}
        />
        <FormikProvider value={formik}>
          <Form onSubmit={formik.handleSubmit}>
            <div className="mt-4">{renderStep(currentStep)}</div>
            <div className="flex items-center justify-end mt-6 space-x-4">
              <button
                type="button"
                onClick={() =>
                  handleStepNavigation(Math.max(1, currentStep - 1))
                }
                className="py-2 px-6 text-gray-700 border border-gray-300 rounded-md"
                disabled={currentStep === 1}
              >
                Previous
              </button>
              {/* Validation error message container */}
              <div
                id="step-validation-error"
                className="text-red text-sm mr-auto"
              ></div>

              {currentStep < 4 && (
                <button
                  type="button"
                  onClick={async () => {
                    // Use the same validation logic as in handleStepNavigation
                    const isValid = await validateCurrentStep();
                    if (isValid) {
                      handleStepNavigation(Math.min(4, currentStep + 1));
                    } else {
                      // Show validation error message
                      const errorContainer = document.getElementById(
                        "step-validation-error"
                      );
                      if (errorContainer) {
                        errorContainer.textContent =
                          "Please fix the validation errors before proceeding to the next step.";
                        // Auto-hide the message after 5 seconds
                        setTimeout(() => {
                          if (errorContainer) {
                            errorContainer.textContent = "";
                          }
                        }, 5000);
                      }
                    }
                  }}
                  className="py-2 px-6 text-white rounded-md bg-[#163381]"
                >
                  Next
                </button>
              )}
              {currentStep === 4 && (
                <button
                  type="submit"
                  disabled={isCreating || isUpdating}
                  className="py-2 px-6 text-white rounded-md bg-[#163381]"
                >
                  {isCreating || isUpdating ? (
                    <Icon icon="line-md:loading-loop" width="18" height="18" />
                  ) : (
                    "Save"
                  )}
                </button>
              )}
            </div>
          </Form>
        </FormikProvider>
      </div>
    </PopupModal>
  );
};

const BasicDetails = ({ formik }: { formik: any }) => {
  const { data: religionData, isSuccess: religionSuccess } = useGetReligion();

  const addressFields = {
    temporary: [
      {
        name: "tempAddress.district",
        label: "District",
        type: "text",
        placeholder: "Enter District",
        required: true,
      },
      {
        name: "tempAddress.municipality",
        label: "Municipality",
        type: "text",
        placeholder: "Enter Municipality",
        required: true,
      },
      {
        name: "tempAddress.tole",
        label: "Tole",
        type: "text",
        placeholder: "Enter Tole",
        required: true,
      },
    ],
    permanent: [
      {
        name: "permanentAddress.district",
        label: "District",
        type: "text",
        placeholder: "Enter District",
        required: true,
      },
      {
        name: "permanentAddress.municipality",
        label: "Municipality",
        type: "text",
        placeholder: "Enter Municipality",
        required: true,
      },
      {
        name: "permanentAddress.tole",
        label: "Tole",
        type: "text",
        placeholder: "Enter Tole",
        required: true,
      },
    ],
  };

  // State to track if Permanent Address is same as Temporary Address
  const [isSameAddress, setIsSameAddress] = useState(false);

  useEffect(() => {
    if (isSameAddress) {
      formik.setFieldValue(
        "permanentAddress.district",
        formik.values.tempAddress.district
      );
      formik.setFieldValue(
        "permanentAddress.municipality",
        formik.values.tempAddress.municipality
      );
      formik.setFieldValue(
        "permanentAddress.tole",
        formik.values.tempAddress.tole
      );
    }
  }, [
    isSameAddress,
    formik.values.tempAddress.district,
    formik.values.tempAddress.municipality,
    formik.values.tempAddress.tole,
    formik.setFieldValue,
  ]);

  // Handle checkbox change
  const handleSameAddressChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const checked = e.target.checked;
    setIsSameAddress(checked);
    if (!checked) {
      // Clear Permanent Address fields when unchecked
      formik.setFieldValue("permanentAddress", {
        district: "",
        municipality: "",
        tole: "",
      });
    }
  };

  return (
    <div className="space-y-4">
      <Card>
        <CardContent className="grid grid-cols-4 gap-4">
          <FormField
            name="role"
            label="Select Role"
            type="dropdown"
            placeholder="Select Role"
            options={[
              { label: "Staff", value: "staff" },
              { label: "Housekeeper", value: "housekeeper" },
            ]}
            formik={formik}
            required
          />
          <FormField
            name="name"
            label="Employee Name"
            type="text"
            placeholder="Enter Employee Name"
            formik={formik}
            required
          />
          <FormField
            name="phoneNumber"
            label="Phone Number"
            type="text"
            placeholder="9XXXXXXXXX"
            formik={formik}
            required
          />
          <FormField
            name="email"
            label="Email"
            type="email"
            placeholder="<EMAIL>"
            formik={formik}
            required
          />
          <div className="flex flex-col w-full">
            <label htmlFor="password" className="mb-1 text-sm">
              Password <span className="text-red">*</span>
            </label>
            <div className="relative">
              <input
                id="password"
                name="password"
                type={formik.values.showPassword ? "text" : "password"}
                placeholder="Enter Password"
                value={formik.values.password}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                className={`p-2 border rounded-md w-full ${
                  formik.touched.password && formik.errors.password
                    ? "border-red-500 focus:ring-red-500 focus:border-red-500"
                    : "border-gray-300 focus:ring-blue-500 focus:border-blue-500"
                }`}
              />
              <button
                type="button"
                className="absolute inset-y-0 right-0 pr-3 flex items-center"
                onClick={() =>
                  formik.setFieldValue(
                    "showPassword",
                    !formik.values.showPassword
                  )
                }
              >
                <Icon
                  icon={formik.values.showPassword ? "mdi:eye-off" : "mdi:eye"}
                  width="20"
                  height="20"
                  className="text-gray-500"
                />
              </button>
            </div>
            {formik.touched.password && formik.errors.password && (
              <div className="mt-1 text-xs text-red">
                {formik.errors.password}
              </div>
            )}
            {formik.values.password && (
              <div className="mt-2">
                <div className="text-xs mb-1">Password strength:</div>
                <div className="h-1 w-full bg-gray-200 rounded-full overflow-hidden">
                  <div
                    className={`h-full ${
                      formik.values.password.length < 6
                        ? "bg-red-500 w-1/4"
                        : formik.values.password.length < 8
                        ? "bg-yellow-500 w-2/4"
                        : formik.values.password.length < 10
                        ? "bg-blue-500 w-3/4"
                        : "bg-green-500 w-full"
                    }`}
                  ></div>
                </div>
                <div className="text-xs mt-1 text-gray-500">
                  {formik.values.password.length < 6
                    ? "Weak: Use at least 6 characters"
                    : formik.values.password.length < 8
                    ? "Fair: Consider using a longer password"
                    : formik.values.password.length < 10
                    ? "Good: Password has decent length"
                    : "Strong: Password has good length"}
                </div>
              </div>
            )}
          </div>
          <FormField
            name="DOB"
            label="Date of Birth"
            type="date"
            placeholder="Select Date of Birth"
            formik={formik}
            required
          />
          <FormField
            name="gender"
            label="Gender"
            type="dropdown"
            placeholder="Select Gender"
            options={[
              { label: "Male", value: "male" },
              { label: "Female", value: "female" },
              { label: "Other", value: "other" },
            ]}
            formik={formik}
            required
          />
        </CardContent>
      </Card>
      <Card>
        <CardContent className="grid grid-cols-2 gap-x-3 gap-y-1">
          <h3 className="col-span-4 font-medium">Temporary Address</h3>
          {addressFields.temporary.map((item, index) => (
            <FormField
              key={index}
              name={item.name}
              label={item.label}
              type={item.type}
              placeholder={item.placeholder}
              required={item.required}
              formik={formik}
            />
          ))}
          <div className="col-span-4 mt-2 flex items-center gap-3">
            <h3 className="font-medium">Permanent Address</h3>
            <label className="flex items-center gap-2 text-sm">
              <input
                type="checkbox"
                checked={isSameAddress}
                onChange={handleSameAddressChange}
                className="w-4 h-4"
              />
              Same as Temporary Address
            </label>
          </div>
          {addressFields.permanent.map((item, index) => (
            <FormField
              key={index}
              name={item.name}
              label={item.label}
              type={item.type}
              placeholder={item.placeholder}
              required={item.required}
              formik={formik}
              disabled={isSameAddress} // Disable field if same as temporary
            />
          ))}
        </CardContent>
      </Card>
      <Card>
        <CardContent className="grid grid-cols-4 gap-4">
          <FormField
            name="religion"
            label="Religion"
            type="dropdown"
            placeholder="Select Religion"
            options={
              religionSuccess
                ? religionData?.map((item) => ({
                    label: item.name,
                    value: item._id ?? "",
                  }))
                : []
            }
            formik={formik}
            required
          />
          <FormField
            name="maritalStatus"
            label="Marital Status"
            type="dropdown"
            placeholder="Select Marital Status"
            options={[
              { label: "Married", value: "married" },
              { label: "Unmarried", value: "unmarried" },
            ]}
            formik={formik}
            required
          />
          <FormField
            name="staffType"
            label="Staff Type"
            type="dropdown"
            placeholder="Select Staff Type"
            options={[
              { label: "Permanent", value: "permanent" },
              { label: "Temporary", value: "temporary" },
            ]}
            formik={formik}
            required
          />
        </CardContent>
      </Card>
    </div>
  );
};

const JobDetails = ({ formik }: { formik: any }) => {
  const { data: departmentData, isSuccess: departmentSuccess } =
    useGetDepartment();
  const { data: designationData, isSuccess: designationSuccess } =
    useGetDesignation();
  const { data: shiftData, isSuccess: shiftSuccess } = useGetShiftType();

  return (
    <Card>
      <CardContent className="grid grid-cols-2 gap-4">
        <FormField
          name="joinDate"
          label="Joining Date"
          type="date"
          placeholder="Select Joining Date"
          formik={formik}
          required
        />
        <FormField
          name="department"
          label="Department"
          type="dropdown"
          placeholder="Select Department"
          options={
            departmentSuccess
              ? departmentData?.map((item) => ({
                  label: item.name,
                  value: item._id ?? "",
                }))
              : []
          }
          formik={formik}
          required
        />
        <FormField
          name="designation"
          label="Designation"
          type="dropdown"
          placeholder="Select Designation"
          options={
            designationSuccess
              ? designationData?.map((item) => ({
                  label: item.name,
                  value: item._id ?? "",
                }))
              : []
          }
          formik={formik}
          required
        />
        <FormField
          name="shiftType"
          label="Shift Type"
          type="dropdown"
          placeholder="Select Shift Type"
          options={
            shiftSuccess
              ? shiftData?.map((item) => ({
                  label: item.name,
                  value: item._id ?? "",
                }))
              : []
          }
          formik={formik}
          required
        />
      </CardContent>
    </Card>
  );
};

const SalaryDetails = ({ formik }: { formik: any }) => {
  const { data: allowanceData, isSuccess: allowanceSuccess } =
    useGetAllowance();
  const { data: deductionData, isSuccess: deductionSuccess } =
    useGetDeduction();

  return (
    <Card className="">
      <CardContent>
        <div className="mb-4">
          <label className="flex items-center gap-2">
            <input
              type="checkbox"
              name="applyTDS"
              checked={formik.values.applyTDS}
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
              className="w-4 h-4"
            />
            TDS Applicable
          </label>
        </div>
        <div className="flex flex-col gap-3">
          <div className="grid grid-cols-3 gap-4">
            <FormField
              name="basicSalary"
              label="Basic Salary"
              type="number"
              placeholder="Enter Basic Salary"
              formik={formik}
              required
            />
            <FormField
              name="annualSalary"
              label="Annual Salary"
              type="number"
              placeholder="Enter Annual Salary"
              formik={formik}
              required
            />
            <FormField
              name="festivalAllowanceMonth"
              label="Festival Allowance Month"
              type="text"
              placeholder="Enter Festival Allowance Month"
              formik={formik}
              required
            />
            <FormField
              name="overTimePerHr"
              label="Overtime Per Hour"
              type="number"
              placeholder="Enter Overtime Rate"
              formik={formik}
              required
            />
            <FormField
              name="paidLeave"
              label="Paid Leave (Days)"
              type="number"
              placeholder="Enter Paid Leave Days"
              formik={formik}
              required
            />
          </div>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <FieldArray name="deduction">
                {({ push, remove }) => (
                  <div className="space-y-2">
                    {formik.values.deduction.map((_: any, index: number) => (
                      <div key={index} className="flex flex-col">
                        <div className="flex justify-between items-center gap-2">
                          <FormField
                            name={`deduction.${index}.name`}
                            label="Deduction"
                            type="dropdown"
                            placeholder="Select Deduction"
                            options={
                              deductionSuccess
                                ? deductionData.map((item: any) => ({
                                    label: item.name,
                                    value: item._id ?? "",
                                  }))
                                : []
                            }
                            formik={formik}
                            required
                          />
                          {index > 0 && (
                            <button
                              type="button"
                              onClick={() => remove(index)}
                              className="p-[3px] text-white bg-red mt-5 rounded-full"
                            >
                              <Icon
                                icon="fluent-mdl2:calculator-multiply"
                                width="12"
                                height="12"
                              />
                            </button>
                          )}
                        </div>
                      </div>
                    ))}
                    <button
                      type="button"
                      onClick={() => push({ name: "" })}
                      className="py-2 px-4 mt-2 text-white rounded-md bg-[#163381]"
                    >
                      Add Extra Deduction
                    </button>
                    {formik.touched.deduction && formik.errors.deduction && (
                      <span className="text-sm text-red-500">
                        {typeof formik.errors.deduction === "string"
                          ? formik.errors.deduction
                          : Array.isArray(formik.errors.deduction)
                          ? formik.errors.deduction
                              .map((err: any, i: number) =>
                                err && err.name
                                  ? `Deduction ${i + 1}: ${err.name}`
                                  : null
                              )
                              .filter(Boolean)
                              .join(", ")
                          : "Invalid deduction values"}
                      </span>
                    )}
                  </div>
                )}
              </FieldArray>
            </div>
            <div>
              <FieldArray name="allowance">
                {({ push, remove }) => (
                  <div className="space-y-2">
                    {formik.values.allowance.map((_: any, index: number) => (
                      <div key={index} className="flex flex-col">
                        <div className="flex items-center justify-between gap-2">
                          <FormField
                            name={`allowance.${index}.name`}
                            label="Allowance"
                            type="dropdown"
                            placeholder="Select Allowance"
                            options={
                              allowanceSuccess
                                ? allowanceData.map((item: any) => ({
                                    label: item.name,
                                    value: item._id ?? "",
                                  }))
                                : []
                            }
                            formik={formik}
                            required
                          />
                          {index > 0 && (
                            <button
                              type="button"
                              onClick={() => remove(index)}
                              className="p-[3px] text-white bg-red mt-5 rounded-full"
                            >
                              <Icon
                                icon="fluent-mdl2:calculator-multiply"
                                width="12"
                                height="12"
                              />
                            </button>
                          )}
                        </div>
                      </div>
                    ))}
                    <button
                      type="button"
                      onClick={() => push({ name: "" })}
                      className="py-2 px-4 mt-2 text-white rounded-md bg-[#163381]"
                    >
                      Add Allowance
                    </button>
                    {formik.touched.allowance && formik.errors.allowance && (
                      <span className="text-sm text-red-500">
                        {typeof formik.errors.allowance === "string"
                          ? formik.errors.allowance
                          : Array.isArray(formik.errors.allowance)
                          ? formik.errors.allowance
                              .map((err: any, i: number) =>
                                err && err.name
                                  ? `Allowance ${i + 1}: ${err.name}`
                                  : null
                              )
                              .filter(Boolean)
                              .join(", ")
                          : "Invalid allowance values"}
                      </span>
                    )}
                  </div>
                )}
              </FieldArray>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

const DocumentDetails = ({ formik }: { formik: any }) => {
  const documentTypes = [
    { name: "photo", label: "Photo", multiple: true },
    {
      name: "citizenship",
      label: "Citizenship",
      multiple: true,
    },
    { name: "license", label: "License", required: false, multiple: true },
    { name: "resume", label: "Resume", required: false, multiple: true },
  ];

  const handleFileChange = (
    e: React.ChangeEvent<HTMLInputElement>,
    fieldName: string
  ) => {
    if (e.target.files && e.target.files.length > 0) {
      const files = Array.from(e.target.files);
      const documentType = documentTypes.find((doc) => doc.name === fieldName);
      const isMultiple = documentType?.multiple ?? false;

      if (isMultiple) {
        const existingFiles = Array.isArray(formik.values[fieldName])
          ? formik.values[fieldName]
          : [];
        formik.setFieldValue(fieldName, [...existingFiles, ...files]);
        console.log(`Updated ${fieldName}:`, [...existingFiles, ...files]);
      } else {
        formik.setFieldValue(fieldName, files.slice(0, 1));
        console.log(`Updated ${fieldName}:`, files.slice(0, 1));
      }
      formik.setFieldTouched(fieldName, true);
    }
  };

  const isImage = (file: File | string) =>
    typeof file === "string"
      ? file.match(/\.(jpg|jpeg|png|gif)$/i)
      : file.type.startsWith("image/");

  return (
    <div className="grid grid-cols-2 gap-3">
      {documentTypes.map((item, index) => (
        <Card key={index}>
          <CardContent className="flex flex-col gap-1">
            <label className=" text-sm">
              {item.label}{" "}
              {item.required && <span className="text-red-600">*</span>}
            </label>
            <div className="flex items-center w-full space-x-2 border-2 rounded-md">
              <label
                htmlFor={item.name}
                className="flex items-center gap-1 p-2 text-white bg-[#163381] cursor-pointer rounded-l-md"
              >
                <Icon icon="material-symbols:upload" width="18" height="18" />
                Upload {item.label}
              </label>
              <input
                name={item.name}
                id={item.name}
                className="hidden"
                type="file"
                multiple={item.multiple || true}
                accept={item.name === "photo" ? "image/*" : undefined}
                onChange={(e) => {
                  handleFileChange(e, item.name);
                  e.target.value = "";
                }}
                onBlur={() => formik.setFieldTouched(item.name, true)}
              />
              <span className="text-sm text-gray-500">
                {Array.isArray(formik.values[item.name])
                  ? formik.values[item.name].length
                  : formik.values[item.name]
                  ? 1
                  : 0}{" "}
                files selected
              </span>
            </div>
            {formik.touched[item.name] && formik.errors[item.name] && (
              <span className="text-sm text-red-500">
                {String(formik.errors[item.name])}
              </span>
            )}
            {formik.values[item.name]?.length > 0 && (
              <div className="flex flex-wrap p-2 mt-2 border-2 border-gray-300 rounded-md">
                {(Array.isArray(formik.values[item.name])
                  ? formik.values[item.name]
                  : [formik.values[item.name]]
                ).map((file: File | string, idx: number) => {
                  const isString = typeof file === "string";
                  const imageUrl = isString
                    ? `${IMAGE_URL}${file}`
                    : URL.createObjectURL(file);

                  if (!imageUrl && isImage(file)) {
                    console.warn(
                      `Invalid file in ${item.name} at index ${idx}:`,
                      file
                    );
                    return null;
                  }

                  return (
                    <div key={idx} className="relative w-24 h-24 m-2">
                      {isImage(file) ? (
                        <img
                          src={imageUrl}
                          alt={`preview-${idx}`}
                          className="object-cover w-full h-full rounded-md"
                        />
                      ) : (
                        <div className="flex items-center justify-center w-full h-full bg-gray-100 rounded-md">
                          <span className="text-sm text-gray-500">
                            {typeof file === "string"
                              ? file.split("/").pop()
                              : file.name}
                          </span>
                        </div>
                      )}
                      <button
                        type="button"
                        className="absolute top-0 right-0 p-1 bg-red-500 rounded-full"
                        onClick={() => {
                          const newFiles = Array.isArray(
                            formik.values[item.name]
                          )
                            ? formik.values[item.name].filter(
                                (_: any, i: number) => i !== idx
                              )
                            : [];
                          formik.setFieldValue(item.name, newFiles);
                          formik.setFieldTouched(item.name, true);
                        }}
                      >
                        <Icon
                          icon="fluent-mdl2:calculator-multiply"
                          width="16"
                          height="15"
                          className="text-red bg-white rounded-full p-[2px] border"
                        />
                      </button>
                    </div>
                  );
                })}
              </div>
            )}
          </CardContent>
        </Card>
      ))}
    </div>
  );
};

export default EmployeeForm;
