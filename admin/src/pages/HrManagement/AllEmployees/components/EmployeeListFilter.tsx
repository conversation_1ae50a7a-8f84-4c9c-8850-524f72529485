import React, { useState } from "react";
import CustomSelect from "../../../../components/GlobalForm/CustomSelect";

interface FilterOption {
  label: string;
  value: string;
}

interface EmployeeListFilterProps {
  onSearch: (value: string) => void;
  onRoleChange: (value: string) => void;
  roleOptions?: FilterOption[];
}

export default function EmployeeListFilter({
  onSearch,
  onRoleChange,
  roleOptions = [
    { label: "All", value: "all" },
    { label: "Staff", value: "staff" },
    { label: "HouseKeeper", value: "housekeeper" },
  ],
}: EmployeeListFilterProps) {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedRole, setSelectedRole] = useState("all");

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchTerm(value);
    onSearch(value);
  };

  const handleApplyFilters = () => {
    onRoleChange(selectedRole);
  };

  const handleResetFilters = () => {
    setSearchTerm("");
    setSelectedRole("all");
    onSearch("");
    onRoleChange("all");
  };

  return (
    <div className="flex items-center gap-4">
      {/* Filter Row */}
      <div className="flex flex-col md:flex-row gap-4 items-end">
        {/* Role Dropdown */}
        <div className="w-full md:w-48">
          <CustomSelect
            label="Select Role"
            value={selectedRole}
            options={roleOptions}
            onChange={(value) => setSelectedRole(value)}
            className="w-full md:w-48"
            isForm={false}
          />
        </div>

        {/* Buttons */}
        <div className="flex gap-2">
          <button
            onClick={handleResetFilters}
            className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-100 focus:outline-none"
          >
            Reset
          </button>
          <button
            onClick={handleApplyFilters}
            className="px-4 py-2 bg-[#2A3A6D] text-white rounded-md focus:outline-none"
          >
            Apply Filters
          </button>
        </div>
      </div>

      {/* Search Bar */}
      <div className="flex-1 flex justify-end">
        <div className="relative mt-6">
          <input
            type="text"
            placeholder="Search..."
            value={searchTerm}
            onChange={handleSearchChange}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 pl-10"
          />
          <svg
            className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-500"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="2"
              d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
            />
          </svg>
        </div>
      </div>
    </div>
  );
}
