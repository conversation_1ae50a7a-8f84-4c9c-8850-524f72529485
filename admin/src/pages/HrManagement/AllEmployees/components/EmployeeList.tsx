import { Icon } from "@iconify/react/dist/iconify.js";
import { useEffect, useMemo, useState } from "react";
import { useGetAllUser } from "../../../../server-action/API/user.tsx";
import EmployeeDetails from "./EmployeeDetails";
import { IMAGE_URL } from "../../../../constant/constant.ts";

type Employee = {
  _id: string;
  name: string;
  avatar?: string;
  role: string;
  department?: { name: string };
  designation?: {
    _id: string;
    name: string;
  };
  photo?: string[];
  phoneNumber?: string;
  email?: string;
  joinDate?: string;
  basicSalary?: number;
  isSuperAdmin?: boolean;
};

interface EmployeeListProps {
  searchQuery?: string;
  selectedRole?: string;
  selectedDesignation?: string;
}

const EmployeeList = ({
  searchQuery = "",
  selectedRole = "all",
  selectedDesignation = "all",
}: EmployeeListProps) => {
  const [selectedEmployee, setSelectedEmployee] = useState<Employee | null>(
    null
  );
  const {
    data: employeesData,
    isSuccess,
    isLoading,
    error,
  } = useGetAllUser({
    role_ne: "guest",
  });

  console.log(employeesData, "emp");

  const employees: Employee[] = useMemo(() => {
    if (!isSuccess || !employeesData) return [];

    let filteredEmployees = employeesData || [];

    // Filter out employees with roles "admin", "superAdmin", and "guest"
    filteredEmployees = filteredEmployees.filter(
      (emp: Employee) =>
        emp.role &&
        !["admin", "superadmin", "guest"].includes(emp.role.toLowerCase())
    );

    // Filter by search query if provided
    if (searchQuery.trim()) {
      filteredEmployees = filteredEmployees.filter(
        (emp: Employee) =>
          emp.name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
          emp.role?.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    // Filter by selected role if not "all"
    if (selectedRole !== "all") {
      filteredEmployees = filteredEmployees.filter(
        (emp: Employee) =>
          emp.role?.toLowerCase() === selectedRole.toLowerCase()
      );
    }

    if (selectedDesignation !== "all") {
      filteredEmployees ===
        filteredEmployees?.filter(
          (emp: Employee) => emp?.designation?.name === selectedDesignation
        );
    }

    console.log("Filtered employees:", filteredEmployees);
    return filteredEmployees;
  }, [
    employeesData,
    isSuccess,
    searchQuery,
    selectedRole,
    selectedDesignation,
  ]);

  // Select the first employee from the filtered list
  useEffect(() => {
    console.log("useEffect triggered:", {
      isSuccess,
      employeesLength: employees.length,
      selectedEmployee,
      firstEmployee: employees[0],
    });
    if (isSuccess && employees.length > 0 && !selectedEmployee) {
      console.log("Selecting first employee:", employees[0]);
      setSelectedEmployee(employees[0]);
    } else if (isSuccess && employees.length === 0) {
      setSelectedEmployee(null); // Clear selection if no employees match
    }
  }, [employees, isSuccess, selectedEmployee]);

  const roles = useMemo(() => {
    // Create a Set of unique roles, excluding "admin", "superAdmin", and "guest"
    const rolesSet = new Set<string>(
      employees
        .map((employee) => employee.role)
        .filter(
          (role) =>
            role &&
            !["admin", "superadmin", "guest"].includes(role.toLowerCase())
        )
    );

    const roleArray = Array.from(rolesSet);
    console.log("Roles:", roleArray);
    return roleArray;
  }, [employees]);

  return (
    <div className="flex gap-2">
      <div className="w-1/4 py-2 bg-white rounded shadow">
        <div className="flex items-center justify-between p-2 font-semibold">
          <div className="flex items-center gap-2">
            <span className="text-base">All Employees</span>
            {employees.length > 0 && (
              <button className="px-2 py-1 text-xs text-gray-700 border border-gray-400 rounded-3xl">
                {employees.length}
              </button>
            )}
          </div>

          {(searchQuery.trim() || selectedRole !== "all") && (
            <div className="flex gap-2">
              {searchQuery.trim() && (
                <div className="text-xs px-2 py-1 bg-blue-100 text-blue-800 rounded-md">
                  Search: "{searchQuery}"
                </div>
              )}
              {selectedRole !== "all" && (
                <div className="text-[10px] px-2 capitalize py-1 bg-blue-100 text-blue-800 rounded-sm">
                  Role: {selectedRole}
                </div>
              )}
            </div>
          )}
        </div>

        <div
          className="clear-both max-h-[calc(150vh-200px)] overflow-y-auto"
          style={{
            scrollbarWidth: "none", // Firefox
            msOverflowStyle: "none", // IE and Edge
          }}
        >
          <style>
            {`
              .clear-both::-webkit-scrollbar {
                display: none;
              }
            `}
          </style>
          {isLoading ? (
            <div className="p-4 text-center text-gray-600">Loading...</div>
          ) : error ? (
            <div className="p-4 text-center text-red-600">
              Error loading employees: {error.message}
            </div>
          ) : employees.length > 0 ? (
            roles.map((role) => (
              <div key={role}>
                <div className="px-2 py-3 mb-2 text-xs font-bold bg-[#F1F6FD] uppercase text-[#4960A8]">
                  {role}
                </div>
                {employees
                  .filter((emp) => emp.role === role)
                  .map((employee) => (
                    <div
                      key={employee._id}
                      className={`flex items-center p-2 px-3 mb-2 cursor-pointer gap-x-2 hover:bg-gray-100 ${
                        selectedEmployee?._id === employee._id
                          ? "bg-blue-50 border-l-4 border-[#4960A8]"
                          : ""
                      }`}
                      onClick={() => {
                        console.log("Selected employee:", employee);
                        setSelectedEmployee(employee);
                      }}
                    >
                      <div className="flex items-center gap-x-2 w-full">
                        {employee?.photo && employee?.photo?.length > 0 ? (
                          <img
                            src={`${IMAGE_URL}/${employee?.photo[0]}`}
                            alt={employee.name}
                            className="w-8 h-8 rounded-full object-cover"
                          />
                        ) : (
                          <span className="flex items-center justify-center w-8 h-8 bg-gray-200 rounded-full">
                            <Icon
                              icon="duo-icons:user"
                              width="20"
                              height="20"
                            />
                          </span>
                        )}
                        <div className="flex-1">
                          <div className="text-sm font-medium capitalize">
                            {employee.name}
                          </div>
                          <div className="text-xs text-gray-500 uppercase">
                            {employee.role || "No role assigned"}
                          </div>
                        </div>
                        {selectedEmployee?._id === employee._id && (
                          <Icon
                            icon="mdi:check"
                            className="text-blue-500"
                            width="18"
                            height="18"
                          />
                        )}
                      </div>
                    </div>
                  ))}
              </div>
            ))
          ) : (
            <div className="p-4 text-center text-gray-500">
              No employees found
            </div>
          )}
        </div>
      </div>
      <EmployeeDetails selectedEmployee={selectedEmployee} />
    </div>
  );
};

export default EmployeeList;
