import * as Yup from "yup";
export const CreditFormSchema = Yup.object().shape({
  user: Yup.string().required("User is required"),
  date: Yup.date().required("Date is required"),
  creditAmount: Yup.number()
    .required("Credit Amount is required")
    .positive("Credit Amount must be positive"),
  repaymentStartDate: Yup.date().required("Repayment Start Date is required"),
  installmentAmt: Yup.number()
    .required("Installment Amount is required")

    .positive("Installment Amount must be positive"),
  description: Yup.string().required("Description is required"),
  reason: Yup.string().required("Reason is required"),
  creditRepaymentMethod: Yup.string().required("Repayment Method is required"),
  creditRepaymentFrequency: Yup.string().required(
    "Repayment Frequency is required"
  ),
  // paidAmount: Yup.number()
  //   .required("Paid Amount is required")
  //   .positive("Paid Amount must be positive"),

  status: Yup.string().required("Status is required"),
  // Add any other fields you want to validate
  // For example:
  // hotel: Yup.string().required("Hotel is required"),
  // date: Yup.date().required("Date is required"),
});

export const CreditPaymentFormvalidationSchema = Yup.object().shape({
  transaction: Yup.array()
    .of(
      Yup.object().shape({
        date: Yup.string().required("Date is required"),
        paidAmount: Yup.number()
          .required("Amount is required")
          .min(1, "Must be greater than 0"),
      })
    )
    .required(),
});
