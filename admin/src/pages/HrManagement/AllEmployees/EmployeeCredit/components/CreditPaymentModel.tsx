import { Form, FormikProvider, useFormik } from "formik";
import {
  ICredit,
  useUpdateCredit,
} from "../../../../../server-action/API/employeecredit";
import { CreditPaymentFormvalidationSchema } from "./CreditValidatonSchema";
import HeadingPopup from "../../../../GuestManagement/components/HeadingPopup";
import { GlobalForm } from "../../../../../components/GlobalForm/GlobalFormComponent";
import { ActionButton } from "../../../../../components/ActionButton";

interface ICreditPaymentPopupProps {
  onClose: () => void;
  data: ICredit | null;
}

export const getCreditPaymentFormData = () => [
  {
    field: "transaction[0].date",
    label: "Date of Payment",
    type: "date",
    placeholder: "Select Date",
    required: true,
  },
  {
    field: "transaction[0].paidAmount",
    label: "Paid Amount",
    type: "number",
    placeholder: "Enter Amount",
    required: true,
  },
];

const CreditPaymentModel = ({ onClose, data }: ICreditPaymentPopupProps) => {
  const { mutateAsync: updateCredit } = useUpdateCredit();

  const getRemainingBalance = () => {
    if (!data) return 0;
    return data.creditAmount - (data?.paidAmount ?? 0);
  };

  const formik = useFormik<ICredit>({
    initialValues: {
      hotel: data?.hotel || "67c958b0b8e5f91a731b59ac",
      creditAmount: data?.creditAmount || 0,
      paidAmount: data?.paidAmount ?? 0,
      transaction: [
        {
          paidAmount: 0,
          date: "",
        },
      ],
    },
    enableReinitialize: true,
    validationSchema: CreditPaymentFormvalidationSchema,
    onSubmit: async (values) => {
      try {
        if (data && data._id) {
          const newPaid = values.transaction?.[0]?.paidAmount || 0;
          const currentPaid = data.paidAmount || 0;
          const totalPaid = currentPaid + newPaid;
          const creditAmount = data.creditAmount;

          let newStatus = data.status ?? "unpaid";
          if (totalPaid >= creditAmount) {
            newStatus = "paid";
          } else if (totalPaid > 0) {
            newStatus = "unpaid";
          } else {
            newStatus = "paid";
          }

          const updatedCredit: ICredit = {
            ...data,
            transaction: [
              ...(data.transaction ?? []),
              ...(values.transaction ?? []),
            ],
            paidAmount: totalPaid,
            status: newStatus,
          };

          await updateCredit({ creditData: updatedCredit, _id: data._id });
        }
        onClose();
      } catch (error) {
        console.error("Failed to submit credit payment form", error);
      }
    },
  });

  const { handleSubmit, getFieldProps } = formik;
  const formDetails = getCreditPaymentFormData();

  return (
    <HeadingPopup
      onClose={onClose}
      heading="Credit Payment"
      className="w-[40vw] h-auto"
    >
      <div className="space-y-4">
        {/* Employee Info */}
        <div className="border rounded-md p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-600 text-sm">Employee Details</p>
              <p className="text-sm text-gray-800 mt-1">Name</p>
              <p className="font-medium">{data?.user?.name}</p>
            </div>
            <div className="text-right">
              <p className="text-gray-600">Remaining Balance</p>
              <p className="font-medium">Rs {getRemainingBalance()}</p>
            </div>
          </div>
        </div>

        <FormikProvider value={formik}>
          <Form onSubmit={handleSubmit} className="grid grid-cols-3 gap-4">
            <GlobalForm
              formDatails={formDetails}
              getFieldProps={getFieldProps}
            />
            <div className="col-span-3">
              <ActionButton onCancel={onClose} onSubmit={handleSubmit} />
            </div>
          </Form>
        </FormikProvider>
      </div>
    </HeadingPopup>
  );
};

export default CreditPaymentModel;
