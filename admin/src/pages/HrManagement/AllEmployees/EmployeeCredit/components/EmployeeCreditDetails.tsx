import { Status } from "../../../../../components/Status";
import MasterTable from "../../../../../layouts/Table/MasterTable";
import HeadingPopup from "../../../../GuestManagement/components/HeadingPopup";

const EmployeeCreditDetails = ({ data, onClose }: any) => {
  if (!data) return null;
  const formatDate = (date: string) => {
    if (!date) return "N/A";
    const d = new Date(date);
    return new Intl.DateTimeFormat("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    }).format(d);
  };

  const formattedDate = data.date ? formatDate(data.date) : "N/A";
  const formattedRepaymentDate = data.repaymentStartDate
    ? formatDate(data.repaymentStartDate)
    : "N/A";

  const tableData = {
    columns: [
      { title: "SN", key: "sn" },
      { title: "Date", key: "date" },
      { title: "Paid Amount ", key: "paidAmount" },
    ],
    rows:
      data.transaction &&
      data.transaction.map((transaction: any, index: any) => ({
        sn: index + 1,
        date: transaction?.date
          ? new Date(transaction?.date)?.toLocaleDateString("en-GB")
          : "N/A",
        paidAmount: transaction?.paidAmount,
      })),
  };
  return (
    <HeadingPopup
      heading="Credit Details"
      onClose={onClose}
      className="w-[60vw] h-[80vh] overflow-y-auto"
    >
      <div className="space-y-2 p-2">
        {/* Main details section */}

        {/* Employee & Hotel Info */}
        <div className="grid grid-cols-2 gap-4 border rounded-lg p-4">
          <div className="flex flex-col">
            <span className="text-sm text-gray-500">Employee:</span>
            <div className="flex items-center gap-2 font-medium">
              {data.user?.name || "N/A"}
            </div>
          </div>

          <div className="flex flex-col">
            <span className="text-sm text-gray-500">Department:</span>
            <div className="flex items-center gap-2 font-medium">
              {data.user?.department?.name || "N/A"}
            </div>
          </div>

          <div className="flex flex-col">
            <span className="text-sm text-gray-500">Designation:</span>
            <div className="flex items-center gap-2 font-medium">
              {data.user?.designation?.name || "N/A"}
            </div>
          </div>

          <div className="flex flex-col">
            <span className="text-sm text-gray-500">Description:</span>
            <div className="flex items-center gap-2 font-medium">
              {data.description || "No description provided."}
            </div>
          </div>
        </div>

        {/* Dates & Status */}
        <div className="grid grid-cols-2 gap-4 border rounded-lg p-4">
          <div className="flex flex-col">
            <span className="text-sm text-gray-500">Date:</span>
            <div className="flex items-center gap-2 font-medium">
              {formattedDate}
            </div>
          </div>

          <div className="flex flex-col">
            <span className="text-sm text-gray-500">Repayment Start Date:</span>
            <div className="flex items-center gap-2 font-medium">
              {formattedRepaymentDate}
            </div>
          </div>

          <div className="flex flex-col">
            <span className="text-sm text-gray-500">Status:</span>
            <div className="flex items-center gap-2">
              <Status status={data?.status} />
            </div>
          </div>

          <div className="flex flex-col">
            <span className="text-sm text-gray-500">Reason:</span>
            <div className="flex items-center gap-2 font-medium">
              {data.reason
                ? data.reason.charAt(0).toUpperCase() + data.reason.slice(1)
                : "N/A"}
            </div>
          </div>
        </div>

        {/* Payment Details */}
        <div className="grid grid-cols-2 gap-4 border rounded-lg p-4">
          <div className="flex flex-col">
            <span className="text-sm text-gray-500">Repayment Method:</span>
            <div className="flex items-center gap-2 font-medium">
              {typeof data.creditRepaymentMethod === "string"
                ? data.creditRepaymentMethod
                : data.creditRepaymentMethod?.name || "N/A"}
            </div>
          </div>

          <div className="flex flex-col">
            <span className="text-sm text-gray-500">Repayment Frequency:</span>
            <div className="flex items-center gap-2 font-medium">
              {typeof data.creditRepaymentFrequency === "string"
                ? data.creditRepaymentFrequency
                : data.creditRepaymentFrequency?.name || "N/A"}
            </div>
          </div>
        </div>

        <MasterTable
          rows={tableData.rows}
          columns={tableData.columns}
          loading={false}
        />
      </div>
    </HeadingPopup>
  );
};

export default EmployeeCreditDetails;
