export const getCreditFormData = (
  methods: any[],
  frequencies: any[],
  employees: any[]
) => [
  {
    field: "user",
    label: "Employee",
    type: "select",
    placeholder: "Select Employee",
    options: employees
      .filter((user) => user.role === "staff" || user.role === "housekeeper")
      .map((user) => ({
        value: user._id,
        label: `${user.name} (${user.phoneNumber})`,
      })),
  },

  {
    field: "creditAmount",
    label: "Credit Amount",
    type: "number",
    placeholder: "Enter Credit Amount",
  },
  {
    field: "date",
    label: "Date of Credit",
    type: "date",
    placeholder: "Select Date",
  },

  {
    field: "reason",
    label: "Reason for Credit",
    type: "text",
    placeholder: "Reason",
  },

  {
    field: "repaymentStartDate",
    label: "Repayment StartDate",
    type: "date",
    placeholder: "Select Start Date",
  },
  {
    field: "installmentAmt",
    label: "Installment Amount",
    type: "number",
    placeholder: "RS 0",
  },
  {
    field: "creditRepaymentMethod",
    label: "Repayment Method",
    type: "select",
    placeholder: "Select Method",
    options: methods?.map((m) => ({ label: m?.name, value: m?._id })),
  },
  {
    field: "paidAmount",
    label: "Paid Amount",
    type: "number",
    placeholder: "Enter paid amount",
  },
  {
    field: "status",
    label: "Status",
    type: "select",
    placeholder: "Select Status",
    options: [
      { value: "paid", label: "Paid" },
      { value: "unpaid", label: "UnPaid" },
      { value: "expired", label: "Expired" },
    ],
  },
  {
    field: "creditRepaymentFrequency",
    label: "Repayment Frequency",
    type: "select",
    placeholder: "Select Frequency",
    options: frequencies?.map((f) => ({ label: f.name, value: f._id })),
  },

  {
    field: "description",
    label: "Description",
    type: "textarea",
    placeholder: "Enter description",
  },
];
