import { useMemo, useState } from "react";
import MasterTable from "../../../../layouts/Table/MasterTable";
import {
  useDeleteCredit,
  useGetCredit,
} from "../../../../server-action/API/employeecredit";
import { TableAction } from "../../../../layouts/Table/TableAction";
import { Status } from "../../../../components/Status";
import CreditForm from "./components/CreditForm";
import CreditPaymentModel from "./components/CreditPaymentModel";
import EmployeeCreditDetails from "./components/EmployeeCreditDetails";
import { DateForamter } from "../../../../components/DateFormater";

const EmployeeCreditIndex = () => {
  const { data: allEmployeeCredit = [], isLoading } = useGetCredit();
  const { mutate: deleteCredit } = useDeleteCredit();

  const [editData, setEditData] = useState<any>(null);
  const [showCreditForm, setShowCreditForm] = useState(false);

  const [selectedCreditForPayment, setSelectedCreditForPayment] =
    useState<any>(null);
  const [showPaymentForm, setShowPaymentForm] = useState(false);
  const [showCreditDetails, setShowCreditDetails] = useState(false);

  const columns = useMemo(
    () => [
      { key: "sn", title: "SN" },
      { key: "name", title: "Name" },
      { key: "designation", title: "Designation" },
      { key: "creditAmount", title: "Credit Amount" },
      { key: "paidAmount", title: "Paid Amount" },
      { key: "repaymentStartDate", title: "Repayment Start Date" },
      // { key: "installmentAmt", title: "Installment Amount" },
      // { key: "creditRepaymentMethod", title: "Credit Repayment Method" },
      { key: "status", title: "Status" },
      { key: "action", title: "Action" },
    ],
    []
  );

  const rows = useMemo(() => {
    return allEmployeeCredit.map((credit, index) => ({
      sn: index + 1,
      name: credit.user?.name || "N/A",
      designation: credit.user?.designation || "N/A",
      creditAmount: `Rs. ${credit.creditAmount}`,
      installmentAmt: credit?.installmentAmt,
      paidAmount: `Rs. ${credit?.paidAmount}`,
      creditRepaymentMethod: credit?.creditRepaymentMethod || "N/A",
      repaymentStartDate: credit.repaymentStartDate
        ? DateForamter(credit.repaymentStartDate)
        : "N/A",
      status: <Status status={credit.status ?? "N/A"} />,
      action: (
        <TableAction
          onShow={() => {
            setSelectedCreditForPayment(credit);
            setShowCreditDetails(true);
            setShowPaymentForm(false);
          }}
          onEdit={() => {
            setEditData(credit);
            setShowCreditForm(true);
          }}
          onDelete={() => {
            if (credit._id) {
              deleteCredit(credit._id);
            }
          }}
          onPay={() => {
            if (credit.status !== "paid") {
              setSelectedCreditForPayment(credit);
              setShowPaymentForm(true);
              setShowCreditDetails(false); // Ensure details modal is not shown
            }
          }}
          payDisabled={credit.status === "paid"}
        />
      ),
    }));
  }, [allEmployeeCredit, deleteCredit]);

  return (
    <div>
      <MasterTable columns={columns} rows={rows} loading={isLoading} />

      {/* Credit Form Modal */}
      {showCreditForm && (
        <CreditForm
          editData={editData}
          close={() => {
            setShowCreditForm(false);
            setEditData(null);
          }}
        />
      )}

      {/* Payment Modal */}
      {showPaymentForm && selectedCreditForPayment && (
        <CreditPaymentModel
          data={selectedCreditForPayment}
          onClose={() => {
            setShowPaymentForm(false);
            setSelectedCreditForPayment(null);
          }}
        />
      )}

      {/* Employee Credit Details Modal */}
      {showCreditDetails && selectedCreditForPayment && (
        <EmployeeCreditDetails
          data={selectedCreditForPayment}
          onClose={() => {
            setShowCreditDetails(false);
            setSelectedCreditForPayment(null);
          }}
        />
      )}
    </div>
  );
};

export default EmployeeCreditIndex;
