import { useEffect, useMemo, useState } from "react";
import { CustomTabs } from "../../../components/CustomTab";
import Header from "../../../components/Header";
import EmployeeList from "./components/EmployeeList";
import EmployeeForm from "./components/EmployeeForm";
import { CardContent } from "../../../components/Card";
import EmployeeCreditIndex from "./EmployeeCredit/EmployeeCreditIndex";
import CreditForm from "./EmployeeCredit/components/CreditForm";
import EmployeeListFilter from "./components/EmployeeListFilter";

const EmployeeManagementIndex = () => {
  const [tab, setTab] = useState("Employee List");
  const [openForm, setOpenForm] = useState(false);
  const [formType, setFormType] = useState<"employee" | "credit">("employee");
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedRole, setSelectedRole] = useState("all");
  const [selectedDesignation, setSelectedDesignation] = useState("all");

  const tabOptions = useMemo(() => ["Employee List", "Employee Credit"], []);

  useEffect(() => {
    if (tab === "Employee Credit") {
      setFormType("credit");
      setOpenForm(false);
      setSearchQuery("");
      setSelectedRole("all");
      setSelectedDesignation("all");
    } else {
      setFormType("employee");
      setOpenForm(false);
    }
  }, [tab]);

  const handleAddClick = () => {
    if (tab === "Employee Credit") {
      setFormType("credit");
    } else {
      setFormType("employee");
    }
    setOpenForm(true);
  };

  return (
    <div className="flex flex-col min-h-screen bg-gray-100">
      {/* Header */}
      <Header
        title={tab === "Employee Credit" ? "Credit" : "Employee"}
        onAddClick={handleAddClick}
      />

      {/* Tabs */}
      <CardContent className="flex items-center border py-3 mb-2 bg-white rounded-md">
        <CustomTabs tabs={tabOptions} defaultTab={tab} onTabChange={setTab} />
      </CardContent>

      {/* Filter Section - show only for Employee List tab */}
      {tab === "Employee List" && (
        <div className="p-4 border bg-white rounded-md mb-2">
          <EmployeeListFilter
            onSearch={setSearchQuery}
            onRoleChange={setSelectedRole}
          />
        </div>
      )}

      {/* Tab Content */}
      {tab === "Employee List" && (
        <EmployeeList searchQuery={searchQuery} selectedRole={selectedRole} />
      )}
      {tab === "Employee Credit" && <EmployeeCreditIndex />}

      {/* Conditional Forms */}
      {openForm && formType === "employee" && (
        <EmployeeForm onClose={() => setOpenForm(false)} editData={null} />
      )}
      {openForm && formType === "credit" && (
        <CreditForm close={() => setOpenForm(false)} />
      )}
    </div>
  );
};

export default EmployeeManagementIndex;
