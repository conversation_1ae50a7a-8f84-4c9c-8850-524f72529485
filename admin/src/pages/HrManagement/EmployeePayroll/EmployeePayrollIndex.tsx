/*eslint-disable @typescript-eslint/no-explicit-any */
import { useCallback, useState } from "react";
import Header from "../../../components/Header";
import MasterTable from "../../../layouts/Table/MasterTable";
import { DeleteDialog, PopupModal } from "../../../components";
import EmployeePayrollForm from "./Components/EmployeePayrollForm";
import {
  useDeleteSalary,
  useGenerateSalary,
  useGetSalaries,
} from "../../../server-action/API/employeepayroll";
import { Icon } from "@iconify/react/dist/iconify.js";
import { toast } from "react-toastify";
import SalaryInvoiceDetail from "./Components/SalaryInvoiceDetail";
import { ISalary } from "../../../Interface/salary.interface";
import { PayrollTableAction } from "./Components/PayrollTableAction";
import EmployeePayrollFilter from "./Components/EmployeePayrollFilter";

const EmployeePayrollIndex = () => {
  const [showPopup, setShowPopup] = useState(false);
  const [page, setPage] = useState(1); // Current page
  const [limit, setLimit] = useState(6);
  const [generateSalaryPopup, setGenerateSalaryPopup] = useState(false);
  const [editPopup, setEditPopup] = useState(false);
  const [deletePopup, setDeletePopup] = useState(false);
  const [editData, setEditData] = useState<ISalary | null>(null);
  const [selectedSalary, setSelectedSalary] = useState<ISalary | null>(null);
  const [salaryToDelete, setSalaryToDelete] = useState<string | null>(null);
  const [month, setMonth] = useState<string | "">("");
  const [year, setYear] = useState(new Date().getFullYear());
  // Filter states
  const [searchTerm, setSearchTerm] = useState("");
  const [paymentType, setPaymentType] = useState("all");
  const [role, setRole] = useState("");
  const [targetYear, setTargetYear] = useState("");

  // Original API call without filter parameters
  const { data: AllSalaries, isLoading } = useGetSalaries(page, limit);
  const { mutateAsync: deleteSalary } = useDeleteSalary();
  const { mutateAsync: generateSalary, isPending: isGenerating } =
    useGenerateSalary();

  // Payment type options for filter

  const openModal = () => setShowPopup(true);
  const closeModal = () => {
    setEditData(null);
    setShowPopup(false);
  };

  const openEditModal = (salary: ISalary) => {
    const basicSalary =
      typeof salary.user === "object" ? salary.user.basicSalary || 0 : 0;
    const allowances = salary.totalAllowance || 0;
    const deductions = salary.totalDeduction || 0;
    const sstPercentage = salary.sstPercentage || 0;

    const netSalary = basicSalary + allowances - deductions;
    const sstAmount = (netSalary * sstPercentage) / 100;
    const calculatedSalary = netSalary - sstAmount;

    setEditData({
      ...salary,
      calculatedSalary: calculatedSalary,
      dispatchedSalary: netSalary,
    });
    setEditPopup(true);
  };

  const closeEditModal = () => {
    setEditData(null);
    setEditPopup(false);
  };

  const openDeleteModal = (salaryId: string) => {
    setSalaryToDelete(salaryId);
    setDeletePopup(true);
  };

  const closeDeleteModal = () => {
    setSalaryToDelete(null);
    setDeletePopup(false);
  };

  const handleDelete = async () => {
    if (salaryToDelete) {
      try {
        await deleteSalary(salaryToDelete);
        toast.success("Salary record deleted successfully");
        closeDeleteModal();
      } catch (error) {
        console.error("Error deleting salary:", error);
        toast.error("Error deleting salary record");
      }
    }
  };

  const openGenerateSalaryModal = () => setGenerateSalaryPopup(true);
  const closeGenerateSalaryModal = () => setGenerateSalaryPopup(false);

  const handleGenerateSalary = async () => {
    if (!month) {
      toast.error("Please select a month");
      return;
    }

    try {
      await generateSalary({ targetMonth: month, targetYear: year });
      closeGenerateSalaryModal();
    } catch (error) {
      console.error("Error generating salary:", error);
    }
  };

  const getMonthName = (monthNum: string) => {
    const monthNames = [
      "January",
      "February",
      "March",
      "April",
      "May",
      "June",
      "July",
      "August",
      "September",
      "October",
      "November",
      "December",
    ];
    const num = parseInt(monthNum);
    return monthNames[num - 1] || monthNum;
  };

  const handlePageChange = useCallback((newPage: number, pageSize: number) => {
    setPage(newPage + 1);
    setLimit(pageSize);
  }, []);

  // Filter handlers
  const paymentTypeOptions = [
    { label: "DEBIT", value: "DEBIT" },
    { label: "CREDIT", value: "CREDIT" },
  ];
  const roleOptions = [
    { label: "HouseKeeper", value: "housekeeper" },
    { label: "Staff", value: "staff" },
  ];

  const handleSearch = (value: string) => {
    setSearchTerm(value);
    setPage(1);
  };

  const handleApplyFilters = (filters: {
    paymentType: string;
    targetYear: string;
    role?: string;
  }) => {
    setPaymentType(filters.paymentType);
    setTargetYear(filters.targetYear);
    setRole(filters?.role || "");
    setPage(1);
  };

  const excludedRoles = ["admin", "superAdmin", "staff"];

  const filteredSalaries =
    AllSalaries?.salary?.filter((salary: ISalary) => {
      const userRole = typeof salary?.user === "object" ? salary.user.role : "";

      // Exclude admin, superAdmin, staff
      if (excludedRoles.includes(userRole)) return false;

      // Existing filters
      const matchesSearch =
        searchTerm === "" ||
        (typeof salary.user === "object" &&
          salary.user.name.toLowerCase().includes(searchTerm.toLowerCase()));

      const matchesPaymentType =
        paymentType === "all" || salary?.paymentType === paymentType;

      const matchesRole = role === "" || userRole === role;

      const matchesYear =
        targetYear === "" || salary?.targetYear.toString() === targetYear;

      return matchesSearch && matchesPaymentType && matchesRole && matchesYear;
    }) || [];

  const tableData = {
    columns: [
      { title: "SN", key: "sn" },
      { title: "Employee Name", key: "employeeName" },
      { title: "Role", key: "role" },
      { title: "Month/Year", key: "period" },
      // { title: "Calculated Salary", key: "calculatedSalary" },
      { title: "Allowances", key: "allowance" },
      { title: "Deductions", key: "deduction" },
      { title: "SST %", key: "sst" },
      { title: "Dispatch", key: "netSalary" },
      { title: "Payment Type", key: "paymentType" },
      { title: "Actions", key: "action" },
    ],
    rows: filteredSalaries.map((salary: ISalary, index: number) => ({
      sn: index + 1,
      employeeName:
        typeof salary?.user === "object" ? salary?.user?.name : "Employee",
      role: typeof salary?.user === "object" ? salary?.user?.role : "",
      period: `${getMonthName(salary?.targetMonth)} ${salary?.targetYear}`,
      calculatedSalary: `Rs ${salary?.calculatedSalary?.toLocaleString() || 0}`,
      allowance: `Rs ${salary.totalAllowance?.toLocaleString() || 0}`,
      deduction: `Rs ${salary.totalDeduction?.toLocaleString() || 0}`,
      sst: `${salary.sstPercentage || 0}%`,
      netSalary: `Rs ${salary.dispatchedSalary?.toLocaleString() || 0}`,
      paymentType: salary.paymentType || "N/A",
      action: (
        <PayrollTableAction
          onShow={() => {
            const basicSalary =
              typeof salary.user === "object"
                ? salary.user.basicSalary || 0
                : 0;
            const allowances = salary.totalAllowance || 0;
            const deductions = salary.totalDeduction || 0;
            const sstPercentage = salary.sstPercentage || 0;

            const netSalary = basicSalary + allowances - deductions;
            const sstAmount = (netSalary * sstPercentage) / 100;
            const calculatedSalary = netSalary - sstAmount;

            setSelectedSalary({
              ...salary,
              calculatedSalary: calculatedSalary,
              dispatchedSalary: netSalary,
            });
          }}
          onEdit={() => openEditModal(salary)}
          onDelete={() => salary?._id && openDeleteModal(salary._id.toString())}
        />
      ),
    })),
  };

  return (
    <div className="rounded-md shadow w-full">
      <div className="">
        {showPopup && (
          <div className="fixed inset-0 flex items-center justify-center z-50">
            <PopupModal onClose={closeModal}>
              <EmployeePayrollForm editData={null} onClose={closeModal} />
            </PopupModal>
          </div>
        )}

        {editPopup && (
          <div className="fixed inset-0 flex items-center justify-center z-50">
            <PopupModal onClose={closeEditModal}>
              <EmployeePayrollForm
                editData={editData}
                onClose={closeEditModal}
              />
            </PopupModal>
          </div>
        )}

        {deletePopup && (
          <DeleteDialog
            confirmAction={true}
            title="Delete Salary Record"
            des="Are you sure you want to delete this salary record? This action cannot be undone."
            onClose={closeDeleteModal}
            onConfirm={handleDelete}
          />
        )}

        {selectedSalary && (
          <SalaryInvoiceDetail
            salary={selectedSalary}
            onClose={() => setSelectedSalary(null)}
          />
        )}

        {generateSalaryPopup && (
          <div className="fixed inset-0 flex items-center justify-center z-50">
            <PopupModal onClose={closeGenerateSalaryModal}>
              <div className="p-6 w-full max-w-md">
                <h2 className="text-xl font-bold mb-4">
                  Generate Monthly Salary
                </h2>
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Month
                    </label>
                    <select
                      className="w-full p-2 border border-gray-300 rounded-md"
                      value={month}
                      onChange={(e) => setMonth(e.target.value)}
                    >
                      <option value="">Select Month</option>
                      <option value="1">January</option>
                      <option value="2">February</option>
                      <option value="3">March</option>
                      <option value="4">April</option>
                      <option value="5">May</option>
                      <option value="6">June</option>
                      <option value="7">July</option>
                      <option value="8">August</option>
                      <option value="9">September</option>
                      <option value="10">October</option>
                      <option value="11">November</option>
                      <option value="12">December</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Year
                    </label>
                    <input
                      type="number"
                      className="w-full p-2 border border-gray-300 rounded-md"
                      value={year}
                      onChange={(e) => setYear(e.target.value as any)}
                      min="2020"
                      max="2050"
                    />
                  </div>

                  <div className="flex justify-end space-x-2 mt-4">
                    <button
                      onClick={closeGenerateSalaryModal}
                      className="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300"
                    >
                      Cancel
                    </button>
                    <button
                      onClick={handleGenerateSalary}
                      disabled={isGenerating}
                      className="px-4 py-2 bg-[#163381] text-white rounded-md hover:bg-[#163389] disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
                    >
                      {isGenerating ? (
                        <>
                          <Icon
                            icon="mdi:loading"
                            className="animate-spin mr-2"
                          />
                          Generating...
                        </>
                      ) : (
                        "Generate Salary"
                      )}
                    </button>
                  </div>
                </div>
              </div>
            </PopupModal>
          </div>
        )}

        <div className="flex items-center justify-between">
          <Header hideHeader={false} title="" />

          <div className="flex gap-4">
            <button
              onClick={openModal}
              className="px-4 py-2 gap-2 -mt-2 bg-green-600 bg-[#163381] text-white rounded-md hover:bg-green-700 flex items-center"
            >
              <svg
                width="20"
                height="20"
                viewBox="0 0 20 20"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M4.16602 9.99984H15.8327M9.99935 4.1665V15.8332"
                  stroke="#FCFCFC"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
              </svg>{" "}
              Add Payroll
            </button>
            <button
              onClick={openGenerateSalaryModal}
              className="px-4 py-2 -mt-2 bg-green-600 bg-[#163381] text-white rounded-md hover:bg-green-700 flex items-center"
            >
              <Icon icon="mdi:cash-register" className="mr-2" />
              Generate Monthly Salary
            </button>
          </div>
        </div>
      </div>
      <div className="p-4 border bg-white rounded-md mb-2">
        <EmployeePayrollFilter
          onSearch={handleSearch}
          onApplyFilters={handleApplyFilters}
          paymentTypeOptions={paymentTypeOptions}
          roleOptions={roleOptions}
        />
      </div>
      <MasterTable
        columns={tableData?.columns}
        rows={tableData?.rows || []}
        loading={isLoading}
        totalItems={filteredSalaries.length || 0}
        apiPagination={false} // Changed to false since filtering is client-side
        onPageChange={handlePageChange}
        showLimit={true}
        canSearch={false}
      />
    </div>
  );
};

export default EmployeePayrollIndex;
