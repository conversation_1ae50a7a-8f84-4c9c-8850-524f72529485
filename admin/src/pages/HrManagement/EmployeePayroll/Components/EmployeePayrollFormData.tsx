import { IFormData } from "../../../../components/GlobalForm/globalinterface";
import {
  IAllowance,
  IDeduction,
} from "../../../../Interface/employeeconfig.interface";

export const getPayrollFormData = (
  allowances: IAllowance[] = [],
  deductions: IDeduction[] = [],
  users: { _id: string; name: string }[] = []
): IFormData[] => {
  // Map allowances to select options
  const allowanceOptions = allowances.length
    ? allowances.map((allowance) => ({
        label: allowance.name,
        value: allowance._id,
      }))
    : [{ label: "Select Allowance", value: "" }];

  // Map deductions to select options
  const deductionOptions = deductions.length
    ? deductions.map((deduction) => ({
        label: deduction.name,
        value: deduction._id,
      }))
    : [{ label: "Select Deduction", value: "" }];

  // Map users to select options (if provided)
  const employeeOptions = users.length
    ? [
        { label: "Select Employee", value: "" },
        ...users.map((user) => ({
          label: user.name,
          value: user._id,
        })),
      ]
    : [{ label: "Select Employee", value: "" }];

  return [
    {
      field: "user",
      label: "Employee",
      type: "select",
      placeholder: "Select Employee",
      options: employeeOptions,
    },
    {
      field: "targetMonth",
      label: "Month",
      type: "select",
      placeholder: "Select Month",
      options: [
        { label: "January", value: "1" },
        { label: "February", value: "2" },
        { label: "March", value: "3" },
        { label: "April", value: "4" },
        { label: "May", value: "5" },
        { label: "June", value: "6" },
        { label: "July", value: "7" },
        { label: "August", value: "8" },
        { label: "September", value: "9" },
        { label: "October", value: "10" },
        { label: "November", value: "11" },
        { label: "December", value: "12" },
      ],
    },
    {
      field: "targetYear",
      label: "Year",
      type: "number",
      placeholder: "Enter Year",
    },
    {
      field: "calculatedSalary",
      label: "Calculated Salary",
      type: "number",
      placeholder: "Enter Salary Amount",
    },
    {
      field: "allowances",
      label: "Allowances",
      type: "select",
      placeholder: "Select Allowances",
      options: allowanceOptions as any,
    },

    {
      field: "deductions",
      label: "Deductions",
      type: "select",
      placeholder: "Select Deductions",
      options: deductionOptions,
    },

    // {
    //   field: "sstPercentage",
    //   label: "SST Percentage",
    //   type: "number",
    //   placeholder: "Enter SST Percentage",
    // },

    {
      field: "paymentType",
      label: "Payment Type",
      type: "select",
      placeholder: "Select Payment Type",
      options: [
        { label: "Debit Card", value: "DEBIT" },
        { label: "Credit Card", value: "CREDIT" },
      ],
    },
    {
      field: "remarks",
      label: "Remarks",
      type: "textarea",
      placeholder: "Enter any remarks",
    },
  ];
};
