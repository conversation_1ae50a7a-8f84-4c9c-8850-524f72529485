import { Form, FormikProvider, useFormik } from "formik";
import { ISalary } from "../../../../Interface/salary.interface";
import { IUser } from "../../../../Interface/user.interface";
import {
  useCreateSalary,
  useUpdateSalary,
} from "../../../../server-action/API/employeepayroll";
import {
  useGetAllUser,
  useGetUserById,
} from "../../../../server-action/API/user";

import { getPayrollFormData } from "./EmployeePayrollFormData";
import { GlobalForm } from "../../../../components/GlobalForm/GlobalFormComponent";
import { ActionButton } from "../../../../components/ActionButton";
import { toast } from "react-toastify";
import { useMemo } from "react";
import HeadingPopup from "../../../GuestManagement/components/HeadingPopup";
import {
  useGetAllowance,
  useGetDeduction,
} from "../../../../server-action/API/EmployeeConfiguration/salaryconfig";

interface IEmployeePayrollFormProps {
  editData?: ISalary | null;
  onClose: () => void;
}

const EmployeePayrollForm = ({
  onClose,
  editData,
}: IEmployeePayrollFormProps) => {
  const { data: users } = useGetAllUser();
  const { data: allowances } = useGetAllowance();
  const { data: deductions } = useGetDeduction();
  const { data: selectedEmployee } = useGetUserById(
    typeof editData?.user === "object"
      ? editData.user._id
      : editData?.user || ""
  ) as { data: IUser | null | undefined };

  const { mutateAsync: createSalary } = useCreateSalary();
  const { mutateAsync: updateSalary } = useUpdateSalary();

  // Dynamically generate form data with employee, allowance, and deduction options
  const PayrollFormData = useMemo(() => {
    return getPayrollFormData(allowances || [], deductions || [], users || []);
  }, [users, allowances, deductions]);

  const formik = useFormik({
    initialValues: {
      user:
        editData?.user && typeof editData.user === "object"
          ? editData.user._id
          : editData?.user || "",
      targetMonth: editData?.targetMonth || "",
      targetYear: editData?.targetYear || new Date().getFullYear().toString(),
      calculatedSalary: editData?.calculatedSalary || 0,
      allowances: editData?.allowances || [], // Array of allowance IDs
      deductions: editData?.deductions || [], // Array of deduction IDs

      paymentType: editData?.paymentType || "DEBIT",
      remarks: editData?.remarks || "",
    },
    enableReinitialize: true,
    onSubmit: async (values) => {
      try {
        // Prepare JSON payload
        const payload = {
          user: values.user,
          targetMonth: values.targetMonth,
          targetYear: values.targetYear,
          allowances: values.allowances,
          deductions: values.deductions,
          paymentType: values.paymentType,
          remarks: values.remarks,
        };

        // Log payload for debugging
        console.log("Payload contents:", payload);

        if (editData && editData._id) {
          await updateSalary({ _id: editData._id, salaryData: payload });
          toast.success("Salary record updated successfully");
        } else {
          await createSalary(payload);
          toast.success("Salary record created successfully");
        }
        onClose();
      } catch (error) {
        console.error("Submission error:", error);
        toast.error("Error saving salary record");
      }
    },
  });

  const { handleSubmit, getFieldProps } = formik;

  return (
    <HeadingPopup
      onClose={onClose}
      className="w-full max-w-screen-lg h-[80vh]"
      heading={editData ? "Edit Payroll" : "Add Payroll"}
    >
      <FormikProvider value={formik}>
        <Form onSubmit={handleSubmit}>
          <div className="grid grid-cols-3 gap-x-10 gap-y-3">
            <GlobalForm
              formDatails={PayrollFormData}
              getFieldProps={getFieldProps}
            />
          </div>
          <ActionButton onCancel={onClose} onSubmit={handleSubmit} />
        </Form>
      </FormikProvider>
    </HeadingPopup>
  );
};

export default EmployeePayrollForm;
