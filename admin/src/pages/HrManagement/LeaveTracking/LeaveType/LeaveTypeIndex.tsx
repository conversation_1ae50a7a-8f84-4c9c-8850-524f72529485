// LeaveTypeIndex.tsx
import {
  useDeleteLeaveType,
  useGetAllLeaveTypes,
} from "../../../../server-action/API/LeaveTracking/leavetracking";
import { TableAction } from "../../../../layouts/Table/TableAction";
import MasterTable from "../../../../layouts/Table/MasterTable";

interface LeaveTypeIndexProps {
  togglePopup: (data: any) => void;
  editData: any;
  setEditData: (data: any) => void;
}

const LeaveTypeIndex = ({ togglePopup }: LeaveTypeIndexProps) => {
  const { data: allleaveTypeData } = useGetAllLeaveTypes();
  const { mutateAsync: deleteLeaveType } = useDeleteLeaveType();

  console.log(allleaveTypeData, "leaveType");

  const tableData = {
    column: [
      { title: "SN", key: "sn" },
      { title: "Leave Type", key: "name" },
      { title: "Action", key: "action" },
    ],
    rows: allleaveTypeData?.map((leaveType, index) => ({
      sn: index + 1,
      name: leaveType?.name || "N/A",
      action: (
        <TableAction
          onEdit={() => togglePopup(leaveType)} // Open form with leave type data for editing
          onDelete={() =>
            leaveType?._id && deleteLeaveType(leaveType._id.toString())
          }
        />
      ),
    })),
  };

  return (
    <div className="my-2">
      <MasterTable
        loading={false}
        columns={tableData?.column}
        rows={tableData?.rows || []}
      />
    </div>
  );
};

export default LeaveTypeIndex;
