import { Form, FormikProvider, useFormik } from "formik";
import HeadingPopup from "../../../../GuestManagement/components/HeadingPopup";
import { GlobalForm } from "../../../../../components/GlobalForm/GlobalFormComponent";
import { ActionButton } from "../../../../../components/ActionButton";
import {
  useCreateLeaveType,
  useUpdateLeaveType,
} from "../../../../../server-action/API/LeaveTracking/leavetracking";
import { LeaveTypeData } from "./LeaveTypeData";
import { ILeaveTypes } from "../../../../../Interface/leave.interface";
import * as Yup from "yup";
interface LeavesTypeFormData {
  onClose: () => void;
  editData: ILeaveTypes | null;
}

const LeaveTypeValidationSchema = Yup.object().shape({
  name: Yup.string().min(3).required("Name is Required Field"),
});

const LeaveTypeForm = ({ onClose, editData }: LeavesTypeFormData) => {
  const { mutateAsync: createLeaveType } = useCreateLeaveType();
  const { mutateAsync: updateLeaveType } = useUpdateLeaveType();
  const formik = useFormik({
    initialValues: {
      name: editData?.name || "",
      isActive: editData?.isActive || true,
    },
    enableReinitialize: true,
    validationSchema: LeaveTypeValidationSchema,
    onSubmit: async (values) => {
      onClose();
      if (editData) {
        const res = await updateLeaveType({
          _id: editData?._id || "",
          leaveTypeData: values || "",
        });
      } else {
        const res = await createLeaveType(values);
        if (res?.data?.success) {
          // onClose();
        }
      }
    },
  });

  const { handleSubmit, getFieldProps } = formik;

  return (
    <HeadingPopup
      onClose={onClose}
      className="w-full max-w-screen-sm"
      heading={`${editData ? "Edit" : "Add"} Leave Type`}
    >
      <FormikProvider value={formik}>
        <Form onSubmit={handleSubmit}>
          <div className="flex flex-col gap-2">
            <GlobalForm
              formDatails={LeaveTypeData}
              getFieldProps={getFieldProps}
            />
            <ActionButton onCancel={onClose} onSubmit={handleSubmit} />
          </div>
        </Form>
      </FormikProvider>
    </HeadingPopup>
  );
};

export default LeaveTypeForm;
