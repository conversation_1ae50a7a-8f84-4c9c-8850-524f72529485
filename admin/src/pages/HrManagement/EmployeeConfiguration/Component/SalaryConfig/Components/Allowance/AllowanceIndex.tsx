import { useCallback, useState } from "react";
import { Card, CardContent } from "../../../../../../../components/Card";
import MasterTable from "../../../../../../../layouts/Table/MasterTable";
import AllowanceForm from "./AllowanceForm";
import { TableAction } from "../../../../../../../layouts/Table/TableAction";
import {
  useDeleteAllowance,
  useGetAllowance,
} from "../../../../../../../server-action/API/EmployeeConfiguration/salaryconfig";

const AllowanceIndex = () => {
  const [popup, setPopup] = useState(false);
  const { data: allowanceData } = useGetAllowance();
  const { mutateAsync: deleteAllowance } = useDeleteAllowance();
  const [editData, setEditData] = useState(null);

  const tableData = {
    columns: [
      { title: "SN", key: "sn" },
      { title: "Allowance", key: "name" },
      { title: "Category", key: "category" },
      { title: "Value", key: "value" },
      { title: "Action", key: "action" },
    ],
    rows: allowanceData?.map((allowance, index) => ({
      sn: index + 1,
      name: allowance?.name,
      category: allowance?.category,
      value: allowance?.value,

      action: (
        <TableAction
          onEdit={() => {
            setEditData(allowance as any);
            setPopup(true);
          }}
          onDelete={() =>
            allowance?._id && deleteAllowance(allowance._id.toString())
          }
        />
      ),
    })),
  };
  const togglePopup = useCallback(() => setPopup((prev) => !prev), []);

  return (
    <div className="w-full">
      <Card className="bg-white">
        <CardContent className="flex items-center justify-between">
          <p>Allowance</p>
          <button
            onClick={togglePopup}
            className="flex whitespace-nowrap items-center justify-center gap-1 px-4 bg-[#2E4476] py-1 text-white rounded-md"
          >
            Add Allowance
          </button>
        </CardContent>
      </Card>
      <div>
        <MasterTable
          columns={tableData.columns}
          rows={tableData.rows || []}
          loading={false}
        />
      </div>
      {popup && <AllowanceForm onClose={togglePopup} editData={editData} />}
    </div>
  );
};

export default AllowanceIndex;
