import { useState } from "react";
import { PopupModal } from "../../../components";
import { TabData } from "../../../components/TabData";
import { Card, CardContent } from "../../../components/Card";
import Header from "../../../components/Header";
import AddInventoryItemForm from "../../HouseKeeping/Inventory/components/AddInventoryItemForm";
import GeneralInventory from "./components/GeneralInventory";

const Inventoryindex = () => {
  const [showPopup, setShowPopup] = useState("");
  const [showAddForm, setShowAddForm] = useState(false);

  return (
    <div className="flex flex-col gap-4">
      <div className="flex justify-between items-center">
        <Header title="Inventory Management" showButton={false} />
      </div>

      <GeneralInventory />

      {showPopup === "view" && (
        <PopupModal onClose={() => setShowPopup("")}>
          <div>hello</div>
        </PopupModal>
      )}

      {showAddForm && (
        <>
          <AddInventoryItemForm
            onClose={() => {
              console.log("AddInventoryItemForm onClose called");
              setShowAddForm(false);
            }}
          />
        </>
      )}
    </div>
  );
};

export default Inventoryindex;
