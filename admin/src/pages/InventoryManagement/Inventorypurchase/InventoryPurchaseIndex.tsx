import { useCallback, useMemo, useState } from "react";
import { CustomTabs } from "../../../components/CustomTab";
import PurchaseListingIndex from "./PurchaseListingIndex";
import PurchaseOrderIndex from "./PurchaseOrderIndex";
import VendorForm from "./components/VendorForm";
import Header from "../../../components/Header";
import VendorListindex from "./VendorListindex";
import PurchaseOrderForm from "./components/PurchaseOrderForm";
import PurchaseListForm from "./components/PurchaseListForm";
import PurchaseReturnIndex from "./PurchaseReturnIndex";
import { CardContent } from "../../../components/Card";
interface IFormObj {
  [key: string]: React.ReactNode;
}

const InventoryPurchaseIndex = () => {
  const [tab, setTab] = useState("Purchase List");
  const [openForm, setOpenform] = useState("");
  const tabOptions = useMemo(
    () => ["Purchase List", "Purchase Return", "Purchase Order", "Vendor List"],
    []
  );

  const onTabChange = useCallback((status: string) => setTab(status), []);
  const formObj: IFormObj = {
    "Purchase List": <PurchaseListForm close={() => setOpenform("")} />,
    "Purchase Return": <PurchaseListForm close={() => setOpenform("")} />,
    "Purchase Order": <PurchaseOrderForm close={() => setOpenform("")} />,
    "Vendor List": <VendorForm close={() => setOpenform("")} />,
  };
  return (
    <div>
      <Header
        title={tab === "Purchase Return" ? "" : tab}
        onAddClick={() => setOpenform(tab)}
      />
      <CardContent className="flex items-center border bg-white py-3 rounded-md">
        <CustomTabs
          tabs={tabOptions}
          defaultTab={tab}
          onTabChange={onTabChange}
        />
      </CardContent>

      <div className="my-2">
        {tab === "Purchase List" ? (
          <PurchaseListingIndex />
        ) : tab === "Purchase Return" ? (
          <PurchaseReturnIndex />
        ) : tab === "Purchase Order" ? (
          <PurchaseOrderIndex />
        ) : (
          <VendorListindex />
        )}

        {openForm && formObj[openForm]}
      </div>
    </div>
  );
};

export default InventoryPurchaseIndex;
