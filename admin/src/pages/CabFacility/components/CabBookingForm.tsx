import { Form, FormikProvider, useFormik } from "formik";
import HeadingPopup from "../../GuestManagement/components/HeadingPopup";
import { FormField } from "../../BookingManagement/components/ReservationCustomForm";
import { get } from "lodash";
import { useGetAllRoom } from "../../../server-action/API/Room/room";
import { useGetAllCabList } from "../../../server-action/API/CabFacility/CabList/cablist";
import { useGetAllBooking } from "../../../server-action/API/BookingManagement/booking";
import {
  useCreateCabBooking,
  useUpdateCabBooking,
} from "../../../server-action/API/CabFacility/CabBooking/cabbooking";
import { date, object, string } from "yup";
import { Icon } from "@iconify/react/dist/iconify.js";

const CabBookingForm = ({
  onClose,
  edit = false,
  editData,
}: {
  onClose?: () => void;
  edit?: boolean;
  editData?: any;
}) => {
  const yesterday = new Date(new Date().setDate(new Date().getDate() - 1));
  let bookingSchema = object({
    booking: string().required("Room no Doesn't contain any Booking"),
    date: date()
      .min(yesterday, "date cannot be before today")
      .required("Date is required"),
    time: string().required("Time is required"),
    car: string().required("Car is required"),
    roomNo: string().required("Room no is required"),
  });
  const { mutate: createBooking, isPending: creating } = useCreateCabBooking();
  const {
    mutate: updateBooking,

    isPending: updating,
  } = useUpdateCabBooking();
  const { data: booking, isSuccess: bookingSuccess } = useGetAllBooking();

  const bookingOption =
    bookingSuccess && Array.isArray(booking)
      ? booking.map((item: any) => ({
          label: item?.bookingId,
          value: item?._id,
        }))
      : [];

  const { data: cabList, isSuccess: cabListSuccess } = useGetAllCabList();
  const { data: roomList, isSuccess: roomSuccess } = useGetAllRoom();

  const roomOption = roomSuccess
    ? roomList?.map((item) => ({
        label: item.roomNo ?? "",
        value: item._id as string,
      }))
    : [];
  const cabOption = cabListSuccess
    ? cabList?.map((item: any) => ({ label: item.name, value: item._id }))
    : [];
  const cabNumberOption = cabListSuccess
    ? cabList?.map((item: any) => ({
        label: item.carNumber,
        value: item.carNumber,
      }))
    : [];

  const handleClose = () => {
    onClose?.();
  };
  const formik = useFormik({
    initialValues: {
      date: edit ? get(editData, "date", "") : "",
      time: edit ? get(editData, "time", "") : "",
      car: edit ? get(editData, "car._id", "") : "",
      carNumber: edit ? get(editData, "car.carNumber", "") : "",
      roomNo: edit ? get(editData, "booking.room._id", "") : "",
      booking: edit ? get(editData, "booking._id", "") : "",
    },
    enableReinitialize: false,

    validationSchema: bookingSchema,
    onSubmit: async (values) => {
      if (edit) await updateBooking({ id: editData._id, body: values });
      else await createBooking(values);
    },
  });

  return (
    <HeadingPopup
      onClose={handleClose}
      className="w-full max-w-screen-sm"
      heading="Cab Booking"
    >
      <FormikProvider value={formik}>
        <Form onSubmit={formik.handleSubmit} className="space-y-3">
          <div className="grid grid-cols-2 gap-x-4 gap-y-3">
            <FormField label="Date" type="Date" name="date" formik={formik} />
            <FormField
              label="Transportation Time"
              type="time"
              name="time"
              formik={formik}
            />
            <FormField
              label="Car"
              type="dropdown"
              name="car"
              formik={formik}
              placeholder="Choose a Car"
              options={cabOption}
              onChange={(e) => {
                const selectedItem = cabList.find(
                  (p: any) => p._id === e.target.value
                );

                formik.setFieldValue(
                  "carNumber",
                  e.target.value ? selectedItem?.carNumber : ""
                );
              }}
            />
            <FormField
              label="Room no"
              type="dropdown"
              name="roomNo"
              formik={formik}
              placeholder="Choose Room no"
              options={roomOption}
              onChange={(e) => {
                const selectedItem = roomList?.find(
                  (p: any) => p._id === e.target.value
                );
                const selectedBooking = booking?.find(
                  (p: any) => p.room?._id === selectedItem?._id
                );
                formik.setFieldValue(
                  "booking",
                  e.target.value ? selectedBooking?._id : ""
                );
              }}
            />
            <FormField
              label="Cab Number"
              type="dropdown"
              name="carNumber"
              formik={formik}
              placeholder="Choose Cab Number"
              options={cabNumberOption}
              value={formik.values.carNumber}
            />
            <FormField
              label="Booking"
              type="dropdown"
              name="booking"
              formik={formik}
              placeholder="Booking"
              options={bookingOption}
              value={formik.values.booking}
            />
          </div>
          <div className="flex items-center justify-end mt-4">
            <button
              disabled={creating || updating}
              type="submit"
              className="px-4 py-2 bg-[#2A3A6D] text-white rounded-md"
            >
              {creating || updating ? (
                <Icon icon="line-md:loading-loop" width="18" height="18" />
              ) : (
                "Save"
              )}
            </button>
          </div>
        </Form>
      </FormikProvider>
    </HeadingPopup>
  );
};

export default CabBookingForm;
