import { useState } from "react";
import Laundry from "./Laundry";
import LaundryItems from "./components/LaundryItems";
import { CustomTabs } from "../../../components/CustomTab";
import Header from "../../../components/Header";

/**
 * GuestLaundryTabs component that provides tab navigation between Tickets and Laundry views
 */
const GuestLaundryTabs = () => {
  // State to track the currently selected tab
  const [selectedTab, setSelectedTab] = useState<string>("Tickets");

  // Define the tab options
  const tabOptions = ["Tickets", "Laundry Items"];

  // State to handle add button functionality for child components
  const [triggerAddTicket, setTriggerAddTicket] = useState<boolean>(false);
  const [triggerAddLaundryItems, setTriggerAddLaundryItems] =
    useState<boolean>(false);

  // Handle add button click from header
  const handleAddClick = () => {
    if (selectedTab === "Tickets") {
      setTriggerAddTicket(true);
    } else if (selectedTab === "Laundry Items") {
      setTriggerAddLaundryItems(true);
    }
  };

  // Reset trigger states
  const resetTriggers = () => {
    setTriggerAddTicket(false);
    setTriggerAddLaundryItems(false);
  };

  return (
    <div className="">
      {/* Header with dynamic title and add button based on selected tab */}
      <Header
        title={selectedTab === "Tickets" ? "Ticket" : "Laundry Items"}
        onAddClick={handleAddClick}
        showButton={true}
        path={false}
      />

      {/* Tab navigation */}
      <div className="w-full bg-white rounded-md shadow-sm py-3 px-3">
        <CustomTabs
          tabs={tabOptions}
          defaultTab={selectedTab}
          onTabChange={(tab) => {
            setSelectedTab(tab);
          }}
        />
      </div>

      {/* Tab content */}
      <div className="bg-white rounded-lg">
        {selectedTab === "Tickets" && (
          <Laundry
            triggerAdd={triggerAddTicket}
            onAddTriggered={resetTriggers}
          />
        )}
        {selectedTab === "Laundry Items" && (
          <LaundryItems
            triggerAdd={triggerAddLaundryItems}
            onAddTriggered={resetTriggers}
          />
        )}
      </div>
    </div>
  );
};

export default GuestLaundryTabs;
