import { useCallback, useState, useEffect } from "react";
import MasterTable from "../../../layouts/Table/MasterTable";
import { TableAction } from "../../../layouts/Table/TableAction";
import GuestLaundryForm from "../components/GuestLaundryForm";
import { DeleteDialog } from "../../../components";
import CleanLaundry from "./components/CleanLaundry";
import { toast } from "react-toastify";
import {
  useGetAllTickets,
  useCreateTicket,
  useUpdateTicket,
  Ticket as TicketType,
} from "../../../server-action/API/Ticket/Ticket";
import { useGetAllRooms } from "../../../server-action/API/HotelConfiguration/room";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { apiClient } from "../../../server-action/utils/ApiGateway";

interface Ticket extends Omit<TicketType, "room"> {
  reportedBy?: any; // Can be object or string
  room?: any; // Can be object or string
}

interface LaundryProps {
  triggerAdd?: boolean;
  onAddTriggered?: () => void;
}

const Laundry: React.FC<LaundryProps> = ({ triggerAdd, onAddTriggered }) => {
  // Get query client for manual invalidation
  const queryClient = useQueryClient();

  // State for pagination
  const [currentPage, setCurrentPage] = useState<number>(1);
  const itemsPerPage = 10;

  // API hooks
  const {
    data: laundryData,
    isLoading: isLaundryLoading,
    error: laundryError,
    refetch: refetchLaundry,
  } = useGetAllTickets({ ticketCategory: "laundry" });

  // Fetch rooms data
  const { data: roomData } = useGetAllRooms();

  // Fetch housekeepers data with higher priority
  const { data: housekeeperUsers, isLoading: isHousekeepersLoading } = useQuery<
    any[]
  >({
    queryKey: ["housekeepers"],
    queryFn: async () => {
      console.log("Fetching housekeeper users for Laundry component");
      const res = await apiClient.get("/user", {
        params: { role: "housekeeper" },
      });
      console.log("Housekeeper users fetched:", res.data.data);
      return res.data.data || [];
    },
    // Ensure data is refetched when the component mounts
    refetchOnMount: true,
    // Ensure data is fetched before the component is rendered
    staleTime: 0,
    gcTime: 5 * 60 * 1000, // 5 minutes (renamed from cacheTime in newer versions)
  });

  // Mutation hooks
  const { isPending: isCreating } = useCreateTicket();
  const { isPending: isUpdating } = useUpdateTicket();

  // Component state
  const [formState, setFormState] = useState<{
    state: boolean;
    edit: boolean;
    editData: Ticket | null;
  }>({
    state: false,
    edit: false,
    editData: null,
  });
  const [selectedLaundry, setSelectedLaundry] = useState<any>(null);

  // Simplified modal state - only one modal can be open at a time
  const [modalState, setModalState] = useState<{
    type: "" | "delete" | "clean"; // Removed "details" since we're not using it anymore
    id: string | null;
    data: any | null;
  }>({
    type: "",
    id: null,
    data: null,
  });

  // Handle add ticket functionality
  const handleAddTicket = () => {
    // Close any open popups first
    setModalState({
      type: "",
      id: null,
      data: null,
    });
    setSelectedLaundry(null);
    // Then open the add form
    setFormState({ state: true, edit: false, editData: null });
  };

  // Handle API errors and reset pagination when data changes
  useEffect(() => {
    if (laundryError) {
      toast.error("Failed to load laundry data");
      console.error("Laundry fetch error:", laundryError);
    }

    // Reset to first page when data changes
    setCurrentPage(1);
  }, [laundryError, laundryData]);

  // Handle triggerAdd prop from parent component
  useEffect(() => {
    if (triggerAdd) {
      handleAddTicket();
      onAddTriggered?.();
    }
  }, [triggerAdd, onAddTriggered, handleAddTicket]);

  // Close all popups and forms
  const closeAll = useCallback(() => {
    // Reset modal state
    setModalState({
      type: "",
      id: null,
      data: null,
    });
    // Reset form state
    setFormState({
      state: false,
      edit: false,
      editData: null,
    });
    // Reset selected laundry
    setSelectedLaundry(null);
  }, []);

  // Handle delete action
  const handleDelete = useCallback((id: string) => {
    // Close any open form first
    setFormState({
      state: false,
      edit: false,
      editData: null,
    });
    // Then set up the delete dialog
    setModalState({
      type: "delete",
      id: id,
      data: null,
    });
  }, []);

  // Confirm delete action
  const confirmDelete = useCallback(() => {
    const idToDelete = modalState.id;

    if (!idToDelete) {
      console.error("Delete ID is null or undefined");
      return;
    }

    console.log("Attempting to delete ticket with ID:", idToDelete);

    // Use a direct API call to delete the ticket
    try {
      // First close the modal to improve UX
      closeAll();

      // Make the API call directly
      apiClient
        .delete(`ticket/${idToDelete}`)
        .then((response) => {
          console.log("Delete API response:", response);
          toast.success("Laundry ticket deleted successfully");

          // Explicitly invalidate the query cache to trigger a refetch when needed
          queryClient.invalidateQueries({ queryKey: ["tickets"] });
        })
        .catch((error) => {
          console.error("Delete API error:", error);
          toast.error("Failed to delete laundry ticket: " + error.message);
        });
    } catch (error: any) {
      console.error("Delete error in try/catch:", error);
      toast.error("Failed to delete laundry ticket: " + error.message);
    }
  }, [modalState.id, closeAll, queryClient]);

  // Get room number from room ID
  const getRoomNumber = useCallback(
    (room: any) => {
      if (!room) return "N/A";

      // If room is an object with roomNo property
      if (typeof room === "object" && room.roomNo) {
        return room.roomNo;
      }

      // If room has nested structure with roomNo
      if (typeof room === "object" && room.room && room.room.roomNo) {
        return room.room.roomNo;
      }

      // If room is an ID string, find in roomData
      if (typeof room === "string" && roomData) {
        const foundRoom = roomData.find((r: any) => r._id === room);
        if (foundRoom && foundRoom.roomNo) {
          return foundRoom.roomNo;
        }

        // If not found by direct ID match, try to find by nested ID
        const nestedRoom = roomData.find(
          (r: any) => r.room && r.room._id === room
        );
        if (nestedRoom && nestedRoom.roomNo) {
          return nestedRoom.roomNo;
        }

        // If still not found, return the ID as a fallback
        return room;
      }

      return "N/A";
    },
    [roomData]
  );

  // Get housekeeper name from user field - improved version
  const getHousekeeperName = useCallback(
    (user: any) => {
      if (!user) return "N/A";

      // Debug - log the user value to see what we're working with
      console.log("housekeeper user:", user);

      // If user is an object with name property
      if (typeof user === "object" && user.name) {
        return user.name;
      }

      // If user is a string ID, find in housekeeperUsers
      if (
        typeof user === "string" &&
        housekeeperUsers &&
        Array.isArray(housekeeperUsers)
      ) {
        const foundUser = housekeeperUsers.find((u: any) => u._id === user);
        if (foundUser) {
          return (
            foundUser.name || foundUser.username || foundUser.email || user
          );
        }
      }

      // If it's an object with an _id, try to find that ID in housekeeperUsers
      if (
        typeof user === "object" &&
        user._id &&
        housekeeperUsers &&
        Array.isArray(housekeeperUsers)
      ) {
        const foundUser = housekeeperUsers.find((u: any) => u._id === user._id);
        if (foundUser) {
          return (
            foundUser.name || foundUser.username || foundUser.email || user._id
          );
        }
      }

      // If all else fails, return a generic label
      return "Unknown";
    },
    [housekeeperUsers]
  );

  // Calculate paginated data
  const getPaginatedData = () => {
    if (!laundryData) return [];

    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;

    // Return the slice of data for the current page
    return laundryData.slice(startIndex, endIndex);
  };

  // Table data configurations
  const tableData = {
    columns: [
      { key: "sn", title: "S.N" },
      { key: "roomNo", title: "Room No" },
      { key: "housekeeper", title: "Housekeeper" },
      { key: "priority", title: "Priority" },
      { key: "status", title: "Status" },
      { key: "action", title: "Actions" },
    ],
    rows: laundryData
      ? getPaginatedData().map((item: Ticket, index: number) => {
          // Debug - log the item to see the structure
          console.log("Processing ticket item:", item);

          // Get room number
          const roomNumber = getRoomNumber(item.room);

          // Get housekeeper name from user field (previously was reportedBy)
          const housekeeperName = getHousekeeperName(
            item.user || item.reportedBy // Try user first (new structure), fall back to reportedBy (old structure)
          );

          // Calculate the serial number based on the current page
          // For page 1, start with 1, for page 2, start with 11, etc.
          const serialNumber = (currentPage - 1) * itemsPerPage + index + 1;

          return {
            key: item._id,
            sn: serialNumber, // Add serial number with pagination offset
            roomNo: roomNumber,
            housekeeper: housekeeperName,
            priority: item.priority || "medium",
            status: item.ticketStatus || "pending",
            action: (
              <TableAction
                onShow={undefined} // Remove the view functionality by setting onShow to undefined
                onEdit={() => {
                  // Close any open popups first
                  setModalState({
                    type: "",
                    id: null,
                    data: null,
                  });
                  // Then open the edit form
                  setFormState({
                    state: true,
                    edit: true,
                    editData: item,
                  });
                }}
                onDelete={() => handleDelete(item._id as string)}
              />
            ),
          };
        })
      : [],
  };

  return (
    <div>
      {/* Error message */}
      {laundryError && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          Failed to load laundry tickets.{" "}
          <button
            onClick={() => refetchLaundry()}
            className="text-blue-600 underline"
          >
            Retry
          </button>
        </div>
      )}

      {/* Loading state */}
      {(isLaundryLoading ||
        isHousekeepersLoading ||
        !roomData ||
        !housekeeperUsers) && (
        <div className="flex justify-center items-center py-8">
          <p>Loading laundry tickets...</p>
        </div>
      )}

      {/* Data table */}
      {!isLaundryLoading && roomData && housekeeperUsers && (
        <div className="mt-2 bg-white rounded-lg shadow">
          <MasterTable
            columns={tableData.columns}
            rows={tableData.rows}
            loading={isLaundryLoading}
            pagination={{
              currentPage: currentPage,
              totalPage: Math.ceil((laundryData?.length || 0) / itemsPerPage),
              limit: itemsPerPage,
              onClick: (page) => setCurrentPage(page),
            }}
            sortBy="createdAt"
            sortOrder="desc"
          />
        </div>
      )}

      {/* Empty state */}
      {!isLaundryLoading && tableData.rows.length === 0 && (
        <div className="text-center py-8 text-gray-500">
          No laundry tickets found
        </div>
      )}

      {/* Modals */}
      {formState.state && (
        <>
          {isHousekeepersLoading ? (
            <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
              <div className="bg-white p-6 rounded-lg shadow-lg">
                <p className="text-lg font-semibold mb-2">
                  Loading Housekeeper Data
                </p>
                <div className="flex items-center">
                  <svg
                    className="animate-spin h-5 w-5 mr-3 text-blue-500"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                  >
                    <circle
                      className="opacity-25"
                      cx="12"
                      cy="12"
                      r="10"
                      stroke="currentColor"
                      strokeWidth="4"
                    ></circle>
                    <path
                      className="opacity-75"
                      fill="currentColor"
                      d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                    ></path>
                  </svg>
                  <p>Please wait while we load the housekeeper data...</p>
                </div>
              </div>
            </div>
          ) : housekeeperUsers && Array.isArray(housekeeperUsers) ? (
            <GuestLaundryForm
              close={() =>
                setFormState({ state: false, edit: false, editData: null })
              }
              edit={formState.edit}
              editData={formState.editData}
              onSubmit={() => {
                queryClient.invalidateQueries({ queryKey: ["tickets"] });
                setFormState({ state: false, edit: false, editData: null });
              }}
              isLoading={formState.edit ? isUpdating : isCreating}
              housekeeperUsers={housekeeperUsers} // Pass the housekeeper users data
            />
          ) : (
            <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
              <div className="bg-white p-6 rounded-lg shadow-lg">
                <p className="text-lg font-semibold mb-2 text-red-500">
                  Error Loading Data
                </p>
                <p>Failed to load housekeeper data. Please try again.</p>
                <button
                  className="mt-4 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
                  onClick={() =>
                    setFormState({ state: false, edit: false, editData: null })
                  }
                >
                  Close
                </button>
              </div>
            </div>
          )}
        </>
      )}

      {modalState.type === "delete" && (
        <DeleteDialog
          confirmAction={true} // Set to true to ensure the dialog is shown
          title="Delete Laundry Ticket"
          des="Are you sure you want to delete this laundry ticket? This action cannot be undone."
          onClose={closeAll}
          onConfirm={() => {
            console.log("Delete confirmation clicked for ID:", modalState.id);
            confirmDelete();
          }}
        />
      )}

      {/* Details modal removed as we no longer have view functionality */}

      {modalState.type === "clean" && selectedLaundry && (
        <CleanLaundry
          data={selectedLaundry}
          onClose={closeAll}
          onSuccess={() => {
            queryClient.invalidateQueries({ queryKey: ["tickets"] });
            closeAll();
          }}
        />
      )}
    </div>
  );
};

export default Laundry;
