import { Card } from "../../../../components/Card";
import HeadingPopup from "../../../GuestManagement/components/HeadingPopup";
import { useEffect, useMemo } from "react";
import { useGetGuestLaundryById } from "../../../../server-action/API/LaundryManagement/guestLaundry";
import {
  useGetAllBooking,
  getRoomNoFromBooking,
} from "../../../../server-action/API/BookingManagement/booking";
import { useGetAllLaundryCategory } from "../../../../server-action/API/LaundryManagement/laundryCategory";
import MasterTable from "../../../../layouts/Table/MasterTable";

/**
 * GuestLaundryDetails component for displaying detailed information about a guest laundry record
 */
const GuestLaundryDetails = ({
  onClose,
  data,
  id,
}: {
  onClose?: () => void;
  data?: any;
  id?: string;
}) => {
  // Fetch data from API if id is provided
  const {
    data: apiData,
    isLoading,
    isError,
  } = useGetGuestLaundryById(id || "");

  // Fetch bookings data with room information
  const { data: bookings, isSuccess: isBookingsSuccess } = useGetAllBooking();

  // Fetch laundry categories
  const { data: categories, isSuccess: isCategoriesSuccess } =
    useGetAllLaundryCategory();

  // Use API data if available, otherwise use passed data prop
  const displayData = apiData || data;

  // Log bookings data when available
  useEffect(() => {
    if (isBookingsSuccess && bookings) {
      console.log(`Found ${bookings.length} bookings`);

      // If we have display data with a guest, try to find their booking
      if (displayData?.guest?._id) {
        const guestBookings = bookings.filter(
          (booking: any) => booking.guest?._id === displayData.guest._id
        );

        if (guestBookings.length > 0) {
          console.log(
            `Found ${guestBookings.length} bookings for guest ${displayData.guest.name}`
          );
          guestBookings.forEach((booking: any, index: number) => {
            console.log(
              `Booking ${index + 1} room:`,
              getRoomNoFromBooking(booking)
            );
          });
        }
      }
    }
  }, [isBookingsSuccess, bookings, displayData]);

  const handleClose = () => {
    onClose?.();
  };

  // Calculate total cost if not available
  const totalCost =
    displayData?.totalCost ||
    displayData?.items?.reduce(
      (acc: number, item: any) => acc + item.quantity * item.price,
      0
    ) ||
    0;

  // Determine room number using the utility function from booking API
  const roomNo = useMemo(() => {
    if (!displayData) return "N/A";

    // First try to get room number directly from the display data
    const directRoomNo = getRoomNoFromBooking(displayData);
    if (directRoomNo !== "N/A") {
      return directRoomNo;
    }

    // If not found directly, try to find the booking for this laundry item
    // Note: We're not using roomId directly here, but keeping the logic for future reference
    // in case we need to implement room lookup by ID
    if (displayData.room) {
      // Room ID is available in displayData.room
    } else if (displayData.booking?.room) {
      // Room ID is available in displayData.booking.room
    }

    // If we have a booking ID, try to find it in the bookings data
    if (displayData.booking?._id && isBookingsSuccess && bookings) {
      const foundBooking = bookings.find(
        (booking: any) => booking._id === displayData.booking._id
      );
      if (foundBooking) {
        const bookingRoomNo = getRoomNoFromBooking(foundBooking);
        if (bookingRoomNo !== "N/A") {
          return bookingRoomNo;
        }
      }
    }

    // If we have a roomId but no roomNo, try to find the room in the rooms data
    // if (roomId && isRoomsSuccess && rooms) {
    //   const foundRoom = rooms.find((room: any) => room._id === roomId);
    //   if (foundRoom) {
    //     return foundRoom.roomNo;
    //   }
    // }

    // If we have a guest ID, try to find their active booking
    if (displayData.guest?._id && isBookingsSuccess && bookings) {
      const guestBooking = bookings.find(
        (booking: any) =>
          booking.guest?._id === displayData.guest._id &&
          (booking.status === "checked-in" || booking.status === "confirmed")
      );

      if (guestBooking) {
        const guestRoomNo = getRoomNoFromBooking(guestBooking);
        if (guestRoomNo !== "N/A") {
          return guestRoomNo;
        }
      }
    }

    return "N/A";
  }, [displayData, isBookingsSuccess, bookings]);
  //isRoomsSuccess, rooms,

  return (
    <HeadingPopup
      onClose={handleClose}
      className="w-full max-w-screen-lg"
      heading="Guest Laundry Details"
    >
      {isLoading && (
        <div className="flex justify-center items-center py-20">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
          <p className="ml-2">Loading laundry details...</p>
        </div>
      )}

      {isError && !isLoading && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          <p>Failed to load laundry details. Please try again later.</p>
        </div>
      )}
      {!isLoading && displayData && (
        <>
          <Card className="py-5 px-3 my-2">
            <h3 className="font-semibold text-lg mb-3">Laundry Summary</h3>
            <div className="grid grid-cols-3 gap-x-4 justify-center items-center gap-y-3">
              <p>
                <span className="font-semibold">Guest Name:</span>{" "}
                {displayData?.guest?.name ||
                  displayData?.booking?.guest?.name ||
                  "Null"}
              </p>
              <p>
                <span className="font-semibold">Room no:</span> {roomNo}
              </p>
              <p>
                <span className="font-semibold">Total Items:</span>{" "}
                {displayData?.items?.reduce(
                  (acc: number, item: any) => acc + (item.quantity || 0),
                  0
                ) || 0}
              </p>
              <p>
                <span className="font-semibold">Service Type:</span>{" "}
                {displayData?.serviceType?.toLowerCase() === "regular" ||
                displayData?.serviceType?.toLowerCase() === "express"
                  ? displayData?.serviceType?.toLowerCase()
                  : "regular"}
              </p>
              <p>
                <span className="font-semibold">Status:</span>{" "}
                <span
                  className={`px-2 py-1 rounded text-xs ${
                    displayData?.status === "completed" ||
                    displayData?.ticketStatus === "completed"
                      ? "bg-green-100 text-green-800"
                      : displayData?.status === "pending" ||
                        displayData?.ticketStatus === "pending"
                      ? "bg-yellow-100 text-yellow-800"
                      : "bg-gray-100 text-gray-800"
                  }`}
                >
                  {displayData?.status || displayData?.ticketStatus || "Null"}
                </span>
              </p>
              <p>
                <span className="font-semibold">Total Cost:</span> {totalCost}
              </p>
            </div>
          </Card>

          {displayData?.items && displayData.items.length > 0 ? (
            <Card className="py-5 px-3 my-2">
              <h3 className="font-semibold text-lg mb-3">Laundry Items</h3>
              <MasterTable
                columns={[
                  { key: "sn", title: "S.N" },
                  { key: "category", title: "Category" },
                  { key: "serviceType", title: "Service Type" },
                  { key: "service", title: "Service" },
                  { key: "quantity", title: "Quantity" },
                  { key: "price", title: "Price" },
                  { key: "total", title: "Total" },
                ]}
                rows={displayData.items.map((item: any, index: number) => ({
                  sn: index + 1,
                  category:
                    typeof item.category === "object" && item.category?.name
                      ? item.category.name
                      : typeof item.category === "string" &&
                        isCategoriesSuccess &&
                        categories
                      ? categories.find((cat: any) => cat._id === item.category)
                          ?.name || item.category
                      : typeof item.category === "string"
                      ? item.category
                      : "N/A",
                  serviceType:
                    item.serviceType?.toLowerCase() === "regular" ||
                    item.serviceType?.toLowerCase() === "express"
                      ? item.serviceType?.toLowerCase()
                      : "regular",
                  service: item.service?.name || item.service || "N/A",
                  quantity: item.quantity || 0,
                  price: item.price || 0,
                  total: (item.quantity || 0) * (item.price || 0),
                }))}
                canSearch={false}
                canSelect={false}
                loading={false}
                showTopPageSelector={false}
                showLimit={false}
                pagination={undefined}
              />
            </Card>
          ) : (
            <Card className="py-5 px-3 my-2">
              <div className="text-center py-8 text-gray-500">
                No laundry items found
              </div>
            </Card>
          )}
        </>
      )}

      {!isLoading && !isError && !displayData && (
        <div className="text-center py-8 text-gray-500">
          No laundry details available
        </div>
      )}
    </HeadingPopup>
  );
};

export default GuestLaundryDetails;
