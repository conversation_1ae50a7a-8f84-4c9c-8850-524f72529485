import { Form, FormikProvider, useFormik, FieldArray } from "formik";
import React, { useEffect, useState } from "react";
import { toast } from "react-toastify";
import { useQuery } from "@tanstack/react-query";
import { apiClient } from "../../../../server-action/utils/ApiGateway";
import { useAuth } from "../../../../hooks";
import HeadingPopup from "../../../GuestManagement/components/HeadingPopup";
import { useGetAllUser } from "../../../../server-action/API/user";
import {
  getLaundryItemsValidationSchema,
  getInitialValues,
  calculateTotalCost,
  calculateItemPrice,
} from "./LaundryItemsValidation";
import { useGetAllLaundryCategory } from "../../../../server-action/API/LaundryManagement/laundryCategory";
import { useGetAllLaundryService } from "../../../../server-action/API/LaundryManagement/laundryService";
import { useGetAllRooms } from "../../../../server-action/API/HotelConfiguration/room";
import { ILaundryItem } from "../../../../Interface/laundryItems.interface";
import { FiTrash2 } from "react-icons/fi";
import {
  useUpdateGuestLaundry,
  useCreateGuestLaundry,
} from "../../../../server-action/API/LaundryManagement/guestLaundry";
import MasterTable from "../../../../layouts/Table/MasterTable";
import {
  extractObjectId,
  addObjectIdField,
} from "../../../../utils/objectIdHelpers";
import { updateItemPrices } from "../../../../utils/priceCalculationHelpers";

enum ServiceType {
  REGULAR = "regular",
  EXPRESS = "express",
}

interface LaundryItemsFormProps {
  close: () => void;
  edit?: boolean;
  editData?: any;
  onSuccess?: () => void;
}

const LaundryItemsForm: React.FC<LaundryItemsFormProps> = ({
  close,
  edit = false,
  editData,
  onSuccess,
}) => {
  const { data: authData } = useAuth();
  const currentUser = authData?.user;
  const isGuestUser = currentUser?.role === "guest";

  // State to store the selected guest name for display
  const [selectedGuestName, setSelectedGuestName] = useState<string>("");
  const [selectedRoomNumber, setSelectedRoomNumber] = useState<string>("");

  // Define types for form errors
  type LaundryItemErrors = {
    serviceType?: string;
    category?: string;
    service?: string;
    quantity?: string;
    price?: string;
    totalPrice?: string;
  };

  // Initialize mutation hooks for create and update operations
  const { mutateAsync: createLaundry, isPending: isCreating } =
    useCreateGuestLaundry();
  const { mutateAsync: updateLaundry, isPending: isUpdating } =
    useUpdateGuestLaundry();

  const { data: categories } = useGetAllLaundryCategory();
  const { data: services } = useGetAllLaundryService();
  const { data: guestUsers } = useGetAllUser({ role: "guest" });
  const { data: rooms } = useGetAllRooms();

  const { data: allBookings } = useQuery({
    queryKey: ["bookings"],
    queryFn: async () => (await apiClient.get("booking")).data.data || [],
  });

  const handleRoomChange = (roomId: string) => {
    if (!roomId) return;

    // Set the room value in formik state
    formik.setFieldValue("room", roomId);

    // Get the room number for display
    const selectedRoom = rooms?.find((r: any) => r._id === roomId);
    const roomNumber = selectedRoom?.roomNo || "";
    setSelectedRoomNumber(roomNumber);

    // Find active bookings for this room
    const relevantBookings =
      allBookings?.filter((b: any) => {
        const bookingRoomId = extractObjectId(b.room);
        return bookingRoomId === roomId;
      }) || [];

    // Find a checked-in or confirmed booking
    const activeBooking = relevantBookings.find((b: any) =>
      ["checked-in", "confirmed"].includes(b.status)
    );

    // If there's an active booking, set the guest user
    if (activeBooking) {
      const guestId = extractObjectId(activeBooking.guest);

      if (guestId) {
        // Set the guest ID in formik state
        formik.setFieldValue("user", guestId);

        // Get the guest name for display
        let guestName = "";
        if (
          typeof activeBooking.guest === "object" &&
          activeBooking.guest.name
        ) {
          guestName = activeBooking.guest.name;
        }
        // Otherwise look up the guest name from the guestUsers array
        else if (guestUsers) {
          const foundGuest = guestUsers.find(
            (user: any) => user._id === guestId
          );
          if (foundGuest) {
            guestName = foundGuest.name;
          }
        }

        // Update the guest name state
        setSelectedGuestName(guestName);
      }
    } else {
      // If no active booking found, clear the guest selection
      formik.setFieldValue("user", "");
      setSelectedGuestName("");
    }
  };

  const initialFormValues = getInitialValues(
    edit,
    editData,
    isGuestUser,
    currentUser
  );

  const formik = useFormik({
    initialValues: initialFormValues,
    validationSchema: getLaundryItemsValidationSchema(edit),
    enableReinitialize: true,
    onSubmit: async (values) => {
      console.log("Form submitted:", {
        isEditMode: edit,
        editDataId: editData?._id,
        values,
      });
      try {
        const totalCost = calculateTotalCost(values.items);
        const roomNo =
          rooms?.find((r: any) => r._id === values.room)?.roomNo || "";

        // Ensure items have proper service type (lowercase)
        interface NormalizedLaundryItem extends ILaundryItem {
          serviceType: string;
          totalPrice: number;
        }

        const normalizedItems: NormalizedLaundryItem[] = values.items.map(
          (item: ILaundryItem): NormalizedLaundryItem => ({
            ...item,
            serviceType: item.serviceType?.toLowerCase() || "regular",
            // Ensure totalPrice is calculated correctly
            totalPrice: item.totalPrice || item.quantity * item.price,
          })
        );

        // Determine booking ID - never send an empty string for booking
        // Try to extract booking ID from edit data first
        let bookingId =
          edit && editData ? extractObjectId(editData.booking) : null;

        // If no booking ID found from edit data, use room ID as fallback
        if (!bookingId && values.room) {
          bookingId = values.room;
        }

        // If still no booking ID, find an active booking for this room
        if (!bookingId && values.room && allBookings) {
          const activeBooking = allBookings.find((b: any) => {
            const bookingRoomId = extractObjectId(b.room);
            return (
              bookingRoomId === values.room &&
              ["checked-in", "confirmed"].includes(b.status)
            );
          });

          if (activeBooking) {
            bookingId = extractObjectId(activeBooking);
          }
        }

        // Create base laundry data
        let laundryData = {
          ...values,
          items: normalizedItems,
          totalCost,
          ticketCategory: "laundry",
          isLaundry: true,
          date: new Date().toISOString().split("T")[0],
          user: isGuestUser ? currentUser?._id : values.user,
          guest: isGuestUser ? currentUser?._id : values.user,
          reportedBy: currentUser?._id || "",
          room:
            values.room ||
            (edit && editData ? extractObjectId(editData.room) : null),
          roomNo,
        };

        // Add booking field only if we have a valid ID
        laundryData = addObjectIdField(laundryData, "booking", bookingId);

        if (edit && editData?._id) {
          console.log("[DEBUG] LaundryItemsForm - Updating laundry items:", {
            id: editData._id,
            data: laundryData,
          });

          try {
            // Use the updateLaundry mutation hook instead of direct API call
            const result = await updateLaundry({
              id: editData._id,
              body: laundryData,
            });

            console.log(
              "[DEBUG] LaundryItemsForm - Update successful, result:",
              result
            );
            // Toast is handled by the mutation hook
          } catch (updateError) {
            console.error(
              "[DEBUG] LaundryItemsForm - Update failed:",
              updateError
            );
            throw updateError; // Re-throw to be caught by the outer catch block
          }
        } else {
          console.log(
            "[DEBUG] LaundryItemsForm - Creating new laundry items:",
            laundryData
          );

          try {
            // Use the createLaundry mutation hook instead of direct API call
            const result = await createLaundry(laundryData);

            console.log(
              "[DEBUG] LaundryItemsForm - Creation successful, result:",
              result
            );
            // Toast is handled by the mutation hook
          } catch (createError) {
            console.error(
              "[DEBUG] LaundryItemsForm - Creation failed:",
              createError
            );
            throw createError; // Re-throw to be caught by the outer catch block
          }
        }

        onSuccess?.();
        close();
      } catch (error: any) {
        console.error("Error submitting form:", {
          error,
          response: error.response?.data,
          status: error.response?.status,
        });
        const errorMessage =
          error.response?.data?.message || "Submission failed";
        toast.error(errorMessage);
      }
    },
  });

  // Effect to initialize guest name and room number when component mounts
  useEffect(() => {
    // If we're in edit mode, set the guest name and room number
    if (edit && editData) {
      // Set guest name
      if (editData.user) {
        const guestId =
          typeof editData.user === "string" ? editData.user : editData.user._id;
        if (guestId && guestUsers) {
          const foundGuest = guestUsers.find(
            (user: any) => user._id === guestId
          );
          if (foundGuest) {
            setSelectedGuestName(foundGuest.name);
          } else if (typeof editData.user === "object" && editData.user.name) {
            setSelectedGuestName(editData.user.name);
          } else if (editData.displayGuestName) {
            setSelectedGuestName(editData.displayGuestName);
          }
        }
      }

      // Set room number
      if (editData.room) {
        const roomId =
          typeof editData.room === "string" ? editData.room : editData.room._id;
        if (roomId && rooms) {
          const foundRoom = rooms.find((room: any) => room._id === roomId);
          if (foundRoom) {
            setSelectedRoomNumber(foundRoom.roomNo);
          } else if (
            typeof editData.room === "object" &&
            editData.room.roomNo
          ) {
            setSelectedRoomNumber(editData.room.roomNo);
          } else if (editData.displayRoomNo) {
            setSelectedRoomNumber(editData.displayRoomNo);
          }
        }
      }
    }
  }, [edit, editData, guestUsers, rooms]);

  // Effect to recalculate prices when in edit mode
  useEffect(() => {
    if (
      edit &&
      editData &&
      formik.values.items &&
      formik.values.items.length > 0 &&
      categories
    ) {
      // Recalculate prices for each item
      formik.values.items.forEach((item: ILaundryItem, index: number) => {
        if (item.category && item.serviceType) {
          // Ensure category is a string
          const categoryId = extractObjectId(item.category) || "";

          const newPrice = calculateItemPrice(
            categoryId,
            item.serviceType,
            item.quantity,
            categories,
            true
          );

          // Only update if the price is different to avoid infinite loops
          if (newPrice !== item.price) {
            updateItemPrices(formik, index, newPrice, item.quantity);
          }
        }
      });
    }
  }, [edit, editData, categories, formik.values.items, formik.setFieldValue]);

  const getRoomNumber = (roomId: string) => {
    if (!roomId) return "N/A";

    // First try to find the room in the rooms data
    if (rooms) {
      const foundRoom = rooms.find((r: any) => r._id === roomId);
      if (foundRoom?.roomNo) {
        return foundRoom.roomNo;
      }
    }

    // If we're in edit mode, try to get the room number from editData
    if (edit && editData) {
      // If editData has room as an object with roomNo
      if (typeof editData.room === "object" && editData.room?.roomNo) {
        return editData.room.roomNo;
      }

      // If editData has roomNo directly
      if (editData.roomNo) {
        return editData.roomNo;
      }

      // If editData has displayRoomNo (from the table data)
      if (editData.displayRoomNo) {
        return editData.displayRoomNo;
      }
    }

    return "N/A";
  };

  const getGuestName = (userId: string) => {
    if (!userId) return "N/A";

    // First try to find the guest in the guestUsers data
    if (guestUsers) {
      const foundUser = guestUsers.find((u: any) => u._id === userId);
      if (foundUser?.name) {
        return foundUser.name;
      }
    }

    // If we're in edit mode, try to get the guest name from editData
    if (edit && editData) {
      // If editData has user as an object with name
      if (typeof editData.user === "object" && editData.user?.name) {
        return editData.user.name;
      }

      // If editData has guest as an object with name
      if (typeof editData.guest === "object" && editData.guest?.name) {
        return editData.guest.name;
      }

      // If editData has displayGuestName (from the table data)
      if (editData.displayGuestName) {
        return editData.displayGuestName;
      }
    }

    return "N/A";
  };

  return (
    <HeadingPopup
      heading={edit ? "Edit Laundry Items" : "Add Laundry Items"}
      className="w-full max-w-screen-lg"
      onClose={close}
    >
      <FormikProvider value={formik}>
        <Form className="overflow-y-auto max-h-[80vh]">
          <div className="grid grid-cols-2 gap-x-4 gap-y-2 mb-4">
            <div className="col-span-1">
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Guest <span className="text-red">*</span>
              </label>
              {edit ? (
                <div className="w-full p-2 border bg-gray-50 rounded-md">
                  {getGuestName(formik.values.user)}
                  {getGuestName(formik.values.user) === "N/A" && (
                    <span className="text-red-500 ml-2">
                      (Guest not found - ID: {formik.values.user})
                    </span>
                  )}
                </div>
              ) : selectedGuestName ? (
                <div className="w-full p-2 border bg-gray-100 rounded-md flex justify-between items-center">
                  <span>{selectedGuestName}</span>
                  <button
                    type="button"
                    className="text-xs text-blue-600 hover:text-blue-800"
                    onClick={() => {
                      setSelectedGuestName("");
                      formik.setFieldValue("user", "");
                    }}
                  >
                    Change
                  </button>
                </div>
              ) : (
                <select
                  name="user"
                  value={formik.values.user}
                  onChange={(e) => {
                    formik.handleChange(e);
                    // If user is manually selected, update the guest name
                    if (e.target.value && guestUsers) {
                      const selectedUser = guestUsers.find(
                        (user: any) => user._id === e.target.value
                      );
                      if (selectedUser) {
                        setSelectedGuestName(selectedUser.name);
                      }
                    } else {
                      setSelectedGuestName("");
                    }
                  }}
                  className={`w-full p-2 border ${
                    formik.errors.user ? "border-red-500" : "border-gray-300"
                  } rounded-md`}
                  disabled={isGuestUser}
                >
                  <option value="">Select Guest</option>
                  {guestUsers?.map((user: any) => (
                    <option key={user._id} value={user._id}>
                      {user.name}
                    </option>
                  ))}
                </select>
              )}
            </div>

            <div className="col-span-1">
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Room <span className="text-red">*</span>
              </label>
              {edit ? (
                <div className="w-full p-2 border bg-gray-50 rounded-md">
                  {getRoomNumber(formik.values.room)}
                  {getRoomNumber(formik.values.room) === "N/A" && (
                    <span className="text-red-500 ml-2">
                      (Room not found - ID: {formik.values.room})
                    </span>
                  )}
                </div>
              ) : (
                <select
                  name="room"
                  value={formik.values.room}
                  onChange={(e) => {
                    handleRoomChange(e.target.value);
                    formik.handleChange(e); // Also trigger formik's handleChange
                  }}
                  className={`w-full p-2 border ${
                    formik.errors.room ? "border-red-500" : "border-gray-300"
                  } rounded-md`}
                >
                  <option value="">Select Room</option>
                  {rooms?.map((room: any) => (
                    <option key={room._id} value={room._id}>
                      {room.roomNo}
                    </option>
                  ))}
                </select>
              )}
              {formik.errors.room && (
                <div className="text-red text-xs mt-1">
                  {formik.errors.room}
                </div>
              )}
            </div>
          </div>

          <FieldArray name="items">
            {({ push, remove }) => (
              <div className="mt-6">
                <h3 className="text-lg font-semibold mb-2">Laundry Items</h3>
                {formik.values.items.map((item: any, index: number) => (
                  <div key={index} className="grid grid-cols-12 gap-2 mb-4">
                    <div className="col-span-2 relative">
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Service Type <span className="text-red">*</span>
                      </label>
                      <select
                        name={`items.${index}.serviceType`}
                        value={item.serviceType || ""}
                        onChange={(e) => {
                          formik.setFieldValue(
                            `items.${index}.serviceType`,
                            e.target.value
                          );
                          // Recalculate price when service type changes
                          if (item.category && categories) {
                            const newPrice = calculateItemPrice(
                              item.category,
                              e.target.value,
                              item.quantity,
                              categories,
                              true
                            );
                            updateItemPrices(
                              formik,
                              index,
                              newPrice,
                              item.quantity
                            );
                          }
                        }}
                        className={`w-full p-2 border ${
                          formik.errors.items &&
                          Array.isArray(formik.errors.items) &&
                          formik.errors.items[index] &&
                          (formik.errors.items[index] as LaundryItemErrors)
                            ?.serviceType
                            ? "border-red-500"
                            : "border-gray-300"
                        } rounded-md`}
                      >
                        <option value="">Select Type</option>
                        <option
                          value={ServiceType.REGULAR}
                          selected={
                            item.serviceType?.toLowerCase() ===
                            ServiceType.REGULAR
                          }
                        >
                          Regular
                        </option>
                        <option
                          value={ServiceType.EXPRESS}
                          selected={
                            item.serviceType?.toLowerCase() ===
                            ServiceType.EXPRESS
                          }
                        >
                          Express
                        </option>
                      </select>
                      {formik.errors.items &&
                        Array.isArray(formik.errors.items) &&
                        formik.errors.items[index] &&
                        (formik.errors.items[index] as LaundryItemErrors)
                          ?.serviceType && (
                          <div className="text-red text-xs mt-1">
                            {
                              (formik.errors.items[index] as LaundryItemErrors)
                                .serviceType
                            }
                          </div>
                        )}
                    </div>

                    <div className="col-span-3 relative">
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Category <span className="text-red">*</span>
                      </label>
                      <select
                        name={`items.${index}.category`}
                        value={item.category || ""}
                        onChange={(e) => {
                          formik.setFieldValue(
                            `items.${index}.category`,
                            e.target.value
                          );
                          formik.setFieldValue(`items.${index}.service`, "");
                          // Recalculate price when category changes
                          if (e.target.value && item.serviceType) {
                            const newPrice = calculateItemPrice(
                              e.target.value,
                              item.serviceType,
                              item.quantity,
                              categories || [],
                              true
                            );
                            updateItemPrices(
                              formik,
                              index,
                              newPrice,
                              item.quantity
                            );
                          }
                        }}
                        className={`w-full p-2 border ${
                          formik.errors.items &&
                          Array.isArray(formik.errors.items) &&
                          formik.errors.items[index] &&
                          (formik.errors.items[index] as LaundryItemErrors)
                            ?.category
                            ? "border-red-500"
                            : "border-gray-300"
                        } rounded-md`}
                      >
                        <option value="">Select Category</option>
                        {categories?.map((category: any) => (
                          <option
                            key={category._id}
                            value={category._id}
                            selected={item.category === category._id}
                          >
                            {category.name}
                          </option>
                        ))}
                      </select>
                      {formik.errors.items &&
                        Array.isArray(formik.errors.items) &&
                        formik.errors.items[index] &&
                        (formik.errors.items[index] as LaundryItemErrors)
                          ?.category && (
                          <div className="text-red text-xs mt-1">
                            {
                              (formik.errors.items[index] as LaundryItemErrors)
                                .category
                            }
                          </div>
                        )}
                    </div>

                    <div className="col-span-3 relative">
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Service <span className="text-red">*</span>
                      </label>
                      <select
                        name={`items.${index}.service`}
                        value={item.service || ""}
                        onChange={formik.handleChange}
                        className={`w-full p-2 border ${
                          formik.errors.items &&
                          Array.isArray(formik.errors.items) &&
                          formik.errors.items[index] &&
                          (formik.errors.items[index] as LaundryItemErrors)
                            ?.service
                            ? "border-red-500"
                            : "border-gray-300"
                        } rounded-md`}
                      >
                        <option value="">Select Service</option>
                        {services?.map((service: any) => (
                          <option
                            key={service._id}
                            value={service._id}
                            selected={item.service === service._id}
                          >
                            {service.name}
                          </option>
                        ))}
                      </select>
                      {formik.errors.items &&
                        Array.isArray(formik.errors.items) &&
                        formik.errors.items[index] &&
                        (formik.errors.items[index] as LaundryItemErrors)
                          ?.service && (
                          <div className="text-red text-xs mt-1">
                            {
                              (formik.errors.items[index] as LaundryItemErrors)
                                .service
                            }
                          </div>
                        )}
                    </div>

                    <div className="col-span-2 relative">
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Quantity <span className="text-red">*</span>
                      </label>
                      <input
                        type="number"
                        name={`items.${index}.quantity`}
                        value={item.quantity}
                        onChange={(e) => {
                          const newQuantity = parseInt(e.target.value) || 0;
                          formik.setFieldValue(
                            `items.${index}.quantity`,
                            newQuantity
                          );
                          // Update total price when quantity changes
                          formik.setFieldValue(
                            `items.${index}.totalPrice`,
                            newQuantity * item.price
                          );
                        }}
                        min="1"
                        max="99"
                        className={`w-full p-2 border ${
                          formik.errors.items &&
                          Array.isArray(formik.errors.items) &&
                          formik.errors.items[index] &&
                          (formik.errors.items[index] as LaundryItemErrors)
                            ?.quantity
                            ? "border-red-500"
                            : "border-gray-300"
                        } rounded-md`}
                      />
                      {formik.errors.items &&
                        Array.isArray(formik.errors.items) &&
                        formik.errors.items[index] &&
                        (formik.errors.items[index] as LaundryItemErrors)
                          ?.quantity && (
                          <div className="text-red text-xs mt-1">
                            {
                              (formik.errors.items[index] as LaundryItemErrors)
                                .quantity
                            }
                          </div>
                        )}
                    </div>

                    <div className="col-span-2 relative">
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Price
                      </label>
                      <input
                        type="number"
                        name={`items.${index}.price`}
                        value={item.price}
                        readOnly
                        className="w-full p-2 border border-gray-300 bg-gray-50 rounded-md"
                      />
                    </div>

                    <div className="relative">
                      <button
                        type="button"
                        className="absolute -right-8 top-1/2 transform -translate-y-1/2 p-1 bg-red text-white rounded-full"
                        onClick={() => remove(index)}
                      >
                        <FiTrash2 size={14} />
                      </button>
                    </div>
                  </div>
                ))}

                <button
                  type="button"
                  className="px-4 py-2 bg-[#163381] text-white rounded-md"
                  onClick={() => {
                    const newItem = {
                      serviceType: "regular",
                      category: "",
                      service: "",
                      quantity: 1,
                      price: 0,
                      totalPrice: 0,
                    };
                    push(newItem);
                  }}
                >
                  Add Item
                </button>

                {/* Laundry Items Table */}
                {formik.values.items.length > 0 && (
                  <div className="mt-6">
                    <h3 className="text-lg font-semibold mb-2">
                      Laundry Items Summary
                    </h3>
                    <div className="overflow-x-auto">
                      {(() => {
                        // Define columns for the MasterTable
                        const columns = [
                          {
                            key: "serviceType",
                            title: "Service Type",
                            sortable: true,
                            render: (row: any) => (
                              <span>
                                {row.serviceType === "regular"
                                  ? "Regular"
                                  : "Express"}
                              </span>
                            ),
                          },
                          {
                            key: "category",
                            title: "Category",
                            sortable: true,
                          },
                          {
                            key: "service",
                            title: "Service",
                            sortable: true,
                          },
                          {
                            key: "quantity",
                            title: "Quantity",
                            sortable: true,
                          },
                          {
                            key: "price",
                            title: "Price",
                            sortable: true,
                          },
                          {
                            key: "totalPrice",
                            title: "Total Price",
                            sortable: true,
                            render: (row: any) => (
                              <span>
                                {(
                                  row.totalPrice || row.quantity * row.price
                                ).toFixed(2)}
                              </span>
                            ),
                          },
                          {
                            key: "action",
                            title: "Action",
                            render: (row: any) => (
                              <button
                                type="button"
                                className="text-red hover:text-red-700 flex items-center gap-1"
                                onClick={() => remove(row.index)}
                              >
                                <FiTrash2 size={16} />
                                <span>Remove</span>
                              </button>
                            ),
                          },
                        ];

                        // Transform the items data for the MasterTable
                        const rows = formik.values.items.map(
                          (item: ILaundryItem, index: number) => {
                            // Find category and service names for display
                            const categoryName =
                              categories?.find(
                                (c: any) => c._id === item.category
                              )?.name || "N/A";
                            const serviceName =
                              services?.find((s: any) => s._id === item.service)
                                ?.name || "N/A";

                            return {
                              id: `item-${index}`, // Unique ID for each row
                              index, // Store the index for the remove function
                              serviceType: item.serviceType || "regular",
                              category: categoryName,
                              service: serviceName,
                              quantity: item.quantity,
                              price: item.price,
                              totalPrice:
                                item.totalPrice || item.quantity * item.price,
                            };
                          }
                        );

                        return (
                          <MasterTable
                            rows={rows}
                            columns={columns}
                            entriesPerPage={10}
                            canSearch={true}
                            showFilter={false}
                            loading={false}
                            sortBy="serviceType"
                            sortOrder="asc"
                          />
                        );
                      })()}
                    </div>

                    {/* Add another button below the table */}
                    <div className="mt-4">
                      <button
                        type="button"
                        className="px-4 py-2 bg-[#163381] text-white rounded-md"
                        onClick={() => {
                          const newItem = {
                            serviceType: "regular",
                            category: "",
                            service: "",
                            quantity: 1,
                            price: 0,
                            totalPrice: 0,
                          };
                          push(newItem);
                        }}
                      >
                        Add More Items
                      </button>
                    </div>
                  </div>
                )}

                {/* Total Cost Display */}
                <div className="mt-4 text-right">
                  <div className="inline-block bg-gray-100 p-3 rounded-md">
                    <span className="font-semibold">Total Cost: </span>
                    <span className="text-lg">
                      {calculateTotalCost(formik.values.items).toFixed(2)}
                    </span>
                  </div>
                </div>
              </div>
            )}
          </FieldArray>

          <div className="flex justify-end mt-6 space-x-2">
            <button
              type="button"
              onClick={close}
              className="px-4 py-2 rounded-md bg-gray-200"
              disabled={isCreating || isUpdating}
            >
              Cancel
            </button>
            <button
              type="submit"
              className="px-4 rounded-md py-2 bg-[#163381] text-white"
              disabled={isCreating || isUpdating}
              onClick={() =>
                console.log("Update/Add button clicked", {
                  isEditMode: edit,
                  editDataId: editData?._id,
                })
              }
            >
              {isCreating || isUpdating ? (
                <span className="flex items-center">
                  <svg
                    className="animate-spin -ml-1 mr-2 h-4 w-4 text-white"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                  >
                    <circle
                      className="opacity-25"
                      cx="12"
                      cy="12"
                      r="10"
                      stroke="currentColor"
                      strokeWidth="4"
                    ></circle>
                    <path
                      className="opacity-75"
                      fill="currentColor"
                      d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                    ></path>
                  </svg>
                  {edit ? "Updating..." : "Adding..."}
                </span>
              ) : edit ? (
                "Update Laundry Items"
              ) : (
                "Add Laundry Items"
              )}
            </button>
          </div>
        </Form>
      </FormikProvider>
    </HeadingPopup>
  );
};

export default LaundryItemsForm;
