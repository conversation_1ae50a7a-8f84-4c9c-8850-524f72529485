import * as Yup from "yup";
import { ILaundryItem } from "../../../../Interface/laundryItems.interface";

// Common validation for laundry items
const laundryItemSchema = Yup.object().shape({
  serviceType: Yup.string().required("Service Type is required"),
  category: Yup.string().required("Category is required"),
  service: Yup.string().required("Service is required"),
  quantity: Yup.number()
    .required("Quantity is required")
    .min(1, "Quantity must be at least 1"),
  price: Yup.number()
    .required("Price is required")
    .min(0, "Price cannot be negative"),
  totalPrice: Yup.number().min(0, "Total price cannot be negative"),
});

// Form validation schema for add mode
export const addLaundryItemsValidationSchema = Yup.object({
  user: Yup.string().required("Guest is required"),
  room: Yup.string().required("Room is required"),
  items: Yup.array()
    .of(laundryItemSchema)
    .min(1, "At least one laundry item is required"),
});

// Form validation schema for edit mode
export const editLaundryItemsValidationSchema = Yup.object({
  user: Yup.string().required("Guest is required"),
  room: Yup.string(), // Room is not required in edit mode as it's already set and displayed as read-only
  items: Yup.array()
    .of(laundryItemSchema)
    .min(1, "At least one laundry item is required"),
});

// Get the appropriate validation schema based on edit mode
export const getLaundryItemsValidationSchema = (isEditMode: boolean) => {
  return isEditMode
    ? editLaundryItemsValidationSchema
    : addLaundryItemsValidationSchema;
};

// Get service types for a category
export const getServiceTypesForCategory = (
  categoryId: string,
  categories: any[],
  isCategoriesSuccess: boolean
) => {
  const defaultTypes = [{ type: "regular" }, { type: "express" }];

  if (!isCategoriesSuccess || !categories || categories.length === 0) {
    return defaultTypes;
  }

  const category = categories.find((cat: any) => cat._id === categoryId);
  if (!category) return defaultTypes;

  if (category.serviceTypes && Array.isArray(category.serviceTypes)) {
    return category.serviceTypes;
  }

  if (category.services && Array.isArray(category.services)) {
    return category.services;
  }

  return defaultTypes;
};

// Calculate price based on service type and quantity
export const calculateItemPrice = (
  categoryId: string,
  serviceType: string,
  quantity: number,
  categories: any[],
  isCategoriesSuccess: boolean
) => {
  if (!categoryId || !serviceType) return 0;

  const serviceTypes = getServiceTypesForCategory(
    categoryId,
    categories,
    isCategoriesSuccess
  );
  const serviceTypeObj = serviceTypes.find(
    (type: any) => type.type === serviceType.toLowerCase()
  );

  const basePrice = serviceTypeObj ? serviceTypeObj.price || 0 : 0;
  return basePrice * quantity;
};

// Get initial values for the form
export const getInitialValues = (
  edit: boolean,
  editData: any,
  isGuestUser: boolean,
  currentUser: any
) => {
  if (edit && editData) {
    // Handle user field which can be string or object
    let userId = "";

    // First try to get user ID from the user field
    if (typeof editData.user === "string") {
      userId = editData.user;
    } else if (editData.user && typeof editData.user === "object") {
      // Check if user has _id property
      if ("_id" in editData.user) {
        userId = editData.user._id;
      }
      // If user has id property (some APIs might use id instead of _id)
      else if ("id" in editData.user) {
        userId = editData.user.id;
      }
    }

    // If userId is still empty, try to get it from other properties
    if (!userId) {
      // Try guest property
      if (typeof editData.guest === "string") {
        userId = editData.guest;
      } else if (
        editData.guest &&
        typeof editData.guest === "object" &&
        "_id" in editData.guest
      ) {
        userId = editData.guest._id;
      }

      // Try booking.guest property
      else if (editData.booking && editData.booking.guest) {
        if (typeof editData.booking.guest === "string") {
          userId = editData.booking.guest;
        } else if (
          typeof editData.booking.guest === "object" &&
          "_id" in editData.booking.guest
        ) {
          userId = editData.booking.guest._id;
        }
      }
    }

    // Handle room field which can be string or object
    let roomId = "";

    // First try to get room ID from the room field
    if (typeof editData.room === "string") {
      roomId = editData.room;
    } else if (editData.room && typeof editData.room === "object") {
      // Check if room has _id property
      if ("_id" in editData.room) {
        roomId = editData.room._id || "";
      }
      // If room has id property (some APIs might use id instead of _id)
      else if ("id" in editData.room) {
        roomId = editData.room.id || "";
      }
    }

    // If roomId is still empty, try to get it from other properties
    if (!roomId) {
      // Try roomId property
      if (editData.roomId) {
        roomId = editData.roomId;
      }
      // Try booking.room property
      else if (editData.booking && editData.booking.room) {
        if (typeof editData.booking.room === "string") {
          roomId = editData.booking.room;
        } else if (
          typeof editData.booking.room === "object" &&
          "_id" in editData.booking.room
        ) {
          roomId = editData.booking.room._id || "";
        }
      }
    }

    return {
      user: userId,
      room: roomId,
      status: editData.status || "pending",
      priority: editData.priority || "medium",
      items:
        Array.isArray(editData.items) && editData.items.length > 0
          ? editData.items.map((item: any) => {
              // Handle category field which can be string or object
              let categoryId = "";
              if (typeof item.category === "string") {
                categoryId = item.category;
              } else if (
                item.category &&
                typeof item.category === "object" &&
                "_id" in item.category
              ) {
                categoryId = item.category._id;
              }

              // Handle service field which can be string or object
              let serviceId = "";
              if (typeof item.service === "string") {
                serviceId = item.service;
              } else if (
                item.service &&
                typeof item.service === "object" &&
                "_id" in item.service
              ) {
                serviceId = item.service._id;
              }

              // Normalize service type to ensure proper case (regular or express)
              let normalizedServiceType = item.serviceType || "regular"; // Default to regular if not set

              // Convert to lowercase for comparison
              const serviceTypeLower =
                typeof normalizedServiceType === "string"
                  ? normalizedServiceType.toLowerCase()
                  : "";

              if (serviceTypeLower === "regular") {
                normalizedServiceType = "regular";
              } else if (serviceTypeLower === "express") {
                normalizedServiceType = "express";
              } else {
                // If it's not a recognized value, default to regular
                normalizedServiceType = "regular";
              }

              return {
                serviceType: normalizedServiceType,
                category: categoryId,
                service: serviceId,
                quantity: item.quantity || 1,
                price: item.price || 0,
                totalPrice: item.totalPrice || item.quantity * item.price || 0,
              };
            })
          : [
              {
                serviceType: "",
                category: "",
                service: "",
                quantity: 1,
                price: 0,
                totalPrice: 0,
              },
            ],
    };
  }

  // For new records, if the current user is a guest, automatically set the user field
  return {
    user: isGuestUser && currentUser?._id ? currentUser._id : "",
    room: "",
    status: "pending",
    priority: "medium",
    items: [
      {
        serviceType: "regular", // Default to regular for new items
        category: "",
        service: "",
        quantity: 1,
        price: 0,
        totalPrice: 0,
      },
    ],
  };
};

// Calculate total cost for all items
export const calculateTotalCost = (items: ILaundryItem[]) => {
  if (!items || !Array.isArray(items) || items.length === 0) {
    return 0;
  }

  return items.reduce((sum, item) => {
    // If totalPrice is already calculated, use it
    if (item.totalPrice && typeof item.totalPrice === "number") {
      return sum + item.totalPrice;
    }

    // Otherwise calculate from quantity and price
    const quantity = typeof item.quantity === "number" ? item.quantity : 0;
    const price = typeof item.price === "number" ? item.price : 0;

    return sum + quantity * price;
  }, 0);
};
