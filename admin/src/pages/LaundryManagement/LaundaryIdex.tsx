import { Icon } from "@iconify/react/dist/iconify.js";
import { useCallback, useMemo, useState } from "react";
import { Card, CardContent } from "../../components/Card";
import { CustomTabs } from "../../components/CustomTab";
import { SampleTableData } from "../../components/SampleData";
import { Status } from "../../components/Status";
import MasterTable from "../../layouts/Table/MasterTable";
import LaundryForm from "./components/LaundryForm";
import DisposeForm from "./components/DisposeForm";
import GuestLaundryForm from "./components/GuestLaundryForm";

const LaundaryIndex = () => {
  const [popup, setPopup] = useState("");
  const [tab, setTab] = useState("Add Item");
  const tabOptions = useMemo(
    () => ["Add Item", "Dispose", "Guest Laundry"],
    []
  );
  const onTabChange = useCallback((status: string) => setTab(status), []);
  const tableData = {
    columns: [
      { title: "Id", key: "tokenid" },
      { title: "Patient Name", key: "GuestName" },
      { title: "Date ", key: "date" },
      { title: "Room", key: "Room" },
      { title: "checkin", key: "checkin" },
      { title: "Checkout", key: "checkout" },
      { title: "Status", key: "status" },
      { title: "Action", key: "action" },
    ],
    rows: SampleTableData.map(
      (
        { tokenId, GuestName, date, checkin, status, Room, checkout },
        index
      ) => ({
        key: index,
        tokenid: tokenId,
        patientName: GuestName,
        date,
        checkin,
        checkout,
        status: <Status status={status} />,
        Room,
      })
    ),
  };
  const closePopup = useCallback(() => setPopup(""), []);
  return (
    <div className="">
      <Card className="">
        <CardContent className="flex items-center justify-between">
          <CustomTabs
            tabs={tabOptions}
            defaultTab={tab}
            onTabChange={onTabChange}
          />
          <button
            onClick={() => {
              setPopup(
                tab === "Add Item"
                  ? "addForm"
                  : tab === "Dispose"
                  ? "dispose"
                  : "guest"
              );
            }}
            className="flex whitespace-nowrap items-center justify-center gap-1 px-4 bg-[#2E4476] py-1 text-white rounded-md"
          >
            <Icon
              icon="fluent:add-20-regular"
              className="text-white"
              width="30"
              height="30"
            />
            {tab === "Add Item"
              ? "Laundary Item"
              : tab === "Dispose"
              ? "Dispose"
              : "Guest Laundry"}
          </button>
        </CardContent>
      </Card>
      <div>
        {tab === "Add Item" ? (
          <MasterTable
            columns={tableData.columns}
            rows={tableData.rows}
            loading={false}
          />
        ) : tab === "Dispose" ? (
          <MasterTable
            columns={tableData.columns}
            rows={tableData.rows}
            loading={false}
          />
        ) : (
          <MasterTable
            columns={tableData.columns}
            rows={tableData.rows}
            loading={false}
          />
        )}
      </div>
      {popup === "addForm" && <LaundryForm onClose={closePopup} />}
      {popup === "dispose" && <DisposeForm onClose={closePopup} />}
      {popup === "guest" && <GuestLaundryForm close={closePopup} />}
    </div>
  );
};

export default LaundaryIndex;
