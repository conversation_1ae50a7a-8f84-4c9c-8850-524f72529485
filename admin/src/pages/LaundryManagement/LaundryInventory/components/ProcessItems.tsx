/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/ban-ts-comment */
import { useState } from "react";
import { useGetLaundryInventoryStatus } from "../../../../server-action/API/LaundryManagement/laundryInventory";
import MasterTable from "../../../../layouts/Table/MasterTable";
import ProcessItemsForm from "./forms/ProcessItemsForm";
import { ILaundryInventoryItem } from "../../../../Interface/laundryInventory.interface";
import { calculateTotalQuantity } from "../../../../utils/inventoryHelpers";
import { toast } from "react-toastify";

const ProcessItems = () => {
  const [showForm, setShowForm] = useState<boolean>(false);
  const [selectedItem, setSelectedItem] =
    useState<ILaundryInventoryItem | null>(null);
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [pageSize, setPageSize] = useState<number>(10);

  const { data: inventoryStatusData, isLoading } = useGetLaundryInventoryStatus(
    {
      page: currentPage,
      limit: pageSize,
      storeType: "laundry", // Filter to only show laundry store items
    }
  );

  // Extract data from the paginated response
  const inventoryStatus = inventoryStatusData?.data || [];
  const totalItems = inventoryStatusData?.total || 0;

  const handleProcessItems = (item: ILaundryInventoryItem) => {
    // Verify we have the correct item ID
    if (!item.item?._id) {
      toast.error("Cannot process items: Missing item information");
      return;
    }

    setSelectedItem(item);
    setShowForm(true);
  };

  const handleCloseForm = () => {
    setShowForm(false);
    setSelectedItem(null);
  };

  // Prepare data for the table
  const tableData = {
    columns: [
      { key: "sn", title: "S.N" },
      { key: "item", title: "Item" },
      { key: "dirty", title: "Dirty Count" },
      { key: "clean", title: "Clean Count" },
      { key: "total", title: "Total Count" },
      { key: "action", title: "Action" },
    ],
    rows: [] as any[],
  };

  // Process inventory data to find laundry store
  let laundryStore = null;

  if (inventoryStatus && Array.isArray(inventoryStatus)) {
    laundryStore = inventoryStatus.find(
      (store) => store.store?.type === "laundry"
    );
  }

  // Process the laundry store data
  if (laundryStore && laundryStore.items && Array.isArray(laundryStore.items)) {
    // Only show items that have dirty quantity > 0
    const dirtyItems = laundryStore.items.filter(
      (item: ILaundryInventoryItem) => (item.dirtyQuantity || 0) > 0
    );

    dirtyItems.forEach((itemData: ILaundryInventoryItem, index: number) => {
      if (itemData) {
        const itemName = itemData.item?.name || "Unknown Item";
        const dirtyCount = itemData.dirtyQuantity || 0;
        const cleanCount = itemData.cleanQuantity || 0;

        // Calculate total quantity using the helper function
        const totalCount = calculateTotalQuantity(itemData);

        tableData.rows.push({
          key: itemData._id || `item-${index}`,
          sn: index + 1,
          item: itemName,
          dirty: dirtyCount,
          clean: cleanCount,
          total: totalCount,
          action: (
            <button
              className="px-3 py-1 bg-blue text-white rounded hover:bg-blue-600"
              onClick={() => {
                // Ensure we have valid IDs before passing to the form
                // Make sure we pass the correct item object with its _id
                // The ACTUAL item ID needed by the backend is item.item._id
                const itemWithValidIds = {
                  ...itemData,
                  // Ensure the item object is preserved with its _id intact
                  item: {
                    ...itemData.item,
                    _id: itemData.item?._id, // This is the ACTUAL item ID needed
                  },
                };

                // Verify we have the correct item ID
                if (!itemData.item?._id) {
                  toast.error(
                    "Missing required item ID! Cannot process this item."
                  );
                  return;
                }
                handleProcessItems(itemWithValidIds);
              }}
              disabled={dirtyCount === 0}
            >
              Process Items
            </button>
          ),
        });
      }
    });
  }

  return (
    <div className="flex flex-col gap-4">
      <h2 className="text-xl font-semibold">Process Dirty Items in Laundry</h2>

      <MasterTable
        columns={tableData.columns}
        rows={tableData.rows}
        loading={isLoading}
        apiPagination={true}
        totalItems={totalItems}
        onPageChange={(page, limit) => {
          setCurrentPage(page);
          setPageSize(limit);
        }}
      />

      {tableData.rows.length === 0 && !isLoading && (
        <div className="text-center py-8 bg-gray-50 rounded-md">
          <p className="text-gray-500">No dirty items to process in laundry</p>
        </div>
      )}

      {showForm && selectedItem && (
        <ProcessItemsForm item={selectedItem} onClose={handleCloseForm} />
      )}
    </div>
  );
};

export default ProcessItems;
