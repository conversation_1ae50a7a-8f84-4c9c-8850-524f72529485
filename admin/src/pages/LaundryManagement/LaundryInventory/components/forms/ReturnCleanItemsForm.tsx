/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/ban-ts-comment */
import { Form, FormikProvider, useFormik } from "formik";
import * as Yup from "yup";
import { useState, useEffect } from "react";
import HeadingPopup from "../../../../GuestManagement/components/HeadingPopup";
import { FormField } from "../../../../BookingManagement/components/ReservationCustomForm";
import {
  useReturnCleanItems,
  useGetAllStores,
} from "../../../../../server-action/API/LaundryManagement/laundryInventory";
import {
  ILaundryInventoryItem,
  IStore,
} from "../../../../../Interface/laundryInventory.interface";
import { toast } from "react-toastify";

interface ReturnCleanItemsFormProps {
  item: ILaundryInventoryItem;
  onClose: () => void;
}

const ReturnCleanItemsForm: React.FC<ReturnCleanItemsFormProps> = ({
  item,
  onClose,
}) => {
  const { mutateAsync: returnCleanItems, isPending: isReturningItems } =
    useReturnCleanItems();
  const { data: stores, isLoading: isLoadingStores } = useGetAllStores();
  const [storeOptions, setStoreOptions] = useState<
    { value: string; label: string }[]
  >([]);

  // Get the available clean quantity
  const availableQuantity = item.cleanQuantity || 0;

  useEffect(() => {
    if (stores && Array.isArray(stores)) {
      // Filter out laundry store
      const filteredStores = stores.filter(
        (store: IStore) => store.type !== "laundry"
      );

      // @ts-ignore - force the type to match what's expected
      setStoreOptions(
        filteredStores.map((store: IStore) => ({
          // @ts-ignore - ensure value is always a string
          value: store._id || "",
          label: store.name || store.type || "Unknown Store",
        }))
      );
    }
  }, [stores]);

  const validationSchema = Yup.object({
    toStoreId: Yup.string().required("Destination store is required"),
    quantity: Yup.number()
      .required("Quantity is required")
      .positive("Quantity must be positive")
      .max(
        availableQuantity,
        `Maximum clean items available is ${availableQuantity}`
      ),
  });

  // Log the item object to debug ID issues
  console.log(
    "ReturnCleanItemsForm - Item object:",
    JSON.stringify(item, null, 2)
  );
  console.log("ReturnCleanItemsForm - Inventory item _id:", item._id);
  console.log("ReturnCleanItemsForm - Inventory item id:", item.id);
  console.log(
    "ReturnCleanItemsForm - ACTUAL Item ID (item.item._id):",
    item.item?._id
  );

  // Get the item ID - this MUST be the _id from the nested item object
  // This is the actual item ID needed by the backend
  const itemId = item.item?._id || "";
  console.log("ReturnCleanItemsForm - Using CORRECT item ID:", itemId);

  const formik = useFormik({
    initialValues: {
      itemId: itemId,
      itemName: item.item?.name || "Unknown Item",
      toStoreId: "",
      availableQuantity: availableQuantity,
      quantity: 1,
    },
    validationSchema,
    onSubmit: async (values) => {
      try {
        // Ensure we have valid IDs before submission
        // The itemId MUST be the _id from the nested item object (item.item._id)
        // NOT the inventory item's own id or _id
        const itemId = values.itemId || item.item?._id || "";

        // Log the data being sent to the API
        console.log("Submitting return clean items data:", {
          itemId,
          quantity: values.quantity,
          toStoreId: values.toStoreId,
        });

        // Check if IDs are valid before submission
        if (!itemId || !values.toStoreId) {
          console.error("Missing required IDs:", {
            itemId,
            toStoreId: values.toStoreId,
          });
          toast.error("Missing required item or store ID");
          return;
        }

        await returnCleanItems({
          itemId,
          quantity: values.quantity,
          toStoreId: values.toStoreId,
        });

        toast.success("Clean items returned successfully");
        onClose();
      } catch (error) {
        toast.error("Failed to return clean items: " + error);
      }
    },
  });

  const isLoading = isReturningItems || isLoadingStores;

  return (
    <HeadingPopup
      heading="Return Clean Items"
      onClose={onClose}
      className="w-full max-w-md"
    >
      <FormikProvider value={formik}>
        <Form>
          <div className="space-y-4 mt-4">
            <FormField
              label="Item Name"
              name="itemName"
              type="text"
              formik={formik}
              disabled={true}
            />

            <FormField
              label="Available Clean Quantity"
              name="availableQuantity"
              type="number"
              formik={formik}
              disabled={true}
            />

            <div className="space-y-1">
              <label className="block text-sm font-medium text-gray-700">
                Destination Store
              </label>
              <select
                name="toStoreId"
                value={formik.values.toStoreId}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                className="w-full p-2 border border-gray-300 rounded-md"
                disabled={isLoadingStores}
              >
                <option value="">Select Destination Store</option>
                {storeOptions.map((option) => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
              {formik.touched.toStoreId && formik.errors.toStoreId && (
                <div className="text-red-500 text-sm mt-1">
                  {formik.errors.toStoreId}
                </div>
              )}
            </div>

            <FormField
              label="Quantity to Return"
              name="quantity"
              type="number"
              formik={formik}
              min="1"
              max={availableQuantity.toString()}
            />

            <div className="flex justify-end space-x-2 mt-6">
              <button
                type="button"
                onClick={onClose}
                className="px-4 py-2 bg-gray-300 text-gray-800 rounded hover:bg-gray-400"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={isLoading || !formik.isValid}
                className="px-4 py-2 bg-[#163381] text-white rounded hover:bg-blue-600 disabled:opacity-50"
              >
                {isReturningItems ? "Returning..." : "Return Clean Items"}
              </button>
            </div>
          </div>
        </Form>
      </FormikProvider>
    </HeadingPopup>
  );
};

export default ReturnCleanItemsForm;
