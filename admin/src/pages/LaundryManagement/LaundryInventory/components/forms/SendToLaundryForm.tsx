import { Form, FormikProvider, useFormik } from "formik";
import * as Yup from "yup";
import HeadingPopup from "../../../../GuestManagement/components/HeadingPopup";
import { FormField } from "../../../../BookingManagement/components/ReservationCustomForm";
import { useSendToLaundry } from "../../../../../server-action/API/LaundryManagement/laundryInventory";
import { ILaundryInventoryItem } from "../../../../../Interface/laundryInventory.interface";
import { toast } from "react-toastify";
import { calculateTotalQuantity } from "../../../../../utils/inventoryHelpers";

interface SendToLaundryFormProps {
  item: ILaundryInventoryItem;
  onClose: () => void;
}

const SendToLaundryForm: React.FC<SendToLaundryFormProps> = ({
  item,
  onClose,
}) => {
  const { mutateAsync: sendToLaundry, isPending } = useSendToLaundry();

  // Get the available total quantity
  const totalQuantity = calculateTotalQuantity(item);
  const cleanQuantity = item.cleanQuantity || 0;
  const dirtyQuantity = item.dirtyQuantity || 0;
  const storeId = item.store?._id || "";
  const storeName = item.store?.name || item.store?.type || "Unknown Store";

  console.log("SendToLaundryForm - Item:", item);
  console.log("SendToLaundryForm - Total quantity:", totalQuantity);
  console.log("SendToLaundryForm - Clean quantity:", cleanQuantity);
  console.log("SendToLaundryForm - Dirty quantity:", dirtyQuantity);

  const validationSchema = Yup.object({
    quantity: Yup.number()
      .required("Quantity is required")
      .positive("Quantity must be positive")
      .max(totalQuantity, `Maximum items available is ${totalQuantity}`),
  });

  // Log the item object to debug ID issues
  console.log(
    "SendToLaundryForm - Item object:",
    JSON.stringify(item, null, 2)
  );
  console.log("SendToLaundryForm - Inventory item _id:", item._id);
  console.log("SendToLaundryForm - Inventory item id:", item.id);
  console.log(
    "SendToLaundryForm - ACTUAL Item ID (item.item._id):",
    item.item?._id
  );
  console.log("SendToLaundryForm - Store ID:", storeId);

  // Get the item ID - this MUST be the _id from the nested item object
  // This is the actual item ID needed by the backend
  const itemId = item.item?._id || "";
  console.log("SendToLaundryForm - Using CORRECT item ID:", itemId);

  const formik = useFormik({
    initialValues: {
      itemId: itemId,
      itemName: item.item?.name || "Unknown Item",
      storeId: storeId,
      storeName: storeName,
      cleanQuantity: cleanQuantity,
      dirtyQuantity: dirtyQuantity,
      totalQuantity: totalQuantity,
      quantity: 1,
    },
    validationSchema,
    onSubmit: async (values) => {
      try {
        // Ensure we have valid IDs before submission
        // The itemId MUST be the _id from the nested item object (item.item._id)
        // NOT the inventory item's own id or _id
        const itemId = values.itemId || item.item?._id || "";
        const fromStoreId =
          values.storeId || item.store?._id || item.store?.id || "";

        // Log the data being sent to the API
        console.log("Submitting send to laundry data:", {
          itemId,
          quantity: values.quantity,
          fromStoreId,
        });

        // Check if IDs are valid before submission
        if (!itemId || !fromStoreId) {
          console.error("Missing required IDs:", { itemId, fromStoreId });
          toast.error("Missing required item or store ID");
          return;
        }

        await sendToLaundry({
          itemId,
          quantity: values.quantity,
          fromStoreId,
        });

        toast.success("Items sent to laundry successfully");
        onClose();
      } catch (error) {
        toast.error("Failed to send items to laundry: " + error);
      }
    },
  });

  return (
    <HeadingPopup
      heading="Send Items to Laundry"
      onClose={onClose}
      className="w-full max-w-md"
    >
      <FormikProvider value={formik}>
        <Form>
          <div className="space-y-4 mt-4">
            <FormField
              label="Item Name"
              name="itemName"
              type="text"
              formik={formik}
              disabled={true}
            />

            <FormField
              label="From Store"
              name="storeName"
              type="text"
              formik={formik}
              disabled={true}
            />

            <FormField
              label="Clean Quantity"
              name="cleanQuantity"
              type="number"
              formik={formik}
              disabled={true}
            />

            <FormField
              label="Dirty Quantity"
              name="dirtyQuantity"
              type="number"
              formik={formik}
              disabled={true}
            />

            <FormField
              label="Total Available Quantity"
              name="totalQuantity"
              type="number"
              formik={formik}
              disabled={true}
            />

            <FormField
              label="Quantity to Send"
              name="quantity"
              type="number"
              formik={formik}
              min="1"
              max={totalQuantity.toString()}
            />

            <div className="flex justify-end space-x-2 mt-6">
              <button
                type="button"
                onClick={onClose}
                className="px-4 py-2 bg-gray-300 text-gray-800 rounded hover:bg-gray-400"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={isPending || !formik.isValid}
                className="px-4 py-2 bg-[#163381] text-white rounded hover:bg-blue-600 disabled:opacity-50"
              >
                {isPending ? "Sending..." : "Send to Laundry"}
              </button>
            </div>
          </div>
        </Form>
      </FormikProvider>
    </HeadingPopup>
  );
};

export default SendToLaundryForm;
