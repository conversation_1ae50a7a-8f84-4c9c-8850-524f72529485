import { Form, FormikProvider, useFormik } from "formik";
import * as Yup from "yup";
import HeadingPopup from "../../../../GuestManagement/components/HeadingPopup";
import { FormField } from "../../../../BookingManagement/components/ReservationCustomForm";
import { useProcessLaundryItems } from "../../../../../server-action/API/LaundryManagement/laundryInventory";
import { ILaundryInventoryItem } from "../../../../../Interface/laundryInventory.interface";
import { toast } from "react-toastify";

interface ProcessItemsFormProps {
  item: ILaundryInventoryItem;
  onClose: () => void;
}

const ProcessItemsForm: React.FC<ProcessItemsFormProps> = ({
  item,
  onClose,
}) => {
  const { mutateAsync: processItems, isPending } = useProcessLaundryItems();

  // Get the available dirty quantity
  const availableQuantity = item.dirtyQuantity || 0;

  const validationSchema = Yup.object({
    quantity: Yup.number()
      .required("Quantity is required")
      .positive("Quantity must be positive")
      .max(
        availableQuantity,
        `Maximum dirty items available is ${availableQuantity}`
      ),
  });

  // Log the item object to debug ID issues
  console.log("ProcessItemsForm - Item object:", JSON.stringify(item, null, 2));
  console.log("ProcessItemsForm - Inventory item _id:", item._id);
  console.log("ProcessItemsForm - Inventory item id:", item.id);
  console.log(
    "ProcessItemsForm - ACTUAL Item ID (item.item._id):",
    item.item?._id
  );

  // Get the item ID - this MUST be the _id from the nested item object
  // This is the actual item ID needed by the backend
  const itemId = item.item?._id || "";
  console.log("ProcessItemsForm - Using CORRECT item ID:", itemId);

  const formik = useFormik({
    initialValues: {
      itemId: itemId,
      itemName: item.item?.name || "Unknown Item",
      availableQuantity: availableQuantity,
      quantity: 1,
    },
    validationSchema,
    onSubmit: async (values) => {
      try {
        // Ensure we have valid IDs before submission
        // The itemId MUST be the _id from the nested item object (item.item._id)
        // NOT the inventory item's own id or _id
        const itemId = values.itemId || item.item?._id || "";

        // Log the data being sent to the API
        console.log("Submitting process items data:", {
          itemId,
          quantity: values.quantity,
        });

        // Check if itemId is valid before submission
        if (!itemId) {
          console.error("Missing required item ID:", { itemId });
          toast.error("Missing required item ID");
          return;
        }

        await processItems({
          itemId,
          quantity: values.quantity,
        });

        toast.success("Items processed successfully");
        onClose();
      } catch (error) {
        toast.error("Failed to process items: " + error);
      }
    },
  });

  return (
    <HeadingPopup
      heading="Process Laundry Items"
      onClose={onClose}
      className="w-full max-w-md"
    >
      <FormikProvider value={formik}>
        <Form>
          <div className="space-y-4 mt-4">
            <FormField
              label="Item Name"
              name="itemName"
              type="text"
              formik={formik}
              disabled={true}
            />

            <FormField
              label="Available Dirty Quantity"
              name="availableQuantity"
              type="number"
              formik={formik}
              disabled={true}
            />

            <FormField
              label="Quantity to Process"
              name="quantity"
              type="number"
              formik={formik}
              min="1"
              max={availableQuantity.toString()}
            />

            <div className="flex justify-end space-x-2 mt-6">
              <button
                type="button"
                onClick={onClose}
                className="px-4 py-2 bg-gray-300 text-gray-800 rounded hover:bg-gray-400"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={isPending || !formik.isValid}
                className="px-4 py-2 bg-[#163381] text-white rounded hover:bg-green-600 disabled:opacity-50"
              >
                {isPending ? "Processing..." : "Process Items"}
              </button>
            </div>
          </div>
        </Form>
      </FormikProvider>
    </HeadingPopup>
  );
};

export default ProcessItemsForm;
