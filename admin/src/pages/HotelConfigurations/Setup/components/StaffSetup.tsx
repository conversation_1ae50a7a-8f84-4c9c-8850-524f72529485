import React, { useState, useEffect } from "react";
import { Form, FormikProvider, useFormik } from "formik";
import * as Yup from "yup";
import { ActionButton } from "../../../../components/ActionButton";
import { FormField } from "../../../BookingManagement/components/ReservationCustomForm";
import { Card, CardContent } from "../../../../components/Card";
import { TableAction } from "../../../../layouts/Table/TableAction";
import MasterTable from "../../../../layouts/Table/MasterTable";
import { PopupModal } from "../../../../components";
import { Icon } from "@iconify/react";

interface StaffSetupProps {
  onComplete: () => void;
}

const StaffSetup: React.FC<StaffSetupProps> = ({ onComplete }) => {
  const [showForm, setShowForm] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [isPending, setIsPending] = useState(false);

  // Sample staff data for demonstration
  const [sampleStaff, setSampleStaff] = useState([
    { _id: "1", name: "<PERSON> Doe", email: "<EMAIL>", role: "admin" },
    { _id: "2", name: "Jane Smith", email: "<EMAIL>", role: "staff" },
    { _id: "3", name: "Bob Johnson", email: "<EMAIL>", role: "staff" },
  ]);

  // For debugging
  useEffect(() => {
    console.log("Staff data:", sampleStaff);
  }, [sampleStaff]);

  const validationSchema = Yup.object({
    name: Yup.string().required("Name is required"),
    email: Yup.string().email("Invalid email").required("Email is required"),
    password: Yup.string()
      .required("Password is required")
      .min(8, "Password must be at least 8 characters"),
    confirmPassword: Yup.string()
      .oneOf([Yup.ref("password")], "Passwords must match")
      .required("Confirm password is required"),
    role: Yup.string().required("Role is required"),
  });

  const formik = useFormik({
    initialValues: {
      name: "",
      email: "",
      password: "",
      confirmPassword: "",
      role: "staff",
      permissions: {
        Dashboard: 1,
        "Booking Management": 3,
        "Room Management": 1,
        "Guest Management": 3,
        Housekeeping: 1,
        Inventory: 1,
      },
    },
    validationSchema,
    onSubmit: async (values, { resetForm }) => {
      setIsPending(true);
      // Simulate API call
      setTimeout(() => {
        console.log("Staff created:", values);
        // Add the new staff member to our sample data
        const newStaffMember = {
          _id: `${Date.now()}`, // Generate a unique ID
          name: values.name,
          email: values.email,
          role: values.role,
        };
        setSampleStaff([...sampleStaff, newStaffMember]);
        setIsPending(false);
        resetForm();
        setShowForm(false);
      }, 1000);
    },
  });

  const tableData = {
    columns: [
      { title: "S.N.", key: "serialNo" },
      { title: "Name", key: "name" },
      { title: "Email", key: "email" },
      { title: "Role", key: "role" },
      { title: "Actions", key: "action" },
    ],
    rows: sampleStaff.map((staff, index) => ({
      key: staff._id,
      serialNo: index + 1,
      name: staff.name,
      email: staff.email,
      role: staff.role.charAt(0).toUpperCase() + staff.role.slice(1),
      action: <TableAction onEdit={() => {}} onDelete={() => {}} />,
    })),
  };

  // Permission modules
  const permissionModules = [
    "Dashboard",
    "Booking Management",
    "Room Management",
    "Guest Management",
    "Housekeeping",
    "Inventory",
    "Finance",
    "Reports",
    "Settings",
  ];

  // Permission levels
  const permissionLevels = [
    { label: "Read Only", value: 1 },
    { label: "Read & Write", value: 3 },
    { label: "Read, Write & Update", value: 7 },
    { label: "Full Access", value: 15 },
  ];

  return (
    <div>
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-xl font-semibold">Staff Users Configuration</h2>
        <button
          onClick={() => setShowForm(true)}
          className="bg-[#2A3A6D] text-black px-4 py-2 rounded-md flex items-center"
        >
          <span className="mr-2">+</span> Add Staff User
        </button>
      </div>

      <Card>
        <CardContent>
          <p className="text-gray-600 mb-4">
            Create staff user accounts with appropriate permissions. Each staff
            member should have a role and specific permissions for different
            modules of the system.
          </p>

          <div className="mb-4">
            <MasterTable
              columns={tableData.columns}
              rows={tableData.rows}
              loading={isLoading}
              sortBy="name"
              sortOrder="asc"
            />
          </div>

          <div className="flex justify-between items-center mt-4">
            <div>
              {sampleStaff.length === 0 ? (
                <div className="flex items-center">
                  <p className="text-orange-500">
                    <Icon
                      icon="mdi:alert-circle-outline"
                      className="inline mr-1"
                      width="20"
                      height="20"
                    />
                    No staff users found. Consider adding some before
                    completing.
                  </p>
                  <button
                    onClick={() => setShowForm(true)}
                    className="ml-2 text-blue-600 underline text-sm"
                  >
                    Add Now
                  </button>
                </div>
              ) : (
                <p className="text-green-600">
                  <Icon
                    icon="mdi:check-circle-outline"
                    className="inline mr-1"
                    width="20"
                    height="20"
                  />
                  {sampleStaff.length} staff user(s) configured
                </p>
              )}
            </div>
            <div className="flex items-center">
              {/* Always enable the button, but show a warning if no data */}
              <button
                onClick={onComplete}
                className="px-4 py-2 rounded-md bg-green-600 text-black hover:bg-green-700"
              >
                Complete Setup
              </button>
              {sampleStaff.length === 0 && (
                <span className="ml-2 text-xs text-black">
                  <Icon
                    icon="mdi:information-outline"
                    className="inline mr-1"
                    width="16"
                    height="16"
                  />
                  Completing without staff users
                </span>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {showForm && (
        <PopupModal
          onClose={() => setShowForm(false)}
          classname="w-full max-w-4xl"
        >
          <div className="p-4">
            <h3 className="text-lg font-semibold mb-4">Add Staff User</h3>
            <FormikProvider value={formik}>
              <Form onSubmit={formik.handleSubmit}>
                <div className="grid grid-cols-2 gap-4">
                  <FormField
                    name="name"
                    label="Full Name"
                    type="text"
                    placeholder="Staff member's full name"
                    formik={formik}
                  />
                  <FormField
                    name="email"
                    label="Email"
                    type="email"
                    placeholder="Email address"
                    formik={formik}
                  />
                  <FormField
                    name="password"
                    label="Password"
                    type="password"
                    placeholder="Create password"
                    formik={formik}
                  />
                  <FormField
                    name="confirmPassword"
                    label="Confirm Password"
                    type="password"
                    placeholder="Confirm password"
                    formik={formik}
                  />
                  <FormField
                    name="role"
                    label="Role"
                    type="dropdown"
                    placeholder="Select role"
                    options={[
                      { label: "Admin", value: "admin" },
                      { label: "Staff", value: "staff" },
                      { label: "Manager", value: "manager" },
                      { label: "Receptionist", value: "receptionist" },
                      { label: "Housekeeping", value: "housekeeping" },
                    ]}
                    formik={formik}
                  />
                </div>

                <div className="mt-6">
                  <h4 className="font-medium mb-2">Permissions</h4>
                  <div className="bg-gray-50 p-4 rounded-md">
                    <div className="grid grid-cols-3 gap-4">
                      {permissionModules.map((module) => (
                        <div key={module} className="mb-3">
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            {module}
                          </label>
                          <select
                            className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm"
                            {...formik.getFieldProps(`permissions.${module}`)}
                          >
                            {permissionLevels.map((level) => (
                              <option key={level.value} value={level.value}>
                                {level.label}
                              </option>
                            ))}
                          </select>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>

                <div className="mt-6">
                  <ActionButton
                    onCancel={() => setShowForm(false)}
                    onSubmit={formik.handleSubmit}
                    loading={isPending}
                    submitText="Add Staff User"
                  />
                </div>
              </Form>
            </FormikProvider>
          </div>
        </PopupModal>
      )}
    </div>
  );
};

export default StaffSetup;
