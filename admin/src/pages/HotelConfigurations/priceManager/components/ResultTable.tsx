/*eslint-disable @typescript-eslint/no-explicit-any */
import MasterTable from "../../../../layouts/Table/MasterTable";
import { useDeleteDateBasedPrice } from "../../../../server-action/API/HotelConfiguration/priceManager";
import { TableAction } from "../../../../layouts/Table/TableAction";
import {
  useGetAllRoom,
  RoomFilterParams,
} from "../../../../server-action/API/Room/room";
import { IDateBasedPrice } from "../../../../Interface/room.interface";
import { DateForamter } from "../../../../components/DateFormater";

interface ResultTableProps {
  roomType?: string;
  priceStatus?: string;
}

export const ResultTable = ({
  roomType = "all",
  priceStatus = "all",
}: ResultTableProps) => {
  // Create filter object based on roomType prop
  // If roomType is 'all' or undefined, don't add it to filters to show all room types
  const filters: RoomFilterParams = {};
  if (roomType && roomType !== "all") {
    filters.roomType = roomType;
  }

  // Fetch rooms with optional filter
  const { data: rooms, isLoading, refetch } = useGetAllRoom(filters);
  const { mutateAsync: deleteDateBasedPrice } = useDeleteDateBasedPrice();

  // Calculate adjusted price based on base price, status, and rate
  const calculateAdjustedPrice = (
    basePrice: number,
    price: number,
    isPercentage: boolean,
    status: string
  ) => {
    if (!basePrice) return "N/A";

    if (isPercentage) {
      // Calculate percentage-based adjustment
      const adjustment = basePrice * (price / 100);
      return status === "elevated"
        ? basePrice + adjustment
        : basePrice - adjustment;
    } else {
      // Fixed amount adjustment
      return status === "elevated" ? basePrice + price : basePrice - price;
    }
  };

  // Handle delete price entry
  const handleDelete = async (roomId: string, priceId: string) => {
    try {
      await deleteDateBasedPrice({ roomId, priceId });
      refetch(); // Refresh data after deletion
    } catch (error) {
      console.error("Error deleting price entry:", error);
    }
  };

  // Prepare data for table
  const tableRows: any = [];

  // Process each room and its date-based prices
  rooms?.forEach((room) => {
    const basePrice = room.roomPrice?.base || 0;
    const roomTypeName = room.roomType?.name || "Unknown";

    // If room has date-based prices, add each as a row
    if (
      room.roomPrice?.dateBasedPrices &&
      room.roomPrice.dateBasedPrices.length > 0
    ) {
      // Log the date-based prices for debugging

      // Filter date-based prices based on priceStatus
      const filteredPrices = room.roomPrice.dateBasedPrices.filter(
        (priceEntry: IDateBasedPrice) => {
          if (priceStatus === "all") return true;
          return priceEntry.status === priceStatus;
        }
      );

      console.log("Filtered prices count:", filteredPrices.length);

      filteredPrices.forEach(
        (priceEntry: IDateBasedPrice & { _id?: string }) => {
          tableRows.push({
            roomNo: room.roomNo,
            roomType: roomTypeName,
            dateFrom: DateForamter(priceEntry.fromDate),
            dateTo: DateForamter(priceEntry.toDate),
            basePrice: basePrice,
            adjustmentType: priceEntry.isPercentage
              ? "Percentage"
              : "Fixed Amount",
            adjustmentValue: priceEntry.isPercentage
              ? `${priceEntry.price}%`
              : priceEntry.price,
            status: priceEntry.status === "elevated" ? "Elevated" : "Offered",
            finalPrice: calculateAdjustedPrice(
              basePrice,
              priceEntry.price,
              priceEntry.isPercentage,
              priceEntry.status
            ),
            action: (
              <TableAction
                onDelete={() =>
                  handleDelete(room._id as any, priceEntry._id as any)
                }
              />
            ),
          });
        }
      );
    } else {
      // Add room with no date-based prices
      tableRows.push({
        roomNo: room.roomNo,
        roomType: roomTypeName,
        dateFrom: "N/A",
        dateTo: "N/A",
        basePrice: basePrice,
        adjustmentType: "N/A",
        adjustmentValue: "N/A",
        status: "N/A",
        finalPrice: basePrice,
        action: null,
      });
    }
  });

  const tableData = {
    columns: [
      {
        key: "roomNo",
        title: "Room Number",
      },
      {
        key: "roomType",
        title: "Room Type",
      },
      {
        key: "dateFrom",
        title: "From Date",
      },
      {
        key: "dateTo",
        title: "To Date",
      },
      {
        key: "basePrice",
        title: "Base Price",
      },
      {
        key: "status",
        title: "Status",
      },
      {
        key: "adjustmentType",
        title: "Adjustment Type",
      },
      {
        key: "adjustmentValue",
        title: "Adjustment Value",
      },
      {
        key: "finalPrice",
        title: "Final Price",
      },
      {
        key: "action",
        title: "Action",
      },
    ],
    rows: tableRows,
  };

  // If loading, show loading state
  if (isLoading) {
    return <div className="p-4 text-center">Loading room data...</div>;
  }

  // If no rooms found, show a message
  if (!rooms || rooms.length === 0) {
    return (
      <div className="p-4 text-center">
        <p className="text-gray-600">
          No rooms found. Please check your filters or add rooms to the system.
        </p>
      </div>
    );
  }

  // If no table rows (no date-based prices), show a message
  if (tableRows.length === 0) {
    return (
      <div className="p-4 text-center">
        <p className="text-gray-600">
          {priceStatus === "all"
            ? "No date-based pricing found for the selected room type."
            : priceStatus === "elevated"
            ? "No elevated pricing found for the selected room type."
            : "No discount pricing found for the selected room type."}
        </p>
      </div>
    );
  }

  return (
    <div>
      <MasterTable
        columns={tableData.columns}
        rows={tableData.rows}
        loading={isLoading}
      />
    </div>
  );
};
