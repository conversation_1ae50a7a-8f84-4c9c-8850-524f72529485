import { useCallback, useMemo, useState } from "react";
import { PopupModal } from "../../../components";
import Header from "../../../components/Header";
import MasterTable from "../../../layouts/Table/MasterTable";
import { TableAction } from "../../../layouts/Table/TableAction";
import AmenatiesForm from "./components/AmenitiesForm";
import {
  useDeleteAmenity,
  useGetAmenities,
  useGetAmenityCategories,
  useUpdateAmenity,
} from "../../../server-action/API/HotelConfiguration/amenities";
import { CustomTabs } from "../../../components/CustomTab";
import { CardContent } from "../../../components/Card";
import { Status } from "../../../components/Status";
import AmenityCategoryIndex from "./AmenitiesCategory/AmenityCategoryIndex";
import AmenityCategoryForm from "./AmenitiesCategory/Components/AmenityCategoryForm";

const Amenities = () => {
  const [amenatiesForm, setAmenatiesForm] = useState<string>("");
  const [tab, setTab] = useState("Amenities List");
  const [selectedCategory, setSelectedCategory] = useState<any | undefined>(); // State for selected category
  const tabOptions = useMemo(() => ["Amenities List", "Amenity Category"], []);
  const onTabChange = useCallback((status: string) => setTab(status), []);
  const { data: amenitiesCategoryData } = useGetAmenityCategories();
  const { data: amenities } = useGetAmenities();
  const { mutateAsync: deleteAminity } = useDeleteAmenity();
  const { mutateAsync: updateAmenity } = useUpdateAmenity();

  const handleEditCategory = useCallback((category: any) => {
    setSelectedCategory(category); // Set the selected category data
    setAmenatiesForm("category-edit"); // Open the edit form
  }, []);

  const tableData = {
    columns: [
      { title: "S.N.", key: "serialNo" },
      { title: "Amenaties Name", key: "amenities" },
      { title: "Categories", key: "categories" },
      { title: "Room Type", key: "roomType" },
      { title: "Status", key: "status" },
      { title: "Actions", key: "action" },
    ],
    rows: amenities?.map((item: any, index: number) => ({
      key: item?._id,
      serialNo: index + 1,
      categories: item?.category,
      status: <Status status={item?.isActive} />,
      amenities: item?.name?.map(
        (name: string, index: number) =>
          `${name}${index === item?.name?.length - 1 ? "" : ""}`
      ),
      roomType: item?.roomTypes?.name,
      action: (
        <TableAction
          onSwitch={() => {
            updateAmenity({
              _id: item?._id,
              aminityData: {
                ...item,
                isActive: !item?.isActive,
              },
            });
          }}
          switchStatus={item?.isActive}
          onEdit={() => {
            setAmenatiesForm("edit");
            setSelectedCategory(item); // Use setSelectedCategory for amenities
          }}
          onDelete={async () => {
            await deleteAminity(item?._id);
          }}
        />
      ),
    })),
  };

  return (
    <div>
      <div>
        <Header
          title={`${
            tab === "Amenities List" ? "Amenities" : "Amenity Category"
          }`}
          onAddClick={() => {
            if (tab === "Amenities List") {
              setAmenatiesForm("add");
              setSelectedCategory(undefined);
            } else if (tab === "Amenity Category") {
              setAmenatiesForm("category-add");
              setSelectedCategory(undefined);
            }
          }}
        />
      </div>

      <CardContent className="flex items-center border py-3 mb-2 bg-white rounded-md">
        <CustomTabs
          tabs={tabOptions}
          defaultTab={tab}
          onTabChange={onTabChange}
        />
      </CardContent>

      {amenatiesForm === "add" && (
        <PopupModal onClose={() => setAmenatiesForm("")}>
          <AmenatiesForm onClose={() => setAmenatiesForm("")} />
        </PopupModal>
      )}

      {amenatiesForm === "edit" && (
        <PopupModal onClose={() => setAmenatiesForm("")}>
          <AmenatiesForm
            onClose={() => setAmenatiesForm("")}
            editData={selectedCategory}
          />
        </PopupModal>
      )}

      {amenatiesForm === "category-add" && (
        <PopupModal onClose={() => setAmenatiesForm("")}>
          <AmenityCategoryForm onClose={() => setAmenatiesForm("")} />
        </PopupModal>
      )}

      {amenatiesForm === "category-edit" && (
        <PopupModal onClose={() => setAmenatiesForm("")}>
          <AmenityCategoryForm
            onClose={() => setAmenatiesForm("")}
            editData={selectedCategory}
          />
        </PopupModal>
      )}

      <div>
        {tab === "Amenities List" ? (
          <div className="bg-white">
            <MasterTable
              columns={tableData?.columns}
              rows={tableData.rows}
              loading={false}
              pagination={{
                currentPage: 1,
                totalPage: 200,
                limit: 5,
                onClick: () => {},
              }}
            />
          </div>
        ) : (
          <div>
            <AmenityCategoryIndex
              amenitiesCategoryData={amenitiesCategoryData}
              onEdit={handleEditCategory}
            />
          </div>
        )}
      </div>
    </div>
  );
};

export default Amenities;
