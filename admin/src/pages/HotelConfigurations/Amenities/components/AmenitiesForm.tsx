import { Form, FormikProvider, useFormik } from "formik";
import React from "react";
import HeadingPopup from "../../../GuestManagement/components/HeadingPopup";
import { GlobalForm } from "../../../../components/GlobalForm/GlobalFormComponent";
import { ActionButton } from "../../../../components/ActionButton";
import { AmenitiesData } from "./AmenitiesData";
import {
  useCreateAmenities,
  useUpdateAmenity,
} from "../../../../server-action/API/HotelConfiguration/amenities";
import * as Yup from "yup";
interface BEDFORMPROPS {
  onClose: () => void;
  editData?: any;
}

const AmenitiesValidationSchema = Yup.object().shape({
  roomTypes: Yup.string().required("Room Types is Required"),
  category: Yup.string().required("Category is required"),
  name: Yup.array().required("Name is required"),
});

const AmenatiesForm: React.FC<BEDFORMPROPS> = ({ onClose, editData }) => {
  const { mutateAsync: createAmenities } = useCreateAmenities();
  const { mutateAsync: updateAmenity } = useUpdateAmenity();

  const formik = useFormik({
    validationSchema: AmenitiesValidationSchema,
    initialValues: {
      roomTypes: editData?.roomTypes?._id || "",
      category: editData?.category?._id || "",
      name: editData?.name || [],
      isActive: editData?.isActive || true,
    },
    enableReinitialize: true,
    onSubmit: async (values) => {
      if (editData) {
        await updateAmenity({
          _id: editData?._id,
          aminityData: values,
        });
      } else {
        await createAmenities(values);
      }
      onClose();
    },
  });
  const { handleSubmit, getFieldProps } = formik;

  return (
    <HeadingPopup
      onClose={onClose}
      className="w-full max-w-screen-sm"
      heading={`${!editData ? "Add" : "Edit"}  Amenities`}
    >
      <FormikProvider value={formik}>
        <Form onSubmit={handleSubmit}>
          <div className="grid grid-cols-2 gap-2">
            <GlobalForm
              formDatails={AmenitiesData().AmenitiesData}
              getFieldProps={getFieldProps}
            />
          </div>

          <ActionButton onCancel={onClose} onSubmit={handleSubmit} />
        </Form>
      </FormikProvider>
    </HeadingPopup>
  );
};

export default AmenatiesForm;
