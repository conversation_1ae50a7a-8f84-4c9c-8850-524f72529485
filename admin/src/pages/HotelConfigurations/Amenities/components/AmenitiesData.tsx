import { useGetAmenityCategories } from "../../../../server-action/API/HotelConfiguration/amenities";
import { useGetAllRoomType } from "../../../../server-action/API/Room/room-type";

export const AmenitiesData = () => {
  const { data } = useGetAllRoomType();
  const { data: amenitiesCategories } = useGetAmenityCategories();
  console.log(amenitiesCategories, "hello");

  const roomTypeOptions = data?.map((item) => ({
    value: item._id,
    label: item.name,
  }));

  interface AmenityCategory {
    _id: string;
    name: string;
  }

  const amenitiesCategoryOptions = amenitiesCategories?.map(
    (item: AmenityCategory) => ({
      value: item?._id,
      label: item?.name,
    })
  );

  const AmenitiesData = [
    {
      field: "roomTypes",
      label: "Room Type",
      type: "select",
      options: roomTypeOptions,
      placeholder: "Select Room Type",
    },
    {
      field: "category",
      label: "Categorys",
      type: "select",
      options: amenitiesCategoryOptions,
      placeholder: "Add Category",
    },
    {
      field: "name",
      label: "Amenities Name",
      type: "multi-value",
      placeholder: "Add Amenities Name",
    },
    {
      field: "isActive",
      label: "Status",
      type: "toggle",
    },
  ];

  return { AmenitiesData };
};
