import React from "react";
import {
  useGetAmenityCategories,
  useDeleteAmenityCategory,
} from "../../../../server-action/API/HotelConfiguration/amenities";
import { TableAction } from "../../../../layouts/Table/TableAction";
import MasterTable from "../../../../layouts/Table/MasterTable";

interface AmenityCategoryIndexProps {
  amenitiesCategoryData: any;
  onEdit: (category: any) => void; // Add onEdit prop to handle edit action
}

const AmenityCategoryIndex = ({
  amenitiesCategoryData,
  onEdit,
}: AmenityCategoryIndexProps) => {
  const { mutateAsync: deleteAmenityCategory } = useDeleteAmenityCategory();

  const tableData = {
    column: [
      { title: "SN", key: "sn" },
      { title: "Category Name", key: "name" },
      { title: "Action", key: "action" },
    ],
    rows:
      amenitiesCategoryData?.map((category: any, index: number) => ({
        sn: index + 1,
        name: category?.name || "N/A",
        action: (
          <TableAction
            onEdit={() => onEdit(category)} // Pass the category data to the onEdit handler
            onDelete={() =>
              category?._id && deleteAmenityCategory(category._id.toString())
            }
          />
        ),
      })) || [],
  };

  return (
    <div className="my-2">
      <MasterTable
        loading={false}
        columns={tableData.column}
        rows={tableData.rows}
      />
    </div>
  );
};

export default AmenityCategoryIndex;
