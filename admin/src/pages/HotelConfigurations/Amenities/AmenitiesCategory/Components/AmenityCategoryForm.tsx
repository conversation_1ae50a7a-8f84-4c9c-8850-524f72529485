import { Form, FormikProvider, useFormik } from "formik";

import * as Yup from "yup";
import {
  useCreateAmenityCategory,
  useUpdateAmenityCategory,
} from "../../../../../server-action/API/HotelConfiguration/amenities";
import HeadingPopup from "../../../../GuestManagement/components/HeadingPopup";
import { FormField } from "../../../../BookingManagement/components/ReservationCustomForm";

const AmenityCategoryForm = ({
  onClose,
  editData,
}: {
  onClose: () => void;
  editData?: {
    _id: string;
    name: string;
  };
}) => {
  const { mutateAsync: createCategory } = useCreateAmenityCategory();
  const { mutateAsync: updateCategory } = useUpdateAmenityCategory();

  const AmenityCategoryValidation = Yup.object({
    name: Yup.string().required("Name is required"),
  });

  const formik = useFormik({
    validationSchema: AmenityCategoryValidation,
    initialValues: {
      name: editData ? editData.name : "",
    },
    onSubmit: async (values) => {
      if (editData) {
        await updateCategory({
          categoryData: values,
          _id: editData._id,
        });
      } else {
        await createCategory(values);
      }

      onClose();
    },
  });

  return (
    <HeadingPopup
      onClose={onClose}
      className="w-[350px]"
      heading={editData ? "Update Amenity Category" : "Create Amenity Category"}
    >
      <FormikProvider value={formik}>
        <Form onSubmit={formik.handleSubmit} className="space-y-3">
          <FormField
            label="Category Name"
            type="text"
            name="name"
            formik={formik}
            placeholder="Amenity Category"
          />

          <div className="flex items-center justify-end mt-4">
            <button
              type="submit"
              className="px-4 py-2 bg-[#2A3A6D] text-white rounded-md"
            >
              {editData ? "Update" : "Add"}
            </button>
          </div>
        </Form>
      </FormikProvider>
    </HeadingPopup>
  );
};

export default AmenityCategoryForm;
