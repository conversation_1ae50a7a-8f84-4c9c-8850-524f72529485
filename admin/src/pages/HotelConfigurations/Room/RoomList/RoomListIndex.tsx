import { useCallback, useMemo, useState } from "react";
import { useGetAllRoom } from "../../../../server-action/API/Room/room";
import { useDeleteRoom } from "../../../../server-action/API/HotelConfiguration/room";
import { Status } from "../../../../components/Status";
import { TableAction } from "../../../../layouts/Table/TableAction";
import Header from "../../../../components/Header";
import { CardContent } from "../../../../components/Card";
import { CustomTabs } from "../../../../components/CustomTab";
import MasterTable from "../../../../layouts/Table/MasterTable";
import RoomTypeIndex from "./RoomTypes/RoomTypeIndex";
import RoomListForm from "./Components/RoomListForm";
import RoomTypeForm from "../components/RoomTypeForm";
import { IRoom } from "../../../../Interface/room.interface";

const RoomListIndex = () => {
  const [showPopup, setShowPopup] = useState("");
  const [tab, setTab] = useState("Room List");
  const tabOptions = useMemo(() => ["Room List", "Room Type"], []);
  const onTabChange = useCallback((status: string) => setTab(status), []);

  const [popup, setPopup] = useState(false);
  const [editData, setEditData] = useState(null);
  const { data: allRooms } = useGetAllRoom();
  const { mutate: deleteRoom } = useDeleteRoom();

  const tableData = {
    column: [
      { title: "SN", key: "sn" },
      { title: "Room No.", key: "roomNo" },
      { title: "Capacity(Max)", key: "capacity" },
      { title: "Bed Count", key: "bedCount" },
      { title: "A/C Room", key: "acType" },
      { title: "Floor Plan", key: "floorPlan" },
      { title: "Base Price", key: "price" },
      { title: "Status", key: "status" },
      { title: "Action", key: "action" },
    ],
    rows: allRooms?.map((room: IRoom, index: number) => ({
      sn: index + 1,
      roomNo: room?.roomNo,
      capacity: room?.capacity?.maximum ?? "",
      bedCount: room?.beds?.count || "",
      acType: room?.features?.acType || "none",
      floorPlan: room?.floorPlan || "",
      price: `Rs. ${room?.roomPrice?.base}` || "",
      status: room?.status ? <Status status={room?.status} /> : "",
      action: (
        <TableAction
          onEdit={() => {
            setEditData(room as any);
            setPopup(true);
          }}
          onDelete={() => room?._id && deleteRoom(room._id.toString())}
        />
      ),
    })),
  };

  return (
    <div>
      <Header
        hideHeader={true}
        title={`${tab === "Room List" ? "Room" : "Room Type"}`}
        onAddClick={() => {
          if (tab === "Room Type") {
            setShowPopup("add-room-type");
          } else {
            setShowPopup("add");
          }
        }}
      />

      <CardContent className="flex items-center border py-3 mb-2 bg-white rounded-md">
        <CustomTabs
          tabs={tabOptions}
          defaultTab={tab}
          onTabChange={onTabChange}
        />
      </CardContent>

      <div>
        {tab === "Room Type" ? (
          <RoomTypeIndex />
        ) : (
          <div className="bg-white">
            <MasterTable
              columns={tableData.column}
              rows={tableData.rows || []}
              loading={false}
            />
          </div>
        )}
      </div>

      {/* Add Room Popup */}
      {showPopup === "add" && <RoomListForm onClose={() => setShowPopup("")} />}

      {/* View Room Type Popup */}
      {showPopup === "add-room-type" && (
        <RoomTypeForm onClose={() => setShowPopup("")} />
      )}

      {/* Edit Room Popup */}
      {popup && (
        <RoomListForm
          onClose={() => {
            setPopup(false);
            setEditData(null);
          }}
          editData={editData}
        />
      )}
    </div>
  );
};

export default RoomListIndex;
