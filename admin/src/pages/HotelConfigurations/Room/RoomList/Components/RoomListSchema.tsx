import * as Yup from "yup";
export const RoomValidationSchema = Yup.object().shape({
  roomType: Yup.string().required("Room type is required"),
  floorPlan: Yup.string().required("Floor plan is required"),
  floor: Yup.number()
    .typeError("Floor must be a number")
    .required("Floor is required")
    .max(200, "Floor must not exceed 200"),
  roomNo: Yup.string().required("Room number is required"),
  capacity: Yup.object({
    maximum: Yup.number()
      .typeError("Room Capacity must be a number")
      .required("Room Capacity is required")
      .max(20, "Maximum capicity must not exceed 20"),
    standard: Yup.number()
      .typeError("No. of beds must be a number")
      .required("No. of beds is required")
      .max(10, "Maximum capicity must not exceed 10"),
  }),
  roomPrice: Yup.object({
    base: Yup.number()
      .typeError("Base price must be a number")
      .required("Base price is required"),
  }),

  beds: Yup.object({
    types: Yup.string().required("Bed Types is required"),
    count: Yup.number()
      .typeError("Bed count must be a number")
      .required("Bed count is required")
      .max(10, "Bed count must not exceed 10"),
  }),

  features: Yup.object({
    acType: Yup.string().required("AC type is required"),
  }),
  status: Yup.string().required("Status is required"),
  amenities: Yup.string().required("Amenities Name is required"),
});
