import { Form, FormikProvider, useFormik } from "formik";
import { Icon } from "@iconify/react/dist/iconify.js";
import * as Yup from "yup";

import { IRoom } from "../../../../../Interface/room.interface";
import { useGetAllRoomType } from "../../../../../server-action/API/Room/room-type";
import { useGetBeds } from "../../../../../server-action/API/HotelConfiguration/bed";
import { useGetAllFloorPlans } from "../../../../../server-action/API/HotelConfiguration/floorPlan";
import {
  useCreateRoom,
  useUpdateRoom,
} from "../../../../../server-action/API/HotelConfiguration/room";
import { getRoomFormData } from "./RoomFormData";
import { RoomValidationSchema } from "./RoomListSchema";
import HeadingPopup from "../../../../GuestManagement/components/HeadingPopup";
import { GlobalForm } from "../../../../../components/GlobalForm/GlobalFormComponent";
import { ActionButton } from "../../../../../components/ActionButton";
import { useGetAmenities } from "../../../../../server-action/API/HotelConfiguration/amenities";

interface ROOMFORMPROPS {
  editData?: IRoom | null;
  onClose: () => void;
}

const RoomListForm = ({ onClose, editData }: ROOMFORMPROPS) => {
  const { data: roomTypes } = useGetAllRoomType();
  const { data: bedTypes } = useGetBeds();
  const { data: floorPlans } = useGetAllFloorPlans();
  const { data: amenities } = useGetAmenities();

  const { mutateAsync: createRoom } = useCreateRoom();
  const { mutateAsync: updateRoom } = useUpdateRoom();

  const RoomFromData = getRoomFormData(
    roomTypes || [],
    bedTypes || [],
    floorPlans || [],
    amenities || []
  );

  console.log(editData, "editdat");

  const formik = useFormik({
    initialValues: {
      roomType: editData?.roomType?._id || "",
      floorPlan: editData?.floorPlan?._id || "",
      floor: editData?.floor || "",
      roomNo: editData?.roomNo || "",
      images: (editData?.images as (string | File)[]) || [],
      capacity: {
        standard: editData?.capacity?.standard || "",
        maximum: editData?.capacity?.maximum || "",
      },
      roomPrice: {
        base: editData?.roomPrice?.base || "",
      },
      beds: {
        count: editData?.beds?.count || "",
        types: editData?.beds?.types[0]?._id || [],
      },
      isVip: editData?.isVip || false,
      features: {
        acType: editData?.features?.acType || "none",
        // viewType: editData?.features?.viewType || "",
        balcony: editData?.features?.balcony || false,
        smoking: editData?.features?.smoking || false,
      },
      amenities: Array.isArray(editData?.amenities)
        ? editData?.amenities[0]?._id || ""
        : ",",

      status: editData?.status || "",
      description: editData?.description || "",
    },
    enableReinitialize: true,
    validationSchema: RoomValidationSchema,
    onSubmit: async (values) => {
      const formData = new FormData();

      // Append all fields except images as JSON strings or strings
      formData.append("roomType", values.roomType);
      formData.append("floorPlan", values.floorPlan);
      formData.append("floor", String(values.floor));
      formData.append("roomNo", values.roomNo);
      formData.append("capacity.standard", String(values.capacity.standard));
      formData.append("capacity.maximum", String(values.capacity.maximum));
      formData.append("roomPrice.base", String(values.roomPrice.base));
      formData.append("beds.types", String(values.beds.types));
      formData.append("beds.count", String(values.beds.count));
      formData.append("isVip", values.isVip.toString());
      formData.append("features.acType", values.features.acType);
      formData.append("features.balcony", values.features.balcony.toString());
      formData.append("features.smoking", values.features.smoking.toString());
      // formData.append(
      //   "features.viewType",
      //   JSON.stringify(values.features.viewType)
      // );

      formData.append("amenities", values.amenities);
      formData.append("status", values.status);
      formData.append("description", values.description);

      // Handle images
      if (values.images?.length > 0) {
        if (editData) {
          const existingImages = values.images.filter(
            (img: any) => typeof img === "string"
          );
          const newImages = values.images.filter(
            (img: any) => img instanceof File
          );
          if (existingImages.length > 0) {
            formData.append("existingImages", JSON.stringify(existingImages));
          }
          newImages.forEach((file) => {
            if (
              file !== null &&
              file !== undefined &&
              typeof file === "object" &&
              (file as object) instanceof File
            ) {
              formData.append("images", file);
            }
          });
        } else {
          values.images
            .filter((img: any) => img instanceof File)
            .forEach((file: string | File) => {
              if (file instanceof File) {
                formData.append("images", file);
              }
            });
        }
      }

      // Log FormData for debugging
      console.log("FormData contents:");
      for (const [key, value] of formData.entries()) {
        console.log(`FormData ${key}:`, value);
      }

      try {
        if (editData && editData._id) {
          await updateRoom({ _id: editData._id, roomData: formData });
        } else {
          await createRoom(formData);
        }
        onClose();
      } catch (error) {
        console.error("Submission error:", error);
      }
    },
  });

  const { handleSubmit, getFieldProps } = formik;

  return (
    <HeadingPopup
      onClose={onClose}
      className="w-full max-w-screen-lg h-[80vh]"
      heading={editData ? "Edit Room" : "Add Room"}
    >
      <FormikProvider value={formik}>
        <Form onSubmit={handleSubmit}>
          <div className={` grid grid-cols-3 gap-x-4 gap-y-3 `}>
            <GlobalForm
              formDatails={RoomFromData}
              getFieldProps={getFieldProps}
            />
          </div>
          <ActionButton onCancel={onClose} onSubmit={handleSubmit} />
        </Form>
      </FormikProvider>
    </HeadingPopup>
  );
};

export default RoomListForm;
