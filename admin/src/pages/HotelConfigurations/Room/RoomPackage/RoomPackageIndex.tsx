import { Icon } from "@iconify/react/dist/iconify.js";
import { useEffect, useState } from "react";
import {
  useDeletePackage,
  useGetAllPackages,
  useUpdatePackage,
} from "../../../../server-action/API/HotelConfiguration/roompackage";
import { TableAction } from "../../../../layouts/Table/TableAction";
import Header from "../../../../components/Header";
import MasterTable from "../../../../layouts/Table/MasterTable";
import RoomPackageForm from "./components/RoomPackageForm";
import RoomPackageDetails from "./components/RoomPackageDetails";
import { Status } from "../../../../components/Status";
import { DateForamter } from "../../../../components/DateFormater";

const RoomPackageIndex = () => {
  const [showPopup, setShowPopup] = useState(false);
  const [showPackageDetails, setShowPackageDetails] = useState(false);
  const [singleRoomData, setSingleRoomData] = useState(null);
  const { data: allRoomPakages } = useGetAllPackages();
  const [editData, setEditData] = useState(null);
  const { mutateAsync: deleteRoomPackage } = useDeletePackage();
  const { mutateAsync: updateRoomPackage } = useUpdatePackage();

  // Prevent background scrolling when either form or details is open
  useEffect(() => {
    if (showPopup || showPackageDetails) {
      document.body.style.overflow = "hidden";
      document.body.style.height = "100vh";
    } else {
      document.body.style.overflow = "auto";
      document.body.style.height = "auto";
    }

    // Cleanup on component unmount or when states change
    return () => {
      document.body.style.overflow = "auto";
      document.body.style.height = "auto";
    };
  }, [showPopup, showPackageDetails]);

  const tableData = {
    columns: [
      { title: "Package Name", key: "name" },
      { title: "Price ", key: "price" },
      { title: "Price Type", key: "priceType" },
      { title: "Max Guest", key: "maxGuests" },
      { title: "Min Guest", key: "minGuests" },
      { title: "Start Date", key: "startDate" },
      { title: "End Date", key: "endDate" },
      { title: "Status", key: "status" },
      { title: "Actions", key: "action" },
    ],
    rows: allRoomPakages?.map((roomPackage: any, index: any) => ({
      sn: index + 1,
      name: roomPackage?.name,
      price: roomPackage?.price,
      priceType: roomPackage?.priceType,
      maxGuests: roomPackage?.maxGuests,
      minGuests: roomPackage?.minGuests,
      startDate: roomPackage?.startDate
        ? DateForamter(roomPackage?.startDate)
        : "N/A",
      endDate: roomPackage?.endDate
        ? DateForamter(roomPackage?.endDate)
        : "N/A",
      status: <Status status={roomPackage?.isActive} />,
      action: (
        <TableAction
          onSwitch={() => {
            updateRoomPackage({
              _id: roomPackage?._id,
              packageData: {
                ...roomPackage,
                isActive: !roomPackage?.isActive,
              },
            });
          }}
          switchStatus={roomPackage?.isActive}
          onShow={() => {
            setSingleRoomData(roomPackage);
            setShowPackageDetails(true);
          }}
          onEdit={() => {
            setEditData(roomPackage);
            setShowPopup(true);
          }}
          onDelete={() => {
            deleteRoomPackage(roomPackage._id);
          }}
        />
      ),
    })),
  };

  return (
    <div>
      <div className="flex items-center justify-between">
        <Header showButton={false} />
        <div>
          <button
            className="flex items-center gap-2 bg-[#2E4476] rounded-md px-3 py-1 text-lg text-white "
            onClick={() => {
              setShowPopup(true);
            }}
          >
            <Icon icon="ic:baseline-plus" width={20} height={20} />
            <span>Room Package</span>
          </button>
        </div>
      </div>

      <div className="bg-white">
        <MasterTable
          columns={tableData?.columns}
          rows={tableData?.rows || []}
          loading={false}
        />
      </div>
      {showPopup && (
        <RoomPackageForm
          onClose={() => setShowPopup(false)}
          editData={editData}
        />
      )}
      {showPackageDetails && (
        <RoomPackageDetails
          onClose={() => setShowPackageDetails(false)}
          data={singleRoomData}
        />
      )}
    </div>
  );
};

export default RoomPackageIndex;
