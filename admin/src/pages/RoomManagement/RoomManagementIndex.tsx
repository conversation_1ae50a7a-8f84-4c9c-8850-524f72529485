import { useState } from "react";
import Header from "../../components/Header";
import MasterTable from "../../layouts/Table/MasterTable";
import { useGetAllRooms } from "../../server-action/API/HotelConfiguration/room";
import { PopupModal } from "../../components";
import { BillDetails } from "./component/BillDetails";
import { get } from "lodash";
import PaymentForm from "./component/PaymentForm";
import { Status } from "../../components/Status";

const RoomManagementIndex = () => {
  const { data: roomData, isLoading } = useGetAllRooms();
  const [showModal, setShowModal] = useState(false);
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const [selectedItem, setSelectedItem] = useState<any>(null);

  console.log(roomData, "room");

  const tableData = {
    column: [
      { title: "Room Category", key: "roomType" },
      { title: "Bed Capacity", key: "bed" },
      { title: "Room No.", key: "Room" },
      { title: "Price (NRS)", key: "price" },
      { title: "Facilities", key: "facility" },
      { title: "Room Status", key: "roomStatus" },
      // { title: "Action", key: "action" },
    ],
    rows:
      roomData?.map((item: any) => ({
        id: get(item, "_id", ""),
        key: get(item, "_id", ""),
        roomType: get(item, "roomType.name", ""),
        bed: get(item, "beds.count", 0),
        Room: get(item, "roomNo", ""),
        price: get(item, "currentPrice", 0),
        facility: get(item, "amenities"),
        status: get(item, "status", ""),
        roomStatus: <Status status={item?.status} />,
        action: (
          <div>
            {get(item, "status", "") === "occupied" ? (
              <div
                className="border py-2 border-[#000000] hover:bg-[#2A3A6D] hover:text-white rounded px-3 hover:border-none w-full flex place-self-center text-center cursor-pointer"
                onClick={() => {
                  setSelectedItem(item);
                  setShowModal(true);
                }}
              >
                Checkout
              </div>
            ) : (
              <div className="w-full h-[40px]"></div>
            )}
          </div>
        ),
      })) ?? [],
  };

  const filterConfig = [
    {
      label: "Status",
      key: "status",
      type: "select",
      options: [
        { value: "available", label: "Available" },
        { value: "occupied", label: "Occupied" },
        { value: "reserved", label: "Reserved" },
        { value: "maintenance", label: "Maintenance" },
        { value: "cleaning", label: "Cleaning" },
      ],
    },
  ];

  // function setFilters(newFilters: { search: string; status: string | null }) {
  //   setFiltersState((prevFilters) => ({
  //     ...prevFilters,
  //     ...newFilters,
  //   }));
  // }

  return (
    <div>
      <Header showButton={false} />
      <MasterTable
        columns={tableData.column}
        rows={tableData.rows ?? []}
        loading={isLoading}
        canSearch={true}
        showFilter={true}
        filterConfig={filterConfig}
        sortBy="roomNo"
        sortOrder="asc"
      />
      {showModal && (
        <PopupModal
          onClose={() => {
            setShowModal(false);
            setSelectedItem(null);
          }}
          classname=""
        >
          <BillDetails
            roomData={selectedItem}
            setShowBillModal={setShowModal}
            setShowPaymentModal={setShowPaymentModal}
          />
        </PopupModal>
      )}
      {showPaymentModal && (
        <PopupModal onClose={() => setShowPaymentModal(false)}>
          <PaymentForm onClose={() => setShowPaymentModal(false)} />
        </PopupModal>
      )}
    </div>
  );
};

export default RoomManagementIndex;
