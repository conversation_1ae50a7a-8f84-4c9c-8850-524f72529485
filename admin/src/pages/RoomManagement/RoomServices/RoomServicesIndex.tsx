import { useCallback, useEffect, useState } from "react";
import MasterTable from "../../../layouts/Table/MasterTable";
import {
  useDeleteService,
  useGetAllService,
  useUpdateService,
} from "../../../server-action/API/HotelConfiguration/services";
import RoomServicesForm from "./Component/RoomServicesForm";
import { TableAction } from "../../../layouts/Table/TableAction";
import { get } from "lodash";
import Header from "../../../components/Header";
import { Status } from "../../../components/Status";

function RoomServicesIndex() {
  const [popup, setPopup] = useState(false);
  const [editData, setEditData] = useState(null);
  const togglePopup = useCallback(() => setPopup((prev) => !prev), []);

  const { data: allServicesData, isLoading } = useGetAllService();
  const { mutateAsync: updateRoomService } = useUpdateService();
  const { mutateAsync } = useDeleteService();

  const tableData = {
    column: [
      { title: "SN", key: "sn" },
      { title: "Service Name", key: "name" },
      { title: "Images", key: "images" },
      { title: "Description", key: "description" },
      { title: "Price", key: "price" },
      { title: "Tax Rate", key: "taxRate" },
      { title: "Status", key: "isActive" },
      { title: "Action", key: "action" },
    ],
    rows:
      allServicesData?.map((service: any, index: number) => ({
        sn: index + 1,
        name: service?.name,
        description: service?.description ?? "-",
        price: service?.price ?? "-",
        taxRate: service?.taxRate ?? "-",
        isActive: <Status status={service?.isActive} />,
        images: (
          <>
            <div className=" gap-2">
              {get(service, "images", [])[0] && (
                <div className="relative w-24 h-24 rounded-lg overflow-hidden shadow-lg transform transition duration-300 hover:scale-105">
                  <img
                    src={`https://hotel-api.webstudiomatrix.com/${
                      get(service, "images", [])[0]
                    }`}
                    alt="Lost Item"
                    className="w-full h-full object-cover border-2 border-gray-300 rounded-lg"
                  />
                </div>
              )}
            </div>
          </>
        ),
        action: (
          <TableAction
            onSwitch={() => {
              updateRoomService({
                id: service?._id,
                body: {
                  ...service,
                  isActive: !service?.isActive,
                },
              });
            }}
            switchStatus={service?.isActive}
            onEdit={() => {
              setEditData(service);
              setPopup(true);
            }}
            onDelete={() => {
              mutateAsync(service?._id);
            }}
          />
        ),
      })) || [],
  };

  // Prevent background scrolling when form is open
  useEffect(() => {
    if (popup) {
      document.body.style.overflow = "hidden";
      document.body.style.height = "100vh";
    } else {
      document.body.style.overflow = "auto";
      document.body.style.height = "auto";
    }

    // Cleanup on component unmount or when popup changes
    return () => {
      document.body.style.overflow = "auto";
      document.body.style.height = "auto";
    };
  }, [popup]);

  return (
    <div>
      <Header
        title="Room Services"
        onAddClick={() => {
          setEditData(null);
          togglePopup();
        }}
      />

      <div className="">
        <MasterTable
          loading={isLoading}
          columns={tableData?.column}
          rows={(tableData?.rows as any) || []}
        />
        {popup && (
          <RoomServicesForm onClose={togglePopup} editData={editData} />
        )}
      </div>
    </div>
  );
}

export default RoomServicesIndex;
