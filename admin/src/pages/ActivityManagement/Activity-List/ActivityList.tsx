import { useState } from "react";
import Header from "../../../components/Header";
import { TableHeaderFilter } from "../../../components/TableHeaderFilter";
import { AddActivity } from "./components/AddActivity";
import MasterTable from "../../../layouts/Table/MasterTable";
import {
  useDeleteActivity,
  useGetAllActivities,
  useUpdateActivity,
} from "../../../server-action/API/activity";
import { TableAction } from "../../../layouts/Table/TableAction";
import { IActivity } from "../../../Interface/activities.interface";
import { DeleteDialog } from "../../../components";
import { Card } from "../../../components/Card";
import ActivityFilter from "./components/ActivityFilter";

export const ActivityList = () => {
  const [openModal, setOpenModal] = useState(false);
  const [deleteModal, setDeleteModal] = useState(false);
  const [filter, setFilter] = useState({
    activity: "",
    price: "",
    availability: "",
    search: "",
  });

  const { data: activityData, isSuccess, isLoading } = useGetAllActivities();
  const prices = activityData?.map((act) => Number(act.price));

  const activitiesOptions =
    activityData?.map((act) => ({
      label: act.name,
      value: act._id,
    })) || [];

  const priceOptions = isSuccess
    ? prices
        ?.filter((value, index, self) => self.indexOf(value) === index)
        ?.map((act) => ({
          label: String(act),
          value: String(act),
        })) || []
    : [];

  const availabilityOptions = [
    { label: "Available", value: "true" },
    { label: "Booked", value: "false" },
  ];

  const [selectedData, setSelectedData] = useState<IActivity | null>(null);

  const { mutateAsync: deleteActivity } = useDeleteActivity();
  const { mutateAsync: updateActivity } = useUpdateActivity();

  const handleDelete = async () => {
    await deleteActivity(selectedData?._id ?? "");
    setDeleteModal(false);
  };

  const handleFilterChange = (filters: {
    activity: string;
    price: string;
    availability: string;
  }) => {
    setFilter((prev) => ({ ...prev, ...filters }));
  };

  const handleSearch = (searchTerm: string) => {
    setFilter((prev) => ({ ...prev, search: searchTerm }));
  };

  const tableData = {
    column: [
      {
        key: "name",
        title: "Activity Name",
      },
      {
        key: "date",
        title: "Date",
      },
      {
        key: "time",
        title: "Time",
      },
      {
        key: "max",
        title: "Max Capacity",
      },
      {
        key: "price",
        title: "Price",
      },
      {
        key: "status",
        title: "Status",
      },
      {
        key: "availability",
        title: "Availability",
      },
      {
        key: "action",
        title: "Action",
      },
    ],
    row: activityData
      ?.filter((i) => {
        const matchesActivity = filter.activity
          ? i._id === filter.activity
          : true;
        const matchesPrice = filter.price
          ? i.price === Number(filter.price)
          : true;
        const matchesAvailability = filter.availability
          ? i.availability === (filter.availability === "true")
          : true;
        const matchesSearch = filter.search
          ? i.name.toLowerCase().includes(filter.search.toLowerCase())
          : true;
        return (
          matchesActivity &&
          matchesPrice &&
          matchesAvailability &&
          matchesSearch
        );
      })
      ?.map((item) => ({
        id: item?._id,
        key: item?._id,
        date: item?.days?.map((sub) => sub + ""),
        name: item?.name,
        time: item?.time,
        max: item?.maxCapacity,
        price: item?.price,

        status: (
          <TableAction
            switchStatus={item?.isActive}
            onApiSwitch={() => {
              updateActivity({
                id: item._id,
                body: {
                  ...item,
                  isActive: !item.isActive,
                  availability: !item.isActive,
                },
              });
            }}
          />
        ),
        availability: (
          <div className="flex justify-center">
            <div
              className={`${
                item?.availability
                  ? "py-1 px-3 w-fit border rounded-md border-green text-green"
                  : "py-1 px-3 w-fit border rounded-md border-red text-red"
              }`}
            >
              {item?.availability ? "Available" : "Booked"}
            </div>
          </div>
        ),
        action: (
          <TableAction
            onDelete={() => {
              setDeleteModal(true);
              setSelectedData(item);
            }}
            onEdit={() => {
              setOpenModal(true);
              setSelectedData(item);
            }}
          />
        ),
      })),
  };

  return (
    <div className="flex flex-col">
      <Header
        title="Activity"
        onAddClick={() => {
          setOpenModal(true);
          setSelectedData(null);
        }}
      />

      <div className="p-4 border bg-white rounded-md mb-2">
        <ActivityFilter
          onSearch={handleSearch}
          onFilterChange={handleFilterChange}
          activityOptions={activitiesOptions}
          priceOptions={priceOptions}
          availabilityOptions={availabilityOptions}
        />
      </div>

      <TableHeaderFilter />
      {openModal && (
        <AddActivity
          onClose={() => setOpenModal(false)}
          editData={selectedData}
        />
      )}

      {deleteModal && (
        <DeleteDialog
          confirmAction={true}
          onClose={() => setDeleteModal(false)}
          onConfirm={handleDelete}
        />
      )}

      <MasterTable
        rows={tableData.row ?? []}
        columns={tableData.column}
        loading={isLoading}
        canSearch={false}
      />
    </div>
  );
};
