import React, { useEffect, useState } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import { isSetupComplete } from "../utils/setupUtils";
import { toast } from "react-toastify";

interface SetupRedirectProps {
  children: React.ReactNode;
}

const SetupRedirect: React.FC<SetupRedirectProps> = ({ children }) => {
  const navigate = useNavigate();
  const location = useLocation();
  const [loading, setLoading] = useState(true);
  const [setupDone, setSetupDone] = useState(true);

  // TEMPORARY: Set this to true to bypass setup check during development
  const BYPASS_SETUP_CHECK = false; // Set to false to enable setup check

  useEffect(() => {
    const checkSetup = async () => {
      try {
        // Skip setup check if we're already on the setup page or login page
        if (
          location.pathname === "/hotel-config/setup" ||
          location.pathname === "/login" ||
          location.pathname === "/auth-login" ||
          BYPASS_SETUP_CHECK // Skip if bypass is enabled
        ) {
          setLoading(false);
          return;
        }

        console.log("🔍 SetupRedirect: Checking if setup is complete...");
        const setupComplete = await isSetupComplete();
        console.log("🔍 SetupRedirect: Setup complete check result:", setupComplete);
        setSetupDone(setupComplete);

        if (!setupComplete) {
          // Redirect to setup page if setup is not complete
          console.log("🔄 SetupRedirect: Setup not complete, redirecting to setup page");
          toast.info(
            "Hotel setup is not complete. Redirecting to setup page...",
            {
              position: "top-center",
              autoClose: 3000,
              hideProgressBar: false,
              closeOnClick: true,
              pauseOnHover: true,
              draggable: true,
            }
          );
          navigate("/hotel-config/setup", { replace: true });
        } else {
          console.log("✅ SetupRedirect: Setup is complete, allowing access to main app");
        }

        setLoading(false);
      } catch (error) {
        console.error("Error checking setup status:", error);
        setLoading(false);
      }
    };

    checkSetup();
  }, [location.pathname, navigate]);

  if (loading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#2A3A6D]"></div>
      </div>
    );
  }

  return <>{children}</>;
};

export default SetupRedirect;
