import React, { useState, useRef, useEffect } from "react";
import { FormikProps } from "formik";
import { ChevronLeft, ChevronRight, Calendar } from "lucide-react";
import ReactDOM from "react-dom";

interface CustomDatePickerProps {
  label: string;
  required?: boolean;
  name: string;
  formik: FormikProps<any>;
  value?: string;
  disabled?: boolean;
  onChange?: (e: React.ChangeEvent<any>) => void;
  modalContainer?: HTMLElement; // Modal container to render the portal into
  [key: string]: any;
}

export const CustomDatePicker: React.FC<CustomDatePickerProps> = ({
  label,
  required = false,
  name,
  formik,
  value,
  disabled,
  onChange,
  modalContainer,
  ...rest
}) => {
  const [isOpen, setIsOpen] = useState<boolean>(false);
  const [currentMonth, setCurrentMonth] = useState<Date>(new Date());
  const [calendarPosition, setCalendarPosition] = useState({ top: 0, left: 0 });
  const containerRef = useRef<HTMLDivElement>(null);
  const calendarRef = useRef<HTMLDivElement>(null);

  const daysInMonth = (year: number, month: number): number =>
    new Date(year, month + 1, 0).getDate();
  const firstDayOfMonth = (year: number, month: number): number =>
    new Date(year, month, 1).getDay();

  const handleChange = (e: React.ChangeEvent<any>) => {
    formik.handleChange(e);
    if (onChange) {
      onChange(e);
    }
  };

  const formatDate = (date: Date): string => {
    if (!date) return "";
    const day = String(date.getDate()).padStart(2, "0");
    const month = String(date.getMonth() + 1).padStart(2, "0");
    const year = date.getFullYear();
    return `${year}-${month}-${day}`;
  };

  const formatDisplayDate = (dateString: string): string => {
    if (!dateString) return "";
    const date = new Date(dateString);
    const day = String(date.getDate()).padStart(2, "0");
    const month = String(date.getMonth() + 1).padStart(2, "0");
    const year = date.getFullYear();
    return `${year}-${month}-${day}`;
  };

  const handleDateClick = (day: number) => {
    const year = currentMonth.getFullYear();
    const month = currentMonth.getMonth();
    const selectedDate = new Date(year, month, day);
    const formattedDate = formatDate(selectedDate);

    formik.setFieldValue(name, formattedDate);

    if (onChange) {
      const syntheticEvent = {
        target: { name, value: formattedDate },
      } as React.ChangeEvent<HTMLInputElement>;
      onChange(syntheticEvent);
    }

    setIsOpen(false);
  };

  const previousMonth = () => {
    setCurrentMonth(
      new Date(currentMonth.getFullYear(), currentMonth.getMonth() - 1, 1)
    );
  };

  const nextMonth = () => {
    setCurrentMonth(
      new Date(currentMonth.getFullYear(), currentMonth.getMonth() + 1, 1)
    );
  };

  // Update calendar position relative to the select field
  const updateCalendarPosition = () => {
    if (!containerRef.current || !isOpen) return;

    const rect = containerRef.current.getBoundingClientRect();
    const modalRect = modalContainer?.getBoundingClientRect() || {
      top: 0,
      left: 0,
    };

    // Calculate position relative to the modal container
    setCalendarPosition({
      top:
        rect.bottom -
        modalRect.top +
        (modalContainer?.scrollTop || window.scrollY),
      left:
        rect.left -
        modalRect.left +
        (modalContainer?.scrollLeft || window.scrollX),
    });
  };

  // Update position on open, scroll, and resize
  useEffect(() => {
    if (isOpen) {
      updateCalendarPosition();

      const handleScroll = () => {
        updateCalendarPosition();
      };

      const target = modalContainer || window;
      target.addEventListener("scroll", handleScroll);
      window.addEventListener("resize", updateCalendarPosition);

      return () => {
        target.removeEventListener("scroll", handleScroll);
        window.removeEventListener("resize", updateCalendarPosition);
      };
    }
  }, [isOpen, modalContainer]);

  // Close the date picker when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        containerRef.current &&
        !containerRef.current.contains(event.target as Node) &&
        (!calendarRef.current ||
          !calendarRef.current.contains(event.target as Node))
      ) {
        setIsOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  const renderCalendar = () => {
    const days = [];
    const totalDays = daysInMonth(
      currentMonth.getFullYear(),
      currentMonth.getMonth()
    );
    const firstDay = firstDayOfMonth(
      currentMonth.getFullYear(),
      currentMonth.getMonth()
    );

    const selectedDate = formik.values[name]
      ? new Date(formik.values[name])
      : null;
    const selectedDay = selectedDate ? selectedDate.getDate() : null;
    const selectedMonth = selectedDate ? selectedDate.getMonth() : null;
    const selectedYear = selectedDate ? selectedDate.getFullYear() : null;

    const isSelectedDate = (day: number): boolean => {
      return (
        selectedDay === day &&
        selectedMonth === currentMonth.getMonth() &&
        selectedYear === currentMonth.getFullYear()
      );
    };

    const isToday = (day: number): boolean => {
      const today = new Date();
      return (
        day === today.getDate() &&
        currentMonth.getMonth() === today.getMonth() &&
        currentMonth.getFullYear() === today.getFullYear()
      );
    };

    for (let i = 0; i < firstDay; i++) {
      const prevMonthDays = daysInMonth(
        currentMonth.getFullYear(),
        currentMonth.getMonth() - 1
      );
      days.push(
        <div
          key={`prev-${i}`}
          className="text-gray-400 p-2 text-center text-xs"
        >
          {prevMonthDays - firstDay + i + 1}
        </div>
      );
    }

    for (let day = 1; day <= totalDays; day++) {
      days.push(
        <button
          key={`current-${day}`}
          className={`h-7 w-7 flex items-center justify-center rounded-full text-xs ${
            isSelectedDate(day)
              ? "bg-[#2A3A6D] text-white"
              : isToday(day)
              ? "border border-[#2A3A6D]"
              : "hover:bg-gray-100"
          }`}
          onClick={() => handleDateClick(day)}
          type="button"
          disabled={disabled}
        >
          {day}
        </button>
      );
    }

    const totalCells = 42;
    const remainingCells = totalCells - days.length;
    for (let i = 1; i <= remainingCells; i++) {
      days.push(
        <div
          key={`next-${i}`}
          className="text-gray-400 p-2 text-center text-xs"
        >
          {i}
        </div>
      );
    }

    return days;
  };

  const monthName = currentMonth.toLocaleString("default", { month: "long" });
  const year = currentMonth.getFullYear();

  const currentDateValue = formik.values[name] || value || "";
  const displayDate = formatDisplayDate(currentDateValue);

  const renderCalendarDropdown = () => {
    if (!isOpen || disabled) return null;

    return ReactDOM.createPortal(
      <div
        ref={calendarRef}
        className="absolute w-60 bg-white rounded-md shadow-lg border border-gray-200"
        style={{
          top: calendarPosition.top,
          left: calendarPosition.left,
          zIndex: 1000,
        }}
      >
        <div className="flex items-center justify-between p-2 border-b">
          <button
            onClick={previousMonth}
            className="p-1 hover:bg-gray-100 rounded-full"
            type="button"
            disabled={disabled}
          >
            <ChevronLeft className="w-4 h-4" />
          </button>
          <div className="font-normal text-[13px]">
            {monthName} {year}
          </div>
          <button
            onClick={nextMonth}
            className="p-1 hover:bg-gray-100 rounded-full"
            type="button"
            disabled={disabled}
          >
            <ChevronRight className="w-4 h-4" />
          </button>
        </div>

        <div className="grid grid-cols-7 text-center py-2 px-1 text-xs text-gray-500">
          {["Su", "Mo", "Tu", "We", "Th", "Fr", "Sa"].map((day) => (
            <div
              key={day}
              className="text-center font-medium text-xs text-gray-500"
            >
              {day}
            </div>
          ))}
        </div>

        <div className="grid grid-cols-7 gap-1 p-2">{renderCalendar()}</div>
      </div>,
      modalContainer || document.body
    );
  };

  return (
    <div className="flex flex-col w-full" ref={containerRef}>
      <label htmlFor={name} className="mb-1 text-sm">
        {label} {required && <span className="text-red-600">*</span>}
      </label>

      <div className="relative">
        <div
          className={`p-2 border rounded-md flex items-center cursor-pointer ${
            formik.touched[name] && formik.errors[name]
              ? "border-red-500"
              : "border-gray-300"
          } ${disabled ? "opacity-50 cursor-not-allowed" : ""}`}
          onClick={() => !disabled && setIsOpen(!isOpen)}
        >
          <Calendar className="w-5 h-5 text-gray-500 mr-2" />
          <input
            type="text"
            id={name}
            name={name}
            className="flex-grow border-none focus:outline-none bg-transparent"
            value={displayDate}
            placeholder="Select Date"
            readOnly
            disabled={disabled}
            onBlur={formik.handleBlur}
          />
          <input
            type="hidden"
            name={name}
            value={currentDateValue}
            onChange={handleChange}
          />
        </div>

        {renderCalendarDropdown()}
      </div>

      {formik.touched[name] && formik.errors[name] && (
        <div className="mt-1 text-xs text-red">{`${formik.errors[name]}`}</div>
      )}
    </div>
  );
};
