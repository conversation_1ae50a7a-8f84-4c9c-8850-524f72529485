import { Field, FieldProps, useFormikContext } from "formik";
import { ReactNode, useEffect, useRef, useState } from "react";
import CustomDatePicker from "./CustomDatePicker";
import CustomSelect from "./CustomSelect";
import { Icon } from "@iconify/react";
import { galleryIcon } from "../svgExports";
import { IMAGE_URL } from "../../constant/constant";

interface Option {
  value: string;
  label: string;
}

interface FieldConfig {
  min?: string;
  required?: boolean;
  type: string;
  field: string;
  label: string;
  options?: Option[];
  placeholder?: string;
  disabled?: boolean;
  readonly?: boolean;
  defaultValue?: string | string[];
  isMultipleFile?: boolean;
  errors?: string;
  display?: boolean;
  accept?: string;
  multiple?: boolean;
}

interface GlobalFormProps {
  formik?: any;
  formDatails: FieldConfig[];
  getFieldProps: (field: string) => FieldProps["field"];
  onValueChange?: (field: string, value: any) => void;
}

const renderField = (
  {
    type,
    field,
    label,
    options,
    placeholder,
    disabled,
    accept,
    multiple,
    required,
  }: FieldConfig,
  getFieldProps: (field: string) => FieldProps["field"],
  onValueChange?: (field: string, value: any) => void,
  errors?: { [key: string]: any },
  touched?: { [key: string]: any }
): ReactNode => {
  const getNestedProp = (
    obj: Record<string, any> | undefined,
    path: string
  ): any => {
    if (!obj) return undefined;
    const parts = path
      .replace(/\[(\d+)\]/g, ".$1")
      .split(".")
      .filter(Boolean);
    return parts.reduce((acc: any, part: string) => acc && acc[part], obj);
  };
  const hasError =
    getNestedProp(errors ?? {}, field) && getNestedProp(touched ?? {}, field);

  const isImage = (file: File | string) =>
    typeof file === "string"
      ? file.match(/\.(jpg|jpeg|png|gif)$/i)
      : file.type.startsWith("image/");

  switch (type) {
    case "select":
      return (
        <div>
          <CustomSelect
            {...getFieldProps(field)}
            id={field}
            options={[...(options || [])]}
            onChange={(value) =>
              getFieldProps(field).onChange({ target: { value, name: field } })
            }
            placeholder={placeholder}
            className={`w-full ${
              hasError ? "border-red-500" : "border-gray-300"
            }`}
            disabled={disabled}
          />
          {hasError && (
            <p className="mt-[2px] text-xs text-red">
              {getNestedProp(errors, field)}
            </p>
          )}
        </div>
      );
    case "radio":
      return (
        <div>
          <div className="flex flex-col gap-2">
            {options?.map(({ value, label }) => (
              <label key={value} className="flex items-center gap-2">
                <input
                  {...getFieldProps(field)}
                  type="radio"
                  value={value}
                  className={`w-4 h-4 text-blue-600 border-gray-300 focus:ring-blue-500 ${
                    hasError ? "border-red-500" : ""
                  }`}
                  disabled={disabled}
                />
                <span className="text-sm text-gray-700">{label}</span>
              </label>
            ))}
          </div>
          {hasError && (
            <p className="mt-[2px] text-xs text-red">
              {getNestedProp(errors, field)}
            </p>
          )}
        </div>
      );

    case "multi-value":
      return (
        <div>
          <Field name={field}>
            {({ field: formikField, form }: FieldProps) => (
              <div>
                <div
                  className={`flex flex-wrap gap-2 p-2 border focus:outline-none focus:border-gray-400 ${
                    hasError ? "border-red-500" : "border-gray-300"
                  } rounded-lg  min-h-12`}
                >
                  {(formikField.value || [])?.map(
                    (value: string, index: number) => (
                      <div
                        key={index}
                        className="flex items-center gap-1 px-2 py-1 bg-blue-100 text-blue-800 rounded-md"
                      >
                        {value}
                        <button
                          type="button"
                          className="text-blue-600 hover:text-blue-800"
                          onClick={() => {
                            const newValues = (formikField.value || []).filter(
                              (_: any, i: number) => i !== index
                            );
                            form.setFieldValue(field, newValues);
                            onValueChange?.(field, newValues);
                          }}
                          disabled={disabled}
                        >
                          ×
                        </button>
                      </div>
                    )
                  )}
                  <input
                    className="flex-1 min-w-[150px] bg-transparent  focus:outline-none border-gray-400  text-sm"
                    placeholder={placeholder || "Type and press Enter to add"}
                    onKeyDown={(e: React.KeyboardEvent<HTMLInputElement>) => {
                      if (
                        e.key === "Enter" ||
                        (e.key === " " && e.currentTarget.value.trim())
                      ) {
                        e.preventDefault();
                        const newValues = [
                          ...(formikField.value || []),
                          e.currentTarget.value.trim(),
                        ];
                        form.setFieldValue(field, newValues);
                        onValueChange?.(field, newValues);
                        e.currentTarget.value = "";
                      }
                    }}
                    disabled={disabled}
                  />
                </div>
                <select
                  multiple
                  className="hidden"
                  name={field}
                  value={formikField.value || []}
                  onChange={() => {}}
                >
                  {(formikField.value || []).map(
                    (value: string, index: number) => (
                      <option key={index} value={value}>
                        {value}
                      </option>
                    )
                  )}
                </select>
              </div>
            )}
          </Field>
          {hasError && (
            <p className="mt-[2px] text-xs text-red">
              {getNestedProp(errors, field)}
            </p>
          )}
        </div>
      );

    case "checkbox":
      return (
        <div>
          <div className="flex flex-wrap gap-2">
            {options?.map(({ value, label }) => (
              <label key={value} className="flex items-center gap-2">
                <input
                  {...getFieldProps(field)}
                  type="checkbox"
                  value={value}
                  className={`w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500 ${
                    hasError ? "border-red-500" : ""
                  }`}
                  disabled={disabled}
                />
                <span className="text-sm text-gray-700">{label}</span>
              </label>
            ))}
          </div>
          {hasError && (
            <p className="mt-[2px] text-xs text-red">
              {getNestedProp(errors, field)}
            </p>
          )}
        </div>
      );

    case "textarea":
      return (
        <div>
          <Field
            as="textarea"
            className={`w-full h-24 px-2 py-1 placeholder-gray-400 border focus:outline-none focus:border-gray-400 ${
              hasError ? "border-red-500" : "border-gray-300"
            } rounded-md resize-none focus:outline-none`}
            placeholder={placeholder || label}
            {...getFieldProps(field)}
            disabled={disabled}
          />
          {hasError && (
            <p className="mt-[2px] text-xs text-red">
              {getNestedProp(errors, field)}
            </p>
          )}
        </div>
      );

    case "toggle":
      return (
        <div>
          <Field name={field}>
            {({ field: formikField, form }: FieldProps) => (
              <div className="flex items-center">
                <label className="relative inline-flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    checked={formikField.value || false}
                    onChange={() => {
                      const newValue = !formikField.value;
                      form.setFieldValue(field, newValue);
                      onValueChange?.(field, newValue);
                    }}
                    className="sr-only peer"
                    disabled={disabled}
                  />
                  <div
                    className={`w-11 h-6 rounded-full peer ${
                      formikField.value ? "bg-gray-300" : "bg-gray-200"
                    } peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-blue-300
                    peer-checked:after:translate-x-full peer-checked:after:border-white
                    after:content-[''] after:absolute after:top-[2px] after:left-[2px]
                    after:bg-secondary after:border-yellow after:border after:rounded-full
                    after:h-5 after:w-5 after:transition-all`}
                  ></div>
                </label>
              </div>
            )}
          </Field>
          {hasError && (
            <p className="mt-[2px] text-xs text-red">
              {getNestedProp(errors, field)}
            </p>
          )}
        </div>
      );

    case "multiple":
      return (
        <div>
          <Field name={field}>
            {({ field: formikField, form }: FieldProps) => {
              const [isOpen, setIsOpen] = useState<boolean>(false);
              const [searchTerm, setSearchTerm] = useState<string>("");
              const dropdownRef = useRef<HTMLDivElement>(null);

              useEffect(() => {
                const handleClickOutside = (event: MouseEvent) => {
                  if (
                    dropdownRef.current &&
                    !dropdownRef.current.contains(event.target as Node)
                  ) {
                    setIsOpen(false);
                  }
                };
                document.addEventListener("mousedown", handleClickOutside);
                return () =>
                  document.removeEventListener("mousedown", handleClickOutside);
              }, []);

              return (
                <div className="relative" ref={dropdownRef}>
                  <div
                    className={`flex flex-wrap w-full gap-1 p-2 text-sm border ${
                      hasError ? "border-red-500" : "border-gray-300"
                    } rounded-lg cursor-pointer min-h-12 bg-gray-50`}
                    onClick={() => setIsOpen(!isOpen)}
                  >
                    {(formikField.value || []).length === 0 ? (
                      <span className="py-1 text-gray-400">
                        Select options...
                      </span>
                    ) : (
                      (formikField.value || []).map((value: string) => {
                        const option = options?.find(
                          (opt) => opt.value === value
                        );
                        return option ? (
                          <span
                            key={value}
                            className="flex items-center gap-1 px-2 py-1 text-blue-800 bg-blue-100 rounded-md"
                          >
                            {option.label}
                            <button
                              type="button"
                              className="text-blue-600 hover:text-blue-800"
                              onClick={(e) => {
                                e.stopPropagation();
                                const newValues = (
                                  formikField.value || []
                                ).filter((val: string) => val !== value);
                                form.setFieldValue(field, newValues);
                                onValueChange?.(field, newValues);
                              }}
                              disabled={disabled}
                            >
                              ×
                            </button>
                          </span>
                        ) : null;
                      })
                    )}
                  </div>
                  {isOpen && (
                    <div className="absolute z-10 w-full mt-[2px] overflow-auto bg-white border border-gray-300 rounded-lg shadow-lg max-h-60">
                      <div className="p-2 border-b border-gray-200">
                        <input
                          type="text"
                          className="w-full p-2 border border-gray-300 rounded-md "
                          placeholder="Search options..."
                          value={searchTerm}
                          onChange={(e) => setSearchTerm(e.target.value)}
                          onClick={(e) => e.stopPropagation()}
                          disabled={disabled}
                        />
                      </div>
                      <div className="p-1">
                        {(
                          options?.filter((option) =>
                            option.label
                              .toLowerCase()
                              .includes(searchTerm.toLowerCase())
                          ) || []
                        ).length === 0 ? (
                          <div className="p-2 text-center text-gray-500">
                            No options found
                          </div>
                        ) : (
                          options
                            ?.filter((option) =>
                              option.label
                                .toLowerCase()
                                .includes(searchTerm.toLowerCase())
                            )
                            .map(({ value, label }) => (
                              <div
                                key={value}
                                className="flex items-center p-2 rounded cursor-pointer hover:bg-gray-100"
                                onClick={() => {
                                  const newValues = (
                                    formikField.value || []
                                  ).includes(value)
                                    ? (formikField.value || []).filter(
                                        (val: string) => val !== value
                                      )
                                    : [...(formikField.value || []), value];
                                  form.setFieldValue(field, newValues);
                                  onValueChange?.(field, newValues);
                                }}
                              >
                                <input
                                  type="checkbox"
                                  className="w-4 h-4 mr-2 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                                  checked={(formikField.value || []).includes(
                                    value
                                  )}
                                  onChange={() => {}}
                                  disabled={disabled}
                                />
                                <span>{label}</span>
                              </div>
                            ))
                        )}
                      </div>
                    </div>
                  )}
                  <select
                    multiple
                    className="hidden"
                    name={field}
                    value={formikField.value || []}
                    onChange={() => {}}
                  >
                    {options?.map(({ value, label }) => (
                      <option key={value} value={value}>
                        {label}
                      </option>
                    ))}
                  </select>
                </div>
              );
            }}
          </Field>
          {hasError && (
            <p className="mt-[2px] text-xs text-red">
              {getNestedProp(errors, field)}
            </p>
          )}
        </div>
      );

    case "file":
      return (
        <div>
          <Field name={field}>
            {({ field: formikField, form }: FieldProps) => (
              <div>
                <label
                  htmlFor={field}
                  className={`flex flex-col items-center justify-center w-full h-24 border-2 border-dashed rounded-lg cursor-pointer bg-gray-50 hover:bg-gray-100 ${
                    hasError ? "border-red-500" : "border-gray-300"
                  }`}
                >
                  <div className="flex flex-col items-center justify-center pt-5 pb-6">
                    <svg
                      className="w-8 h-8 mb-2 text-gray-500"
                      aria-hidden="true"
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      viewBox="0 0 20 16"
                    >
                      <path
                        stroke="currentColor"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth="2"
                        d="M13 13h3a3 3 0 0 0 0-6h-.025A5.56 5.56 0 0 0 16 6.5 5.5 5.5 0 0 0 5.207 5.021C5.137 5.017 5.071 5 5 5a4 4 0 0 0 0 8h2.167M10 15V6m0 0L8 8m2-2 2 2"
                      />
                    </svg>
                    <p className="mb-1 text-sm text-gray-500">
                      <span className="font-semibold">Click to upload</span> or
                      drag and drop
                    </p>
                    <p className="text-xs text-gray-500">
                      {formikField.value
                        ? typeof formikField.value === "string"
                          ? formikField.value
                          : formikField.value.name
                        : "Select a file"}
                    </p>
                  </div>
                  <input
                    id={field}
                    type="file"
                    className="hidden"
                    disabled={disabled}
                    accept={accept}
                    onChange={(event) => {
                      const file = event.currentTarget.files?.[0];
                      form.setFieldValue(field, file || null);
                      onValueChange?.(field, file || null);
                    }}
                  />
                </label>
                {formikField.value && (
                  <div className="flex items-center gap-2 mt-2">
                    <span className="max-w-xs text-sm text-gray-700 truncate">
                      {typeof formikField.value === "string"
                        ? formikField.value
                        : formikField.value.name}
                    </span>
                    <button
                      type="button"
                      className="text-red hover:text-red-800"
                      onClick={() => {
                        form.setFieldValue(field, null);
                        onValueChange?.(field, null);
                      }}
                      disabled={disabled}
                    >
                      Remove
                    </button>
                  </div>
                )}
              </div>
            )}
          </Field>
          {hasError && (
            <p className="mt-[2px] text-xs text-red">
              {getNestedProp(errors, field)}
            </p>
          )}
        </div>
      );

    case "files":
      return (
        <div>
          <Field name={field}>
            {({ field: formikField, form }: FieldProps) => (
              <div className="flex flex-col">
                <div>
                  <label className="mb-1 text-sm">
                    {/* {label} */}
                    {required && <span className="text-red-600">*</span>}
                  </label>
                  <div className="flex items-center w-full space-x-2 border-2 rounded-md">
                    <label
                      htmlFor={`file-input-${field}`}
                      className="flex items-center gap-1 p-2 text-white bg-[#163381] cursor-pointer rounded-l-md"
                    >
                      {galleryIcon({ color: "white", size: "18" })}
                      Choose Files
                    </label>
                    <input
                      id={`file-input-${field}`}
                      type="file"
                      accept={accept || "image/*"}
                      multiple={multiple !== false}
                      onChange={(e) => {
                        if (e.target.files && e.target.files.length > 0) {
                          const newFiles = Array.from(e.target.files);
                          const existingImages = Array.isArray(
                            formikField.value
                          )
                            ? formikField.value.filter(
                                (img: any) =>
                                  typeof img === "string" || img instanceof File
                              )
                            : [];
                          const totalImages =
                            existingImages.length + newFiles.length;
                          if (totalImages > 10) {
                            form.setFieldError(
                              field,
                              "You can upload up to 10 images only"
                            );
                            form.setFieldTouched(field, true);
                            return;
                          }
                          const updatedImages = [
                            ...existingImages,
                            ...newFiles,
                          ];
                          form.setFieldValue(field, updatedImages);
                          form.setFieldTouched(field, true);
                          onValueChange?.(field, updatedImages);
                          e.target.value = "";
                        }
                      }}
                      onBlur={() => form.setFieldTouched(field, true)}
                      className="hidden"
                      disabled={disabled}
                    />
                    <span className="text-sm text-gray-500">
                      {Array.isArray(formikField.value)
                        ? formikField.value.length
                        : 0}{" "}
                      files selected
                    </span>
                  </div>
                  {hasError && (
                    <p className="mt-[2px] text-xs text-red">
                      {getNestedProp(errors, field)}
                    </p>
                  )}
                </div>
                {formikField.value?.length > 0 && (
                  <div className="flex flex-wrap p-4 mt-2 border-2 border-gray-300 rounded-md">
                    {formikField.value.map(
                      (file: File | string, index: number) => {
                        if (!isImage(file)) {
                          console.warn(
                            `Invalid image at index ${index}:`,
                            file
                          );
                          return null;
                        }
                        const isString = typeof file === "string";
                        const imageUrl = isString
                          ? `${IMAGE_URL}${file}`
                          : URL.createObjectURL(file);

                        return (
                          <div key={index} className="relative w-24 h-24 m-2">
                            <img
                              src={imageUrl}
                              alt={`preview-${index}`}
                              className="object-cover w-full h-full rounded-md"
                            />
                            <button
                              type="button"
                              className="absolute top-0 right-0 p-1 bg-red-500 rounded-full"
                              onClick={() => {
                                const newFiles = formikField.value.filter(
                                  (_: any, i: number) => i !== index
                                );
                                form.setFieldValue(field, newFiles);
                                form.setFieldTouched(field, true);
                                onValueChange?.(field, newFiles);
                              }}
                              disabled={disabled}
                            >
                              <Icon
                                icon="fluent-mdl2:calculator-multiply"
                                width="16"
                                height="16"
                                className="text-red bg-white rounded-full p-[2px]"
                              />
                            </button>
                          </div>
                        );
                      }
                    )}
                  </div>
                )}
              </div>
            )}
          </Field>
        </div>
      );

    case "number":
      return (
        <div>
          <Field
            className={`w-full h-12 px-2  border ${
              hasError ? "border-red-500" : "border-gray-300"
            } rounded-md focus:outline-none focus:border-gray-400 ${
              disabled ? "bg-gray-100 cursor-not-allowed" : "bg-white"
            }`}
            placeholder={placeholder || label}
            type="number"
            disabled={disabled}
            {...getFieldProps(field)}
          />

          {hasError && (
            <p className="mt-[2px] text-xs text-red">
              {getNestedProp(errors, field)}
            </p>
          )}
        </div>
      );
    case "date":
      return (
        <div>
          <CustomDatePicker
            id={field}
            name={field}
            value={getFieldProps(field).value || ""}
            onChange={(event) => getFieldProps(field).onChange(event)}
            placeholder={placeholder || label}
            disabled={disabled}
            hasError={hasError}
          />
          {hasError && (
            <p className="mt-[2px] text-xs text-red">
              {getNestedProp(errors, field)}
            </p>
          )}
        </div>
      );
    default:
      return (
        <div>
          <Field
            className={`w-full h-12 px-2 placeholder-gray-400 border focus:outline-none focus:border-gray-400 ${
              hasError ? "border-red-500" : "border-gray-300"
            } rounded-md focus:outline-none`}
            placeholder={placeholder || label}
            type={type}
            disabled={disabled}
            {...getFieldProps(field)}
          />
          {hasError && (
            <p className="mt-[2px] text-xs text-red">
              {getNestedProp(errors, field)}
            </p>
          )}
        </div>
      );
  }
};

export const GlobalForm: React.FC<GlobalFormProps> = ({
  formDatails,
  getFieldProps,
  onValueChange,
}) => {
  const { errors, touched } = useFormikContext();

  return formDatails
    ?.filter(({ display = true }) => display)
    ?.map((fieldConfig) => (
      <div key={fieldConfig.field} className="col-span-1">
        <div className="block py-1 mb-2 text-sm font-bold text-gray-700">
          {fieldConfig.label}
          {fieldConfig.required ? <span className="text-red">*</span> : ""}
        </div>
        {renderField(
          fieldConfig,
          getFieldProps,
          onValueChange,
          errors,
          touched
        )}
      </div>
    ));
};
