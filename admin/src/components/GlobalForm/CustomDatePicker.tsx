import React, { useState, useRef, useEffect } from "react";
import ReactDOM from "react-dom";

// Inline utility function for class names
function classNames(
  ...classes: (string | boolean | undefined | null)[]
): string {
  return classes.filter(Boolean).join(" ");
}

// Format date to YYYY-MM-DD string
function formatDateToString(date: Date): string {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, "0");
  const day = String(date.getDate()).padStart(2, "0");
  return `${year}-${month}-${day}`;
}

// Parse YYYY-MM-DD string to Date
function parseStringToDate(dateString: string): Date {
  return new Date(dateString);
}

interface DatePickerProps {
  id?: string;
  name?: string;
  label?: string;
  value?: string;
  onChange?: (event: { target: { value: string; name?: string } }) => void;
  placeholder?: string;
  disabled?: boolean;
  isForm?: boolean;
  hasError?: boolean;
}

export default function CustomDatePicker({
  id,
  name,
  label = "",
  value = "",
  onChange,
  placeholder = "Select a date",
  disabled = false,
  isForm = true,
  hasError = false,
}: DatePickerProps) {
  // Convert the string value to a Date object for internal use
  const [selectedDate, setSelectedDate] = useState<Date | undefined>(
    value ? parseStringToDate(value) : undefined
  );
  const [isOpen, setIsOpen] = useState(false);
  const [currentMonth, setCurrentMonth] = useState<Date>(
    value ? parseStringToDate(value) : new Date()
  );
  const datePickerRef = useRef<HTMLDivElement>(null);
  const calendarRef = useRef<HTMLDivElement>(null);

  // Update internal state when value prop changes
  useEffect(() => {
    if (value) {
      const parsedDate = parseStringToDate(value);
      if (!isNaN(parsedDate.getTime())) {
        setSelectedDate(parsedDate);
        setCurrentMonth(parsedDate);
      } else {
        setSelectedDate(undefined);
      }
    } else {
      setSelectedDate(undefined);
    }
  }, [value]);

  // Close the date picker when clicking outside
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (
        datePickerRef.current &&
        !datePickerRef.current.contains(event.target as Node) &&
        (!calendarRef.current ||
          !calendarRef.current.contains(event.target as Node))
      ) {
        setIsOpen(false);
      }
    }
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  // Get days in month
  const getDaysInMonth = (date: Date) => {
    return new Date(date.getFullYear(), date.getMonth() + 1, 0).getDate();
  };

  // Get day of week for first day of month (0 = Sunday, 1 = Monday, etc.)
  const getFirstDayOfMonth = (date: Date) => {
    return new Date(date.getFullYear(), date.getMonth(), 1).getDay();
  };

  // Get days from previous month to display
  const getPreviousMonthDays = (date: Date) => {
    const firstDayOfMonth = getFirstDayOfMonth(date);
    const previousMonth = new Date(date.getFullYear(), date.getMonth() - 1, 1);
    const daysInPreviousMonth = getDaysInMonth(previousMonth);

    const days = [];
    for (let i = firstDayOfMonth - 1; i >= 0; i--) {
      days.push({
        date: new Date(
          date.getFullYear(),
          date.getMonth() - 1,
          daysInPreviousMonth - i
        ),
        isCurrentMonth: false,
      });
    }
    return days;
  };

  // Get days for current month
  const getCurrentMonthDays = (date: Date) => {
    const daysInMonth = getDaysInMonth(date);
    const days = [];

    for (let i = 1; i <= daysInMonth; i++) {
      days.push({
        date: new Date(date.getFullYear(), date.getMonth(), i),
        isCurrentMonth: true,
      });
    }
    return days;
  };

  // Get days from next month to display
  const getNextMonthDays = (date: Date) => {
    const firstDayOfMonth = getFirstDayOfMonth(date);
    const daysInMonth = getDaysInMonth(date);
    const nextMonthDays = 42 - (firstDayOfMonth + daysInMonth); // 42 = 6 rows of 7 days

    const days = [];
    for (let i = 1; i <= nextMonthDays; i++) {
      days.push({
        date: new Date(date.getFullYear(), date.getMonth() + 1, i),
        isCurrentMonth: false,
      });
    }
    return days;
  };

  // Get all days to display in calendar
  const getAllDays = (date: Date) => {
    const previousMonthDays = getPreviousMonthDays(date);
    const currentMonthDays = getCurrentMonthDays(date);
    const nextMonthDays = getNextMonthDays(date);

    return [...previousMonthDays, ...currentMonthDays, ...nextMonthDays];
  };

  // Format date for display
  const formatDateForDisplay = (date: Date) => {
    return formatDateToString(date); // returns YYYY-MM-DD
  };

  // Navigate to previous month
  const goToPreviousMonth = () => {
    setCurrentMonth(
      new Date(currentMonth.getFullYear(), currentMonth.getMonth() - 1, 1)
    );
  };

  // Navigate to next month
  const goToNextMonth = () => {
    setCurrentMonth(
      new Date(currentMonth.getFullYear(), currentMonth.getMonth() + 1, 1)
    );
  };

  // Handle date selection
  const handleDateSelect = (date: Date) => {
    if (disabled) return;
    setSelectedDate(date);
    if (onChange) {
      onChange({ target: { value: formatDateToString(date), name } });
    }
    setIsOpen(false);
  };

  // Check if a date is the selected date
  const isSelectedDate = (date: Date) => {
    if (!selectedDate) return false;
    return (
      date.getDate() === selectedDate.getDate() &&
      date.getMonth() === selectedDate.getMonth() &&
      date.getFullYear() === selectedDate.getFullYear()
    );
  };

  // Check if a date is today
  const isToday = (date: Date) => {
    const today = new Date();
    return (
      date.getDate() === today.getDate() &&
      date.getMonth() === today.getMonth() &&
      date.getFullYear() === today.getFullYear()
    );
  };

  // Get all calendar days
  const calendarDays = getAllDays(currentMonth);

  // Get month and year for display
  const monthYearString = currentMonth.toLocaleDateString("en-US", {
    month: "long",
    year: "numeric",
  });

  // Days of week headers
  const daysOfWeek = ["Su", "Mo", "Tu", "We", "Th", "Fr", "Sa"];

  // Render calendar dropdown using portal
  const renderCalendarDropdown = () => {
    if (!isOpen || disabled) return null;

    const rect = datePickerRef.current?.getBoundingClientRect();
    if (!rect) return null;

    return ReactDOM.createPortal(
      <div
        ref={calendarRef}
        className="absolute w-60 bg-white rounded-md shadow-lg border border-gray-200"
        style={{
          top: rect.bottom + window.scrollY + 2, // Small offset for spacing
          left: rect.left + window.scrollX,
          zIndex: 1000, // Ensure it appears above the modal
        }}
      >
        {/* Calendar Header */}
        <div className="flex items-center justify-between p-2 border-b">
          <button
            onClick={goToPreviousMonth}
            className="p-1 hover:bg-gray-100 rounded-full"
            type="button"
            disabled={disabled}
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-4 w-4"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M15 19l-7-7 7-7"
              />
            </svg>
          </button>
          <div className="font-normal text-[13px]">{monthYearString}</div>
          <button
            onClick={goToNextMonth}
            className="p-1 hover:bg-gray-100 rounded-full"
            type="button"
            disabled={disabled}
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-4 w-4"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M9 5l7 7-7 7"
              />
            </svg>
          </button>
        </div>

        {/* Days of Week */}
        <div className="grid grid-cols-7 text-center py-2 px-1 text-xs text-gray-500">
          {daysOfWeek.map((day, index) => (
            <div key={index}>{day}</div>
          ))}
        </div>

        {/* Calendar Days */}
        <div className="grid grid-cols-7 gap-1 p-2">
          {calendarDays.map((day, index) => (
            <button
              key={index}
              onClick={() => handleDateSelect(day.date)}
              className={classNames(
                "h-7 w-7 flex items-center justify-center rounded-full text-xs",
                !day.isCurrentMonth && "text-gray-400",
                day.isCurrentMonth &&
                  !isSelectedDate(day.date) &&
                  !isToday(day.date) &&
                  "hover:bg-gray-100",
                isToday(day.date) &&
                  !isSelectedDate(day.date) &&
                  "border border-[#2A3A6D]",
                isSelectedDate(day.date) && "bg-[#2A3A6D] text-white",
                disabled && "cursor-not-allowed"
              )}
              type="button"
              disabled={disabled}
            >
              {day.date.getDate()}
            </button>
          ))}
        </div>
      </div>,
      document.body
    );
  };

  return (
    <div id={id} className="w-full max-w-md" ref={datePickerRef}>
      {label && <div className="text-sm mb-1">{label}</div>}

      <div className="relative">
        {/* Date Input Field */}
        <div
          className={classNames(
            "flex items-center w-full border rounded-md bg-white",
            isForm ? "p-3" : "p-2",
            disabled ? "opacity-50 cursor-not-allowed" : "cursor-pointer",
            hasError ? "border-red-500" : "border-gray-200"
          )}
          onClick={() => !disabled && setIsOpen(!isOpen)}
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-4 w-4 text-gray-600 mr-2"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"
            />
          </svg>
          <span
            className={classNames(
              "flex-grow",
              !selectedDate && "text-gray-600"
            )}
          >
            {selectedDate ? formatDateForDisplay(selectedDate) : placeholder}
          </span>

          {/* Hidden input for form compatibility */}
          <input
            type="hidden"
            value={selectedDate ? formatDateToString(selectedDate) : ""}
            name={name || id}
          />
        </div>

        {renderCalendarDropdown()}
      </div>
    </div>
  );
}
