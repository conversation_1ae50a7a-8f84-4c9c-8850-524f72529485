import React from "react";
import { createPortal } from "react-dom";

interface ModalProps {
  children: React.ReactNode;
  classname?: string;
  onClose: () => void; // Callback to close the modal
  // open: boolean; // Controls the visibility of the modal
}

export const PopupModal = React.memo(
  React.forwardRef<HTMLDivElement, ModalProps>(
    ({ children, classname, onClose }, ref) => {
      // if (!open) return null; // Don't render the modal if `open` is false

      return createPortal(
        <>
          {/* Backdrop */}
          <div
            className="fixed inset-0 bg-black bg-opacity-50 z-50"
            onClick={onClose} // Close modal when clicking outside
          ></div>

          {/* Modal Content */}
          <main
            ref={ref}
            className={`${classname} fixed top-[50%] left-[50%] z-[999] bg-white border outline-none rounded-lg`}
            style={{ transform: "translate(-50%, -50%)" }}
            onClick={(e) => e.stopPropagation()} // Prevent closing when clicking inside
          >
            {children}
          </main>
        </>,
        document.body
      );
    }
  )
);

// Display name for debugging
PopupModal.displayName = "PopupModal";
