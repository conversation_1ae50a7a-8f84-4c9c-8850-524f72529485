import React from "react";

export interface StepperProps {
  currentStep: number;
  steps: { id: number; label: string }[];
  onStepClick: (stepId: number) => void;
}

const Stepper: React.FC<StepperProps> = ({
  currentStep,
  steps,
  onStepClick,
}) => {
  return (
    <div className="flex items-center justify-between w-full">
      {steps.map((step, index) => {
        const isActive = step.id === currentStep;
        const isCompleted = step.id < currentStep;
        const isLastStep = index === steps.length - 1;

        return (
          <React.Fragment key={step.id}>
            {/* Step Circle and Label */}
            <div
              className="flex flex-col items-center"
              style={{ flex: "0 0 auto" }}
            >
              <button
                type="button"
                onClick={() => onStepClick(step.id)}
                className={`flex items-center justify-center w-10 h-10 rounded-full border-2   text-sm font-medium transition-all
                  ${
                    isCompleted
                      ? "border-[#3651CD] bg-[#3651CD] text-white"
                      : isActive
                      ? "bg-[#3651CD] text-white  border-[#3651CD]"
                      : "border-gray-300 text-gray-400"
                  }`}
              >
                {step.id}
              </button>
              <span
                className={`text-sm font-semibold mt-2 text-center whitespace-nowrap
                  ${
                    isCompleted || isActive ? "text-[#3651CD]" : "text-gray-400"
                  }`}
              >
                {step.label}
              </span>
            </div>

            {/* Connecting Line */}
            {!isLastStep && (
              <div
                className={`flex-1 h-0.5 -mt-6 mx-2 transition-all
                  ${step.id < currentStep ? "bg-[#3651CD]" : "bg-gray-300"}`}
              />
            )}
          </React.Fragment>
        );
      })}
    </div>
  );
};

export default Stepper;
